# 软考学习系统 - 后端详细设计方案

## 1. 整体架构设计

### 1.1 微服务架构
```yaml
# 服务架构图
services:
  # 网关服务
  api-gateway:
    port: 8080
    purpose: 统一入口、负载均衡、限流、认证
    
  # 用户服务
  user-service:
    port: 8081
    database: user_db
    purpose: 用户管理、认证授权、权限控制
    
  # 学习服务
  study-service:
    port: 8082
    database: study_db
    purpose: 学习计划、进度跟踪、知识点管理
    
  # 题库服务
  exercise-service:
    port: 8083
    database: exercise_db
    purpose: 题目管理、答题记录、考试功能
    
  # AI服务
  ai-service:
    port: 8084
    database: ai_db
    purpose: 智能推荐、自然语言处理、学习分析
    
  # 分析服务
  analytics-service:
    port: 8085
    database: analytics_db
    purpose: 数据统计、报表生成、用户行为分析
    
  # 通知服务
  notification-service:
    port: 8086
    database: notification_db
    purpose: 消息推送、邮件发送、学习提醒
```

### 1.2 技术栈选择
```json
{
  "framework": "Spring Boot 3.2+",
  "language": "Java 17+",
  "database": {
    "primary": "MySQL 8.0+",
    "cache": "Redis 7.0+",
    "search": "Elasticsearch 8.0+",
    "analytics": "MongoDB 6.0+"
  },
  "messageQueue": "Apache Kafka 3.0+",
  "security": "Spring Security + JWT",
  "api": "RESTful API + GraphQL",
  "monitoring": "Micrometer + Prometheus",
  "logging": "Logback + ELK Stack",
  "deployment": "Docker + Kubernetes",
  "testing": "JUnit 5 + TestContainers"
}
```

### 1.3 项目结构
```
backend/
├── common/                          # 公共模块
│   ├── common-core/                # 核心工具类
│   ├── common-security/            # 安全组件
│   ├── common-database/            # 数据库组件
│   └── common-message/             # 消息组件
├── gateway/                        # API网关
├── services/                       # 微服务
│   ├── user-service/              # 用户服务
│   ├── study-service/             # 学习服务
│   ├── exercise-service/          # 题库服务
│   ├── ai-service/                # AI服务
│   ├── analytics-service/         # 分析服务
│   └── notification-service/      # 通知服务
├── infrastructure/                 # 基础设施
│   ├── config-server/             # 配置中心
│   ├── eureka-server/             # 服务注册中心
│   └── admin-server/              # 监控中心
└── docker/                        # Docker配置
```

## 2. 用户服务设计 (user-service)

### 2.1 实体设计
```java
// User.java
@Entity
@Table(name = "users")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class User {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(unique = true, nullable = false)
    private String username;
    
    @Column(unique = true, nullable = false)
    private String email;
    
    @Column(nullable = false)
    private String password;
    
    @Column(name = "phone_number")
    private String phoneNumber;
    
    @Column(name = "real_name")
    private String realName;
    
    @Enumerated(EnumType.STRING)
    private UserStatus status;
    
    @Column(name = "avatar_url")
    private String avatarUrl;
    
    @Column(name = "last_login_time")
    private LocalDateTime lastLoginTime;
    
    @Column(name = "last_login_ip")
    private String lastLoginIp;
    
    @CreationTimestamp
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    // 学习偏好设置
    @OneToOne(mappedBy = "user", cascade = CascadeType.ALL)
    private UserPreference preference;
    
    // 用户角色关联
    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
        name = "user_roles",
        joinColumns = @JoinColumn(name = "user_id"),
        inverseJoinColumns = @JoinColumn(name = "role_id")
    )
    private Set<Role> roles = new HashSet<>();
}

// UserPreference.java
@Entity
@Table(name = "user_preferences")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserPreference {
    @Id
    private Long userId;
    
    @OneToOne
    @MapsId
    @JoinColumn(name = "user_id")
    private User user;
    
    // 学习目标科目
    @ElementCollection
    @CollectionTable(name = "user_target_subjects")
    private Set<String> targetSubjects = new HashSet<>();
    
    // 每日学习时长目标（分钟）
    @Column(name = "daily_study_goal")
    private Integer dailyStudyGoal = 60;
    
    // 学习提醒设置
    @Column(name = "study_reminder_enabled")
    private Boolean studyReminderEnabled = true;
    
    @Column(name = "reminder_time")
    private LocalTime reminderTime;
    
    // 难度偏好
    @Enumerated(EnumType.STRING)
    @Column(name = "preferred_difficulty")
    private Difficulty preferredDifficulty;
    
    // 学习模式偏好
    @Enumerated(EnumType.STRING)
    @Column(name = "learning_mode")
    private LearningMode learningMode;
    
    @CreationTimestamp
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
}
```

### 2.2 服务层设计
```java
// UserService.java
@Service
@Transactional
@Slf4j
public class UserService {
    
    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;
    private final JwtTokenProvider jwtTokenProvider;
    private final RedisTemplate<String, Object> redisTemplate;
    
    public UserService(
        UserRepository userRepository,
        PasswordEncoder passwordEncoder,
        JwtTokenProvider jwtTokenProvider,
        RedisTemplate<String, Object> redisTemplate
    ) {
        this.userRepository = userRepository;
        this.passwordEncoder = passwordEncoder;
        this.jwtTokenProvider = jwtTokenProvider;
        this.redisTemplate = redisTemplate;
    }
    
    /**
     * 用户注册
     */
    public UserRegistrationResponse register(UserRegistrationRequest request) {
        // 验证用户名和邮箱唯一性
        validateUserUniqueness(request.getUsername(), request.getEmail());
      
        // 创建用户
        User user = User.builder()
            .username(request.getUsername())
            .email(request.getEmail())
            .password(passwordEncoder.encode(request.getPassword()))
            .realName(request.getRealName())
            .phoneNumber(request.getPhoneNumber())
            .status(UserStatus.ACTIVE)
            .build();
      
        // 设置默认角色
        Role defaultRole = roleRepository.findByName("ROLE_USER")
            .orElseThrow(() -> new BusinessException("默认角色不存在"));
        user.getRoles().add(defaultRole);
      
        // 保存用户
        User savedUser = userRepository.save(user);
      
        // 创建默认学习偏好
        createDefaultPreference(savedUser);
      
        // 发送欢迎邮件
        sendWelcomeEmail(savedUser);
      
        return UserRegistrationResponse.from(savedUser);
    }
  
    /**
     * 用户登录
     */
    public LoginResponse login(LoginRequest request) {
        // 查找用户
        User user = userRepository.findByUsernameOrEmail(
            request.getUsername(), request.getUsername()
        ).orElseThrow(() -> new AuthenticationException("用户不存在"));
      
        // 验证密码
        if (!passwordEncoder.matches(request.getPassword(), user.getPassword())) {
            throw new AuthenticationException("密码错误");
        }
      
        // 检查用户状态
        if (user.getStatus() != UserStatus.ACTIVE) {
            throw new AuthenticationException("用户账户已被禁用");
        }
      
        // 更新登录信息
        updateLoginInfo(user, request.getLoginIp());
      
        // 生成JWT令牌
        String accessToken = jwtTokenProvider.generateToken(user);
        String refreshToken = jwtTokenProvider.generateRefreshToken(user);
      
        // 缓存用户会话信息
        cacheUserSession(user.getId(), accessToken, refreshToken);
      
        return LoginResponse.builder()
            .accessToken(accessToken)
            .refreshToken(refreshToken)
            .user(UserDTO.from(user))
            .build();
    }
  
    /**
     * 获取用户学习档案
     */
    @Transactional(readOnly = true)
    public UserProfileResponse getUserProfile(Long userId) {
        User user = getUserById(userId);
      
        // 获取学习统计信息
        StudyStatistics statistics = studyServiceClient.getUserStudyStatistics(userId);
      
        // 获取最近学习记录
        List<RecentStudyRecord> recentRecords = studyServiceClient.getRecentStudyRecords(userId, 10);
      
        return UserProfileResponse.builder()
            .user(UserDTO.from(user))
            .preference(UserPreferenceDTO.from(user.getPreference()))
            .statistics(statistics)
            .recentRecords(recentRecords)
            .build();
    }
  
    /**
     * 更新用户偏好设置
     */
    public void updateUserPreference(Long userId, UserPreferenceUpdateRequest request) {
        User user = getUserById(userId);
        UserPreference preference = user.getPreference();
      
        if (preference == null) {
            preference = new UserPreference();
            preference.setUser(user);
        }
      
        // 更新偏好设置
        BeanUtils.copyProperties(request, preference, "userId", "user");
      
        userPreferenceRepository.save(preference);
      
        // 清除相关缓存
        clearUserCache(userId);
      
        log.info("用户 {} 更新了学习偏好设置", userId);
    }
  
    private void validateUserUniqueness(String username, String email) {
        if (userRepository.existsByUsername(username)) {
            throw new BusinessException("用户名已存在");
        }
        if (userRepository.existsByEmail(email)) {
            throw new BusinessException("邮箱已被注册");
        }
    }
  
    private void createDefaultPreference(User user) {
        UserPreference preference = UserPreference.builder()
            .user(user)
            .dailyStudyGoal(60)
            .studyReminderEnabled(true)
            .reminderTime(LocalTime.of(20, 0))
            .preferredDifficulty(Difficulty.MEDIUM)
            .learningMode(LearningMode.SYSTEMATIC)
            .build();
      
        userPreferenceRepository.save(preference);
    }
}
```

### 2.3 控制器设计
```java
// UserController.java
@RestController
@RequestMapping("/api/v1/users")
@Validated
@Slf4j
public class UserController {
  
    private final UserService userService;
  
    public UserController(UserService userService) {
        this.userService = userService;
    }
  
    /**
     * 用户注册
     */
    @PostMapping("/register")
    public ResponseEntity<ApiResponse<UserRegistrationResponse>> register(
        @Valid @RequestBody UserRegistrationRequest request
    ) {
        UserRegistrationResponse response = userService.register(request);
        return ResponseEntity.ok(ApiResponse.success(response));
    }
  
    /**
     * 用户登录
     */
    @PostMapping("/login")
    public ResponseEntity<ApiResponse<LoginResponse>> login(
        @Valid @RequestBody LoginRequest request,
        HttpServletRequest httpRequest
    ) {
        request.setLoginIp(getClientIpAddress(httpRequest));
        LoginResponse response = userService.login(request);
        return ResponseEntity.ok(ApiResponse.success(response));
    }
  
    /**
     * 获取当前用户信息
     */
    @GetMapping("/profile")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<UserProfileResponse>> getCurrentUserProfile(
        Authentication authentication
    ) {
        Long userId = getUserIdFromAuthentication(authentication);
        UserProfileResponse response = userService.getUserProfile(userId);
        return ResponseEntity.ok(ApiResponse.success(response));
    }
  
    /**
     * 更新用户偏好设置
     */
    @PutMapping("/preference")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Void>> updateUserPreference(
        @Valid @RequestBody UserPreferenceUpdateRequest request,
        Authentication authentication
    ) {
        Long userId = getUserIdFromAuthentication(authentication);
        userService.updateUserPreference(userId, request);
        return ResponseEntity.ok(ApiResponse.success());
    }
  
    /**
     * 刷新访问令牌
     */
    @PostMapping("/refresh-token")
    public ResponseEntity<ApiResponse<TokenRefreshResponse>> refreshToken(
        @Valid @RequestBody TokenRefreshRequest request
    ) {
        TokenRefreshResponse response = userService.refreshToken(request);
        return ResponseEntity.ok(ApiResponse.success(response));
    }
  
    /**
     * 用户退出登录
     */
    @PostMapping("/logout")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Void>> logout(
        Authentication authentication
    ) {
        Long userId = getUserIdFromAuthentication(authentication);
        userService.logout(userId);
        return ResponseEntity.ok(ApiResponse.success());
    }
}
```

## 3. 题库服务设计 (exercise-service)

### 3.1 实体设计
```java
// Question.java
@Entity
@Table(name = "questions")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Question {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
  
    @Column(nullable = false, columnDefinition = "TEXT")
    private String content;
  
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private QuestionType type;
  
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private Difficulty difficulty;
  
    // 软考科目分类
    @Column(name = "subject_code", nullable = false)
    private String subjectCode;
  
    // 知识点标签
    @ElementCollection
    @CollectionTable(name = "question_knowledge_points")
    private Set<String> knowledgePoints = new HashSet<>();
  
    // 题目选项（JSON格式存储）
    @Column(columnDefinition = "JSON")
    private String options;
  
    // 正确答案
    @Column(name = "correct_answer", nullable = false)
    private String correctAnswer;
  
    // 详细解析
    @Column(columnDefinition = "TEXT")
    private String explanation;
  
    // 题目来源（历年真题/模拟题）
    @Enumerated(EnumType.STRING)
    private QuestionSource source;
  
    // 所属年份（对于真题）
    @Column(name = "exam_year")
    private Integer examYear;
  
    // 题目序号（在试卷中的位置）
    @Column(name = "question_number")
    private Integer questionNumber;
  
    // 统计信息
    @Column(name = "total_attempts")
    private Integer totalAttempts = 0;
  
    @Column(name = "correct_attempts")
    private Integer correctAttempts = 0;
  
    // 审核状态
    @Enumerated(EnumType.STRING)
    private ReviewStatus reviewStatus = ReviewStatus.PENDING;
  
    @Column(name = "reviewer_id")
    private Long reviewerId;
  
    @Column(name = "review_comment")
    private String reviewComment;
  
    @CreationTimestamp
    @Column(name = "created_at")
    private LocalDateTime createdAt;
  
    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
  
    /**
     * 计算正确率
     */
    public double getCorrectRate() {
        if (totalAttempts == 0) return 0.0;
        return (double) correctAttempts / totalAttempts;
    }
}

// UserAnswer.java
@Entity
@Table(name = "user_answers")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserAnswer {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
  
    @Column(name = "user_id", nullable = false)
    private Long userId;
  
    @Column(name = "question_id", nullable = false)
    private Long questionId;
  
    @Column(name = "user_answer", nullable = false)
    private String userAnswer;
  
    @Column(name = "is_correct", nullable = false)
    private Boolean isCorrect;
  
    // 答题用时（秒）
    @Column(name = "time_spent")
    private Integer timeSpent;
  
    // 答题模式（练习/考试）
    @Enumerated(EnumType.STRING)
    @Column(name = "answer_mode")
    private AnswerMode answerMode;
  
    // 所属练习/考试会话ID
    @Column(name = "session_id")
    private String sessionId;
  
    @CreationTimestamp
    @Column(name = "answered_at")
    private LocalDateTime answeredAt;
}

// ExamPaper.java
@Entity
@Table(name = "exam_papers")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ExamPaper {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
  
    @Column(nullable = false)
    private String title;
  
    @Column(columnDefinition = "TEXT")
    private String description;
  
    @Column(name = "subject_code", nullable = false)
    private String subjectCode;
  
    // 试卷类型（真题/模拟题/专项练习）
    @Enumerated(EnumType.STRING)
    private PaperType paperType;
  
    // 考试时长（分钟）
    @Column(name = "duration_minutes", nullable = false)
    private Integer durationMinutes;
  
    // 总分
    @Column(name = "total_score", nullable = false)
    private Integer totalScore;
  
    // 及格分数
    @Column(name = "passing_score", nullable = false)
    private Integer passingScore;
  
    // 题目配置（JSON格式）
    @Column(name = "question_config", columnDefinition = "JSON")
    private String questionConfig;
  
    // 试卷状态
    @Enumerated(EnumType.STRING)
    private PaperStatus status = PaperStatus.ACTIVE;
  
    @Column(name = "created_by")
    private Long createdBy;
  
    @CreationTimestamp
    @Column(name = "created_at")
    private LocalDateTime createdAt;
  
    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
  
    // 试卷中的题目关联
    @OneToMany(mappedBy = "examPaper", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<PaperQuestion> paperQuestions = new ArrayList<>();
}
```

### 3.2 服务层设计
```java
// QuestionService.java
@Service
@Transactional
@Slf4j
public class QuestionService {
  
    private final QuestionRepository questionRepository;
    private final UserAnswerRepository userAnswerRepository;
    private final RedisTemplate<String, Object> redisTemplate;
    private final KafkaTemplate<String, Object> kafkaTemplate;
  
    /**
     * 智能推荐题目
     */
    public List<QuestionDTO> recommendQuestions(QuestionRecommendationRequest request) {
        // 获取用户学习历史
        List<UserAnswer> userAnswers = userAnswerRepository
            .findByUserIdAndAnsweredAtAfter(
                request.getUserId(), 
                LocalDateTime.now().minusDays(30)
            );
      
        // 分析用户薄弱知识点
        Set<String> weakKnowledgePoints = analyzeWeakKnowledgePoints(userAnswers);
      
        // 构建查询条件
        QuestionQueryCriteria criteria = QuestionQueryCriteria.builder()
            .subjectCode(request.getSubjectCode())
            .difficulty(request.getDifficulty())
            .knowledgePoints(weakKnowledgePoints)
            .excludeAnsweredQuestions(request.isExcludeAnswered())
            .userId(request.getUserId())
            .limit(request.getLimit())
            .build();
      
        // 查询推荐题目
        List<Question> questions = questionRepository.findByRecommendationCriteria(criteria);
      
        return questions.stream()
            .map(QuestionDTO::from)
            .collect(Collectors.toList());
    }

    /**
     * 提交答题记录
     */
    public AnswerSubmissionResponse submitAnswer(AnswerSubmissionRequest request) {
        // 查询题目信息
        Question question = questionRepository.findById(request.getQuestionId())
            .orElseThrow(() -> new BusinessException("题目不存在"));
      
        // 判断答案是否正确
        boolean isCorrect = question.getCorrectAnswer().equals(request.getUserAnswer());
      
        // 保存答题记录
        UserAnswer userAnswer = UserAnswer.builder()
            .userId(request.getUserId())
            .questionId(request.getQuestionId())
            .userAnswer(request.getUserAnswer())
            .isCorrect(isCorrect)
            .timeSpent(request.getTimeSpent())
            .answerMode(request.getAnswerMode())
            .sessionId(request.getSessionId())
            .build();
      
        userAnswerRepository.save(userAnswer);
      
        // 更新题目统计信息
        updateQuestionStatistics(question, isCorrect);
      
        // 发送答题事件到消息队列
        publishAnswerEvent(userAnswer, question);
      
        // 构建响应
        return AnswerSubmissionResponse.builder()
            .isCorrect(isCorrect)
            .correctAnswer(question.getCorrectAnswer())
            .explanation(question.getExplanation())
            .userAnswer(request.getUserAnswer())
            .timeSpent(request.getTimeSpent())
            .questionId(request.getQuestionId())
            .build();
    }
  
    /**
     * 生成智能试卷
     */
    public ExamPaperDTO generateIntelligentPaper(PaperGenerationRequest request) {
        // 分析用户能力水平
        UserAbilityProfile abilityProfile = analyzeUserAbility(request.getUserId());
      
        // 根据IRT模型选择合适难度的题目
        List<Question> selectedQuestions = selectQuestionsByIRT(
            request.getSubjectCode(),
            abilityProfile,
            request.getQuestionCount()
        );
      
        // 创建试卷
        ExamPaper examPaper = ExamPaper.builder()
            .title("智能生成试卷 - " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")))
            .subjectCode(request.getSubjectCode())
            .paperType(PaperType.INTELLIGENT)
            .durationMinutes(request.getDurationMinutes())
            .totalScore(calculateTotalScore(selectedQuestions))
            .passingScore(request.getPassingScore())
            .status(PaperStatus.ACTIVE)
            .createdBy(request.getUserId())
            .build();
      
        ExamPaper savedPaper = examPaperRepository.save(examPaper);
      
        // 保存试卷题目关联
        List<PaperQuestion> paperQuestions = IntStream.range(0, selectedQuestions.size())
            .mapToObj(i -> PaperQuestion.builder()
                .examPaper(savedPaper)
                .question(selectedQuestions.get(i))
                .questionOrder(i + 1)
                .score(calculateQuestionScore(selectedQuestions.get(i)))
                .build())
            .collect(Collectors.toList());
      
        paperQuestionRepository.saveAll(paperQuestions);
      
        return ExamPaperDTO.from(savedPaper, paperQuestions);
    }
  
    /**
     * 获取错题集
     */
    @Transactional(readOnly = true)
    public WrongQuestionCollectionResponse getWrongQuestions(WrongQuestionRequest request) {
        // 查询用户错题
        Pageable pageable = PageRequest.of(
            request.getPage(), 
            request.getSize(),
            Sort.by(Sort.Direction.DESC, "answeredAt")
        );
      
        Page<UserAnswer> wrongAnswers = userAnswerRepository
            .findWrongAnswersByUserIdAndFilters(
                request.getUserId(),
                request.getSubjectCode(),
                request.getKnowledgePoints(),
                request.getDifficulty(),
                pageable
            );
      
        // 获取题目详细信息
        List<WrongQuestionDTO> wrongQuestions = wrongAnswers.getContent()
            .stream()
            .map(this::convertToWrongQuestionDTO)
            .collect(Collectors.toList());
      
        // 统计错题分布
        WrongQuestionStatistics statistics = calculateWrongQuestionStatistics(request.getUserId());
      
        return WrongQuestionCollectionResponse.builder()
            .questions(wrongQuestions)
            .totalElements(wrongAnswers.getTotalElements())
            .totalPages(wrongAnswers.getTotalPages())
            .currentPage(request.getPage())
            .statistics(statistics)
            .build();
    }
  
    /**
     * 基于IRT模型选择题目
     */
    private List<Question> selectQuestionsByIRT(String subjectCode, UserAbilityProfile abilityProfile, int questionCount) {
        // IRT模型参数
        double userAbility = abilityProfile.getAbilityLevel();
        double targetDifficulty = userAbility + 0.5; // 稍微高于用户能力水平
      
        // 查询候选题目
        List<Question> candidateQuestions = questionRepository
            .findCandidateQuestionsForIRT(subjectCode, userAbility - 2.0, userAbility + 2.0);
      
        // 计算每个题目的信息量
        List<QuestionIRTInfo> questionInfos = candidateQuestions.stream()
            .map(q -> calculateQuestionInformation(q, userAbility))
            .sorted(Comparator.comparing(QuestionIRTInfo::getInformation).reversed())
            .collect(Collectors.toList());
      
        // 选择信息量最高的题目
        return questionInfos.stream()
            .limit(questionCount)
            .map(QuestionIRTInfo::getQuestion)
            .collect(Collectors.toList());
    }
  
    /**
     * 计算题目信息量（IRT模型）
     */
    private QuestionIRTInfo calculateQuestionInformation(Question question, double userAbility) {
        // IRT 3PL模型参数（实际应用中这些参数需要通过历史数据估算）
        double discrimination = 1.5; // 区分度参数
        double difficulty = mapDifficultyToIRT(question.getDifficulty()); // 难度参数
        double guessing = 0.2; // 猜测参数
      
        // 计算题目信息函数
        double z = discrimination * (userAbility - difficulty);
        double p = guessing + (1 - guessing) / (1 + Math.exp(-z));
        double q = 1 - p;
      
        double information = Math.pow(discrimination, 2) * Math.pow(p - guessing, 2) * q / 
                           (Math.pow(1 - guessing, 2) * p);
      
        return QuestionIRTInfo.builder()
            .question(question)
            .information(information)
            .expectedProbability(p)
            .build();
    }
  
    private void updateQuestionStatistics(Question question, boolean isCorrect) {
        question.setTotalAttempts(question.getTotalAttempts() + 1);
        if (isCorrect) {
            question.setCorrectAttempts(question.getCorrectAttempts() + 1);
        }
        questionRepository.save(question);
    }
  
    private void publishAnswerEvent(UserAnswer userAnswer, Question question) {
        AnswerEvent event = AnswerEvent.builder()
            .userId(userAnswer.getUserId())
            .questionId(userAnswer.getQuestionId())
            .isCorrect(userAnswer.getIsCorrect())
            .timeSpent(userAnswer.getTimeSpent())
            .knowledgePoints(question.getKnowledgePoints())
            .difficulty(question.getDifficulty())
            .subjectCode(question.getSubjectCode())
            .answeredAt(userAnswer.getAnsweredAt())
            .build();
      
        kafkaTemplate.send("answer-events", event);
    }
}
```

### 3.3 控制器设计
```java
// QuestionController.java
@RestController
@RequestMapping("/api/v1/questions")
@Validated
@Slf4j
public class QuestionController {
  
    private final QuestionService questionService;
  
    /**
     * 获取推荐题目
     */
    @GetMapping("/recommendations")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<QuestionDTO>>> getRecommendations(
        @Valid @ModelAttribute QuestionRecommendationRequest request,
        Authentication authentication
    ) {
        Long userId = getUserIdFromAuthentication(authentication);
        request.setUserId(userId);
      
        List<QuestionDTO> questions = questionService.recommendQuestions(request);
        return ResponseEntity.ok(ApiResponse.success(questions));
    }
  
    /**
     * 提交答题
     */
    @PostMapping("/submit-answer")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<AnswerSubmissionResponse>> submitAnswer(
        @Valid @RequestBody AnswerSubmissionRequest request,
        Authentication authentication
    ) {
        Long userId = getUserIdFromAuthentication(authentication);
        request.setUserId(userId);
      
        AnswerSubmissionResponse response = questionService.submitAnswer(request);
        return ResponseEntity.ok(ApiResponse.success(response));
    }
  
    /**
     * 获取错题集
     */
    @GetMapping("/wrong-questions")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<WrongQuestionCollectionResponse>> getWrongQuestions(
        @Valid @ModelAttribute WrongQuestionRequest request,
        Authentication authentication
    ) {
        Long userId = getUserIdFromAuthentication(authentication);
        request.setUserId(userId);
      
        WrongQuestionCollectionResponse response = questionService.getWrongQuestions(request);
        return ResponseEntity.ok(ApiResponse.success(response));
    }
  
    /**
     * 生成智能试卷
     */
    @PostMapping("/intelligent-paper")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<ExamPaperDTO>> generateIntelligentPaper(
        @Valid @RequestBody PaperGenerationRequest request,
        Authentication authentication
    ) {
        Long userId = getUserIdFromAuthentication(authentication);
        request.setUserId(userId);
      
        ExamPaperDTO examPaper = questionService.generateIntelligentPaper(request);
        return ResponseEntity.ok(ApiResponse.success(examPaper));
    }
}

// ExamController.java
@RestController
@RequestMapping("/api/v1/exams")
@Validated
@Slf4j
public class ExamController {
  
    private final ExamService examService;
  
    /**
     * 开始考试
     */
    @PostMapping("/{paperId}/start")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<ExamSessionResponse>> startExam(
        @PathVariable Long paperId,
        Authentication authentication
    ) {
        Long userId = getUserIdFromAuthentication(authentication);
        ExamSessionResponse session = examService.startExam(paperId, userId);
        return ResponseEntity.ok(ApiResponse.success(session));
    }
  
    /**
     * 提交考试
     */
    @PostMapping("/sessions/{sessionId}/submit")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<ExamResultResponse>> submitExam(
        @PathVariable String sessionId,
        @Valid @RequestBody ExamSubmissionRequest request,
        Authentication authentication
    ) {
        Long userId = getUserIdFromAuthentication(authentication);
        ExamResultResponse result = examService.submitExam(sessionId, userId, request);
        return ResponseEntity.ok(ApiResponse.success(result));
    }
  
    /**
     * 获取考试结果详情
     */
    @GetMapping("/results/{resultId}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<ExamResultDetailResponse>> getExamResult(
        @PathVariable Long resultId,
        Authentication authentication
    ) {
        Long userId = getUserIdFromAuthentication(authentication);
        ExamResultDetailResponse result = examService.getExamResultDetail(resultId, userId);
        return ResponseEntity.ok(ApiResponse.success(result));
    }
}
```

## 4. AI服务设计 (ai-service)

### 4.1 智能推荐引擎
```java
// RecommendationEngine.java
@Service
@Slf4j
public class RecommendationEngine {
  
    private final UserBehaviorAnalyzer userBehaviorAnalyzer;
    private final KnowledgeGraphService knowledgeGraphService;
    private final CollaborativeFilteringService collaborativeFilteringService;
  
    /**
     * 生成个性化学习路径
     */
    public LearningPathRecommendation generateLearningPath(Long userId, String subjectCode) {
        // 1. 分析用户当前学习状态
        UserLearningState currentState = userBehaviorAnalyzer.analyzeCurrentState(userId);
      
        // 2. 识别知识薄弱点
        List<String> weakKnowledgePoints = identifyWeakKnowledgePoints(userId, subjectCode);
      
        // 3. 基于知识图谱构建学习路径
        List<LearningNode> learningPath = knowledgeGraphService
            .generateOptimalPath(currentState, weakKnowledgePoints, subjectCode);
      
        // 4. 结合协同过滤推荐相似用户的成功路径
        List<LearningNode> collaborativeRecommendations = collaborativeFilteringService
            .recommendBasedOnSimilarUsers(userId, subjectCode);
      
        // 5. 融合多种推荐策略
        List<LearningNode> finalPath = mergeLearningPaths(learningPath, collaborativeRecommendations);
      
        return LearningPathRecommendation.builder()
            .userId(userId)
            .subjectCode(subjectCode)
            .learningNodes(finalPath)
            .estimatedDuration(calculateEstimatedDuration(finalPath))
            .confidence(calculateConfidence(currentState, finalPath))
            .generatedAt(LocalDateTime.now())
            .build();
    }
  
    /**
     * 智能题目推荐
     */
    public List<QuestionRecommendation> recommendQuestions(QuestionRecommendationContext context) {
        // 1. 基于用户能力水平推荐
        List<Question> abilityBasedQuestions = recommendByAbilityLevel(context);
      
        // 2. 基于学习目标推荐
        List<Question> goalBasedQuestions = recommendByLearningGoals(context);
      
        // 3. 基于遗忘曲线推荐复习题目
        List<Question> reviewQuestions = recommendReviewQuestions(context);
      
        // 4. 融合推荐结果
        return fuseQuestionRecommendations(abilityBasedQuestions, goalBasedQuestions, reviewQuestions);
    }
  
    /**
     * 基于遗忘曲线的复习推荐
     */
    private List<Question> recommendReviewQuestions(QuestionRecommendationContext context) {
        Long userId = context.getUserId();
      
        // 获取用户历史答题记录
        List<UserAnswer> historyAnswers = userAnswerRepository
            .findCorrectAnswersByUserIdWithinDays(userId, 30);
      
        List<Question> reviewQuestions = new ArrayList<>();
      
        for (UserAnswer answer : historyAnswers) {
            // 计算遗忘概率
            double forgettingProbability = calculateForgettingProbability(
                answer.getAnsweredAt(),
                answer.getIsCorrect(),
                getUserMemoryStrength(userId, answer.getQuestionId())
            );
          
            // 如果遗忘概率高于阈值，加入复习队列
            if (forgettingProbability > 0.7) {
                Question question = questionRepository.findById(answer.getQuestionId())
                    .orElse(null);
                if (question != null) {
                    reviewQuestions.add(question);
                }
            }
        }
      
        return reviewQuestions;
    }
  
    /**
     * 计算遗忘概率（基于艾宾浩斯遗忘曲线）
     */
    private double calculateForgettingProbability(LocalDateTime answeredAt, Boolean wasCorrect, double memoryStrength) {
        long daysPassed = ChronoUnit.DAYS.between(answeredAt, LocalDateTime.now());
      
        // 基础遗忘曲线：R = e^(-t/S)
        // R: 记忆保持率, t: 时间, S: 记忆强度
        double baseRetention = Math.exp(-daysPassed / memoryStrength);
      
        // 如果当时答错了，遗忘概率更高
        if (!wasCorrect) {
            baseRetention *= 0.7;
        }
      
        return 1 - baseRetention;
    }
  
    /**
     * 获取用户对特定题目的记忆强度
     */
    private double getUserMemoryStrength(Long userId, Long questionId) {
        // 查询用户对该题目的所有答题记录
        List<UserAnswer> answers = userAnswerRepository
            .findByUserIdAndQuestionIdOrderByAnsweredAtDesc(userId, questionId);
      
        if (answers.isEmpty()) {
            return 1.0; // 默认记忆强度
        }
      
        // 基于答题次数和正确率计算记忆强度
        long correctCount = answers.stream().mapToLong(a -> a.getIsCorrect() ? 1 : 0).sum();
        double correctRate = (double) correctCount / answers.size();
      
        // 记忆强度 = 基础强度 * 正确率加权 * 重复次数加权
        return 1.0 + (correctRate * 2.0) + Math.log(answers.size() + 1);
    }
}

// UserBehaviorAnalyzer.java
@Service
@Slf4j
public class UserBehaviorAnalyzer {
  
    private final UserAnswerRepository userAnswerRepository;
    private final StudySessionRepository studySessionRepository;
  
    /**
     * 分析用户学习行为模式
     */
    public UserLearningBehaviorProfile analyzeLearningBehavior(Long userId) {
        // 获取最近30天的学习数据
        LocalDateTime startDate = LocalDateTime.now().minusDays(30);
      
        // 1. 学习时间分析
        List<StudySession> sessions = studySessionRepository
            .findByUserIdAndStartTimeAfter(userId, startDate);
      
        LearningTimePattern timePattern = analyzeLearningTimePattern(sessions);
      
        // 2. 答题行为分析
        List<UserAnswer> answers = userAnswerRepository
            .findByUserIdAndAnsweredAtAfter(userId, startDate);
      
        AnsweringBehaviorPattern answerPattern = analyzeAnsweringBehavior(answers);
      
        // 3. 学习偏好分析
        LearningPreferencePattern preferencePattern = analyzeLearningPreference(answers, sessions);
      
        // 4. 学习效率分析
        LearningEfficiencyMetrics efficiency = calculateLearningEfficiency(answers, sessions);
      
        return UserLearningBehaviorProfile.builder()
            .userId(userId)
            .timePattern(timePattern)
            .answerPattern(answerPattern)
            .preferencePattern(preferencePattern)
            .efficiency(efficiency)
            .analysisDate(LocalDateTime.now())
            .build();
    }
  
    /**
     * 分析学习时间模式
     */
    private LearningTimePattern analyzeLearningTimePattern(List<StudySession> sessions) {
        if (sessions.isEmpty()) {
            return LearningTimePattern.builder().build();
        }
      
        // 计算每日学习时长分布
        Map<DayOfWeek, Long> dailyDuration = sessions.stream()
            .collect(Collectors.groupingBy(
                s -> s.getStartTime().getDayOfWeek(),
                Collectors.summingLong(s -> ChronoUnit.MINUTES.between(s.getStartTime(), s.getEndTime()))
            ));
      
        // 计算每小时学习活跃度
        Map<Integer, Long> hourlyActivity = sessions.stream()
            .collect(Collectors.groupingBy(
                s -> s.getStartTime().getHour(),
                Collectors.counting()
            ));
      
        // 计算平均学习时长
        double avgSessionDuration = sessions.stream()
            .mapToLong(s -> ChronoUnit.MINUTES.between(s.getStartTime(), s.getEndTime()))
            .average()
            .orElse(0.0);
      
        // 识别最佳学习时间段
        Integer peakHour = hourlyActivity.entrySet().stream()
            .max(Map.Entry.comparingByValue())
            .map(Map.Entry::getKey)
            .orElse(null);
      
        return LearningTimePattern.builder()
            .dailyDuration(dailyDuration)
            .hourlyActivity(hourlyActivity)
            .averageSessionDuration(avgSessionDuration)
            .peakLearningHour(peakHour)
            .totalStudyDays(sessions.size())
            .build();
    }
  
    /**
     * 分析答题行为模式
     */
    private AnsweringBehaviorPattern analyzeAnsweringBehavior(List<UserAnswer> answers) {
        if (answers.isEmpty()) {
            return AnsweringBehaviorPattern.builder().build();
        }
      
        // 总体正确率
        double overallAccuracy = answers.stream()
            .mapToInt(a -> a.getIsCorrect() ? 1 : 0)
            .average()
            .orElse(0.0);
      
        // 不同难度的正确率
        Map<Difficulty, Double> accuracyByDifficulty = answers.stream()
            .collect(Collectors.groupingBy(
                a -> getDifficultyByQuestionId(a.getQuestionId()),
                Collectors.averagingInt(a -> a.getIsCorrect() ? 1 : 0)
            ));
      
        // 答题速度分析
        OptionalDouble avgTimeSpent = answers.stream()
            .filter(a -> a.getTimeSpent() != null && a.getTimeSpent() > 0)
            .mapToInt(UserAnswer::getTimeSpent)
            .average();
      
        // 答题时间趋势（是否越来越快/慢）
        List<UserAnswer> sortedAnswers = answers.stream()
            .sorted(Comparator.comparing(UserAnswer::getAnsweredAt))
            .collect(Collectors.toList());
      
        double timeSpentTrend = calculateTimeSpentTrend(sortedAnswers);
      
        // 连续答题模式分析
        List<AnsweringSession> answeringSessions = identifyAnsweringSessions(sortedAnswers);
      
        return AnsweringBehaviorPattern.builder()
            .overallAccuracy(overallAccuracy)
            .accuracyByDifficulty(accuracyByDifficulty)
            .averageTimeSpent(avgTimeSpent.orElse(0.0))
            .timeSpentTrend(timeSpentTrend)
            .answeringSessions(answeringSessions)
            .totalQuestions(answers.size())
            .build();
    }
}

// KnowledgeGraphService.java
@Service
@Slf4j
public class KnowledgeGraphService {
  
    private final Neo4jTemplate neo4jTemplate;
    private final KnowledgePointRepository knowledgePointRepository;
  
    /**
     * 构建知识图谱
     */
    @PostConstruct
    public void buildKnowledgeGraph() {
        log.info("开始构建软考知识图谱...");
      
        // 1. 加载知识点数据
        List<KnowledgePoint> knowledgePoints = knowledgePointRepository.findAll();
      
        // 2. 创建知识点节点
        for (KnowledgePoint kp : knowledgePoints) {
            createKnowledgeNode(kp);
        }
      
        // 3. 建立知识点关系
        buildKnowledgeRelationships(knowledgePoints);
      
        log.info("知识图谱构建完成，共创建 {} 个知识点节点", knowledgePoints.size());
    }
  
    /**
     * 生成最优学习路径
     */
    public List<LearningNode> generateOptimalPath(
        UserLearningState currentState, 
        List<String> weakKnowledgePoints, 
        String subjectCode
    ) {
        List<LearningNode> learningPath = new ArrayList<>();
      
        // 1. 从知识图谱中找到薄弱知识点
        List<KnowledgeGraphNode> weakNodes = findKnowledgeNodes(weakKnowledgePoints);
      
        // 2. 为每个薄弱知识点找到前置知识点
        for (KnowledgeGraphNode weakNode : weakNodes) {
            List<KnowledgeGraphNode> prerequisites = findPrerequisites(weakNode);
          
            // 3. 检查用户是否已掌握前置知识
            List<KnowledgeGraphNode> unlearned = prerequisites.stream()
                .filter(node -> !currentState.getMasteredKnowledgePoints().contains(node.getName()))
                .collect(Collectors.toList());
          
            // 4. 构建学习序列
            List<LearningNode> sequence = buildLearningSequence(unlearned, weakNode);
            learningPath.addAll(sequence);
        }
      
        // 5. 优化学习路径（去重、排序）
        return optimizeLearningPath(learningPath);
    }
  
    /**
     * 找到知识点的所有前置依赖
     */
    private List<KnowledgeGraphNode> findPrerequisites(KnowledgeGraphNode targetNode) {
        String cypher = """
            MATCH (target:KnowledgePoint {name: $targetName})
            MATCH path = (prereq:KnowledgePoint)-[:PREREQUISITE*]->(target)
            RETURN prereq
            ORDER BY length(path)
            """;
      
        return neo4jTemplate.findAll(cypher, 
            Map.of("targetName", targetNode.getName()), 
            KnowledgeGraphNode.class);
    }
  
    /**
     * 构建学习序列
     */
    private List<LearningNode> buildLearningSequence(
        List<KnowledgeGraphNode> prerequisites, 
        KnowledgeGraphNode target
    ) {
        List<LearningNode> sequence = new ArrayList<>();
      
        // 1. 按依赖关系排序前置知识点
        List<KnowledgeGraphNode> sortedPrereqs = topologicalSort(prerequisites);
      
        // 2. 为每个知识点创建学习节点
        for (KnowledgeGraphNode node : sortedPrereqs) {
            LearningNode learningNode = LearningNode.builder()
                .knowledgePointName(node.getName())
                .difficulty(node.getDifficulty())
                .estimatedDuration(node.getEstimatedLearningTime())
                .resourceRecommendations(getResourceRecommendations(node))
                .practiceQuestions(getPracticeQuestions(node))
                .build();
          
            sequence.add(learningNode);
        }
      
        // 3. 添加目标知识点
        LearningNode targetNode = LearningNode.builder()
            .knowledgePointName(target.getName())
            .difficulty(target.getDifficulty())
            .estimatedDuration(target.getEstimatedLearningTime())
            .resourceRecommendations(getResourceRecommendations(target))
            .practiceQuestions(getPracticeQuestions(target))
            .isTarget(true)
            .build();
      
        sequence.add(targetNode);
      
        return sequence;
    }
}
```

### 4.2 自然语言处理服务
```java
// AIQuestionAnsweringService.java
@Service
@Slf4j
public class AIQuestionAnsweringService {
  
    private final OpenAIClient openAIClient;
    private final KnowledgeBaseService knowledgeBaseService;
    private final ConversationContextManager contextManager;
  
    /**
     * 智能答疑
     */
    public AIAnswerResponse answerQuestion(AIQuestionRequest request) {
        try {
            // 1. 分析问题类型和意图
            QuestionIntent intent = analyzeQuestionIntent(request.getQuestion());
          
            // 2. 从知识库检索相关信息
            List<KnowledgeFragment> relevantKnowledge = knowledgeBaseService
                .searchRelevantKnowledge(request.getQuestion(), request.getSubjectCode());
          
            // 3. 构建上下文提示
            String contextPrompt = buildContextPrompt(
                request.getQuestion(), 
                relevantKnowledge, 
                intent,
                request.getUserId()
            );
          
            // 4. 调用大语言模型生成回答
            OpenAIRequest openAIRequest = OpenAIRequest.builder()
                .model("gpt-4")
                .messages(List.of(
                    ChatMessage.system("你是一个专业的软考学习助手，请根据提供的知识库信息回答学生的问题。"),
                    ChatMessage.user(contextPrompt)
                ))
                .temperature(0.7)
                .maxTokens(1000)
                .build();
          
            OpenAIResponse openAIResponse = openAIClient.chatCompletion(openAIRequest);
            String aiAnswer = openAIResponse.getChoices().get(0).getMessage().getContent();
          
            // 5. 后处理和优化回答
            String optimizedAnswer = postProcessAnswer(aiAnswer, intent);
          
            // 6. 生成相关推荐
            List<String> relatedTopics = generateRelatedTopics(request.getQuestion(), relevantKnowledge);
            List<QuestionDTO> practiceQuestions = generatePracticeQuestions(intent, request.getSubjectCode());
          
            // 7. 保存对话记录
            saveConversationRecord(request.getUserId(), request.getQuestion(), optimizedAnswer, intent);
          
            return AIAnswerResponse.builder()
                .answer(optimizedAnswer)
                .confidence(calculateAnswerConfidence(relevantKnowledge, openAIResponse))
                .relatedTopics(relatedTopics)
                .practiceQuestions(practiceQuestions)
                .sources(extractSources(relevantKnowledge))
                .conversationId(generateConversationId(request.getUserId()))
                .build();
              
        } catch (Exception e) {
            log.error("AI答疑服务异常: ", e);
            return AIAnswerResponse.builder()
                .answer("抱歉，我暂时无法回答这个问题，请稍后再试或联系人工客服。")
                .confidence(0.0)
                .build();
        }
    }
  
    /**
     * 分析问题意图
     */
    private QuestionIntent analyzeQuestionIntent(String question) {
        // 使用NLP技术分析问题类型
        String lowerQuestion = question.toLowerCase();
      
        if (lowerQuestion.contains("什么是") || lowerQuestion.contains("定义") || lowerQuestion.contains("概念")) {
            return QuestionIntent.DEFINITION;
        } else if (lowerQuestion.contains("如何") || lowerQuestion.contains("怎么") || lowerQuestion.contains("步骤")) {
            return QuestionIntent.HOW_TO;
        } else if (lowerQuestion.contains("为什么") || lowerQuestion.contains("原因") || lowerQuestion.contains("原理")) {
            return QuestionIntent.WHY;
        } else if (lowerQuestion.contains("区别") || lowerQuestion.contains("不同") || lowerQuestion.contains("对比")) {
            return QuestionIntent.COMPARISON;
        } else if (lowerQuestion.contains("例子") || lowerQuestion.contains("示例") || lowerQuestion.contains("举例")) {
            return QuestionIntent.EXAMPLE;
        } else if (lowerQuestion.contains("解题") || lowerQuestion.contains("做题") || lowerQuestion.contains("答案")) {
            return QuestionIntent.PROBLEM_SOLVING;
        } else {
            return QuestionIntent.GENERAL;
        }
    }
  
    /**
     * 构建上下文提示
     */
    private String buildContextPrompt(String question, List<KnowledgeFragment> knowledge, 
                                    QuestionIntent intent, Long userId) {
        StringBuilder prompt = new StringBuilder();
      
        // 添加用户学习背景
        UserLearningProfile userProfile = getUserLearningProfile(userId);
        prompt.append("用户学习背景：").append(userProfile.getDescription()).append("\n\n");
      
        // 添加相关知识点
        prompt.append("相关知识点信息：\n");
        for (KnowledgeFragment fragment : knowledge) {
            prompt.append("- ").append(fragment.getTitle()).append("：")
                  .append(fragment.getContent()).append("\n");
        }
      
        // 添加问题类型指导
        prompt.append("\n问题类型：").append(intent.getDescription()).append("\n");
        prompt.append("请根据以上信息回答以下问题，注意：\n");
        prompt.append("1. 回答要准确、专业、易懂\n");
        prompt.append("2. 结合软考考试要求\n");
        prompt.append("3. 提供具体的学习建议\n");
        prompt.append("4. 如果涉及计算或代码，请给出详细步骤\n\n");
      
        prompt.append("问题：").append(question);
      
        return prompt.toString();
    }
  
    /**
     * 后处理AI回答
     */
    private String postProcessAnswer(String aiAnswer, QuestionIntent intent) {
        // 1. 格式化回答
        String formattedAnswer = formatAnswer(aiAnswer);
      
        // 2. 添加相关提示
        StringBuilder enhancedAnswer = new StringBuilder(formattedAnswer);
      
        // 根据问题类型添加特定的学习建议
        switch (intent) {
            case DEFINITION:
                enhancedAnswer.append("\n\n💡 学习建议：理解概念后，建议通过相关练习题来巩固。");
                break;
            case HOW_TO:
                enhancedAnswer.append("\n\n💡 学习建议：建议动手实践，加深理解。");
                break;
            case PROBLEM_SOLVING:
                enhancedAnswer.append("\n\n💡 学习建议：掌握解题思路后，可以尝试类似题目。");
                break;
            default:
                enhancedAnswer.append("\n\n💡 如有其他疑问，欢迎继续提问。");
        }
      
        return enhancedAnswer.toString();
    }
  
    /**
     * 生成练习题推荐
     */
    private List<QuestionDTO> generatePracticeQuestions(QuestionIntent intent, String subjectCode) {
        // 根据问题意图推荐相应的练习题
        QuestionRecommendationRequest request = QuestionRecommendationRequest.builder()
            .subjectCode(subjectCode)
            .limit(3)
            .build();
      
        // 根据意图调整推荐策略
        switch (intent) {
            case DEFINITION:
                request.setQuestionTypes(Set.of(QuestionType.SINGLE_CHOICE));
                break;
            case PROBLEM_SOLVING:
                request.setQuestionTypes(Set.of(QuestionType.CASE_ANALYSIS));
                break;
            default:
                request.setQuestionTypes(Set.of(QuestionType.SINGLE_CHOICE, QuestionType.MULTIPLE_CHOICE));
        }
      
        return questionService.recommendQuestions(request);
    }
}

// ConversationContextManager.java
@Service
@Slf4j
public class ConversationContextManager {
  
    private final RedisTemplate<String, Object> redisTemplate;
    private static final String CONVERSATION_KEY_PREFIX = "conversation:";
    private static final int CONTEXT_EXPIRY_HOURS = 24;
  
    /**
     * 保存对话上下文
     */
    public void saveConversationContext(String conversationId, ConversationContext context) {
        String key = CONVERSATION_KEY_PREFIX + conversationId;
        redisTemplate.opsForValue().set(key, context, Duration.ofHours(CONTEXT_EXPIRY_HOURS));
    }
  
    /**
     * 获取对话上下文
     */
    public ConversationContext getConversationContext(String conversationId) {
        String key = CONVERSATION_KEY_PREFIX + conversationId;
        return (ConversationContext) redisTemplate.opsForValue().get(key);
    }
  
    /**
     * 更新对话上下文
     */
    public void updateConversationContext(String conversationId, String question, String answer) {
        ConversationContext context = getConversationContext(conversationId);
        if (context == null) {
            context = new ConversationContext(conversationId);
        }
      
        context.addExchange(question, answer);
      
        // 保持最近10轮对话
        if (context.getExchanges().size() > 10) {
            context.getExchanges().remove(0);
        }
      
        saveConversationContext(conversationId, context);
    }
}
```

### 4.3 学习分析服务
```java
// LearningAnalyticsService.java
@Service
@Slf4j
public class LearningAnalyticsService {
  
    private final UserAnswerRepository userAnswerRepository;
    private final StudySessionRepository studySessionRepository;
    private final MachineLearningService mlService;
  
    /**
     * 生成个性化学习报告
     */
    public PersonalizedLearningReport generateLearningReport(Long userId, String subjectCode) {
        // 1. 收集用户学习数据
        LearningDataCollection dataCollection = collectLearningData(userId, subjectCode);
      
        // 2. 分析学习进度
        LearningProgressAnalysis progressAnalysis = analyzeLearningProgress(dataCollection);
      
        // 3. 识别知识盲区
        List<KnowledgeGap> knowledgeGaps = identifyKnowledgeGaps(dataCollection);
      
        // 4. 预测考试通过概率
        ExamPassPrediction passPrediction = predictExamPassProbability(userId, subjectCode);
      
        // 5. 生成改进建议
        List<ImprovementSuggestion> suggestions = generateImprovementSuggestions(
            progressAnalysis, knowledgeGaps, passPrediction);
      
        // 6. 制定学习计划
        StudyPlanRecommendation studyPlan = generateStudyPlan(userId, subjectCode, knowledgeGaps);
      
        return PersonalizedLearningReport.builder()
            .userId(userId)
            .subjectCode(subjectCode)
            .progressAnalysis(progressAnalysis)
            .knowledgeGaps(knowledgeGaps)
            .passPrediction(passPrediction)
            .suggestions(suggestions)
            .studyPlan(studyPlan)
            .generatedAt(LocalDateTime.now())
            .build();
    }
  
    /**
     * 预测考试通过概率
     */
    public ExamPassPrediction predictExamPassProbability(Long userId, String subjectCode) {
        // 1. 提取特征
        LearningFeatures features = extractLearningFeatures(userId, subjectCode);
      
        // 2. 使用机器学习模型预测
        MLPredictionRequest request = MLPredictionRequest.builder()
            .modelName("exam_pass_predictor")
            .features(features.toFeatureVector())
            .build();
      
        MLPredictionResponse response = mlService.predict(request);
      
        // 3. 解析预测结果
        double probability = response.getProbability();
        String confidenceLevel = getConfidenceLevel(response.getConfidence());
      
        // 4. 分析影响因素
        List<FactorAnalysis> factorAnalyses = analyzeInfluencingFactors(features, response);
      
        return ExamPassPrediction.builder()
            .probability(probability)
            .confidenceLevel(confidenceLevel)
            .influencingFactors(factorAnalyses)
            .recommendations(generatePassPredictionRecommendations(probability, factorAnalyses))
            .predictionDate(LocalDateTime.now())
            .build();
    }
  
    /**
     * 自适应难度调整
     */
    public DifficultyAdjustmentResult adjustDifficulty(Long userId, String subjectCode) {
        // 1. 分析用户当前能力水平
        UserAbilityLevel currentLevel = assessUserAbilityLevel(userId, subjectCode);
      
        // 2. 分析最近答题表现
        List<UserAnswer> recentAnswers = userAnswerRepository
            .findRecentAnswersByUserIdAndSubject(userId, subjectCode, 20);
      
        PerformanceMetrics recentPerformance = calculatePerformanceMetrics(recentAnswers);
      
        // 3. 基于IRT模型计算最优难度
        double optimalDifficulty = calculateOptimalDifficulty(currentLevel, recentPerformance);
      
        // 4. 考虑学习目标和时间限制
        UserPreference preference = userPreferenceRepository.findByUserId(userId);
        double adjustedDifficulty = adjustForUserPreference(optimalDifficulty, preference);
      
        return DifficultyAdjustmentResult.builder()
            .userId(userId)
            .subjectCode(subjectCode)
            .currentAbilityLevel(currentLevel.getLevel())
            .recommendedDifficulty(adjustedDifficulty)
            .adjustmentReason(generateAdjustmentReason(currentLevel, recentPerformance))
            .confidenceScore(calculateAdjustmentConfidence(recentPerformance))
            .build();
    }
  
    /**
     * 实时学习状态监控
     */
    @EventListener
    public void handleAnswerSubmissionEvent(AnswerSubmissionEvent event) {
        // 1. 更新用户实时能力评估
        updateRealTimeAbilityAssessment(event.getUserId(), event.getQuestionId(), 
                                       event.isCorrect(), event.getTimeSpent());
      
        // 2. 检测学习异常模式
        LearningAnomalyDetectionResult anomaly = detectLearningAnomaly(event);
        if (anomaly.hasAnomaly()) {
            handleLearningAnomaly(event.getUserId(), anomaly);
        }
      
        // 3. 触发个性化推荐更新
        triggerRecommendationUpdate(event.getUserId(), event.getSubjectCode());
      
        // 4. 更新学习进度缓存
        updateLearningProgressCache(event.getUserId(), event.getSubjectCode());
    }
  
    /**
     * 检测学习异常模式
     */
    private LearningAnomalyDetectionResult detectLearningAnomaly(AnswerSubmissionEvent event) {
        Long userId = event.getUserId();
      
        // 获取用户最近的答题模式
        List<UserAnswer> recentAnswers = userAnswerRepository
            .findRecentAnswersByUserId(userId, 10);
      
        List<AnomalyType> detectedAnomalies = new ArrayList<>();
      
        // 1. 检测连续错题模式
        if (hasConsecutiveWrongAnswers(recentAnswers, 5)) {
            detectedAnomalies.add(AnomalyType.CONSECUTIVE_WRONG_ANSWERS);
        }
      
        // 2. 检测答题时间异常
        if (hasAbnormalAnswerTime(recentAnswers)) {
            detectedAnomalies.add(AnomalyType.ABNORMAL_ANSWER_TIME);
        }
      
        // 3. 检测学习疲劳
        if (hasLearningFatigue(userId)) {
            detectedAnomalies.add(AnomalyType.LEARNING_FATIGUE);
        }
      
        // 4. 检测知识点混淆
        if (hasKnowledgePointConfusion(recentAnswers)) {
            detectedAnomalies.add(AnomalyType.KNOWLEDGE_POINT_CONFUSION);
        }
      
        return LearningAnomalyDetectionResult.builder()
            .userId(userId)
            .detectedAnomalies(detectedAnomalies)
            .confidence(calculateAnomalyConfidence(detectedAnomalies, recentAnswers))
            .detectionTime(LocalDateTime.now())
            .recommendations(generateAnomalyRecommendations(detectedAnomalies))
            .build();
    }
  
    /**
     * 处理学习异常
     */
    private void handleLearningAnomaly(Long userId, LearningAnomalyDetectionResult anomaly) {
        for (AnomalyType anomalyType : anomaly.getDetectedAnomalies()) {
            switch (anomalyType) {
                case CONSECUTIVE_WRONG_ANSWERS:
                    // 推荐复习基础知识点
                    recommendBasicKnowledgeReview(userId);
                    // 降低题目难度
                    adjustDifficultyDown(userId);
                    break;
                  
                case LEARNING_FATIGUE:
                    // 建议休息
                    sendRestRecommendation(userId);
                    // 调整学习计划
                    adjustStudySchedule(userId);
                    break;
                  
                case ABNORMAL_ANSWER_TIME:
                    // 分析是否需要时间管理训练
                    recommendTimeManagementTraining(userId);
                    break;
                  
                case KNOWLEDGE_POINT_CONFUSION:
                    // 推荐相关知识点对比学习
                    recommendComparativeLearning(userId, anomaly);
                    break;
            }
        }
      
        // 记录异常处理日志
        logAnomalyHandling(userId, anomaly);
    }
  
    /**
     * 生成学习改进建议
     */
    private List<ImprovementSuggestion> generateImprovementSuggestions(
        LearningProgressAnalysis progressAnalysis,
        List<KnowledgeGap> knowledgeGaps,
        ExamPassPrediction passPrediction
    ) {
        List<ImprovementSuggestion> suggestions = new ArrayList<>();
      
        // 1. 基于进度分析的建议
        if (progressAnalysis.getOverallProgress() < 0.5) {
            suggestions.add(ImprovementSuggestion.builder()
                .type(SuggestionType.STUDY_FREQUENCY)
                .priority(Priority.HIGH)
                .title("增加学习频率")
                .description("您的整体学习进度较慢，建议增加每日学习时间至2小时以上")
                .actionItems(List.of(
                    "制定详细的每日学习计划",
                    "设置学习提醒",
                    "利用碎片时间进行复习"
                ))
                .build());
        }
      
        // 2. 基于知识盲区的建议
        if (!knowledgeGaps.isEmpty()) {
            KnowledgeGap primaryGap = knowledgeGaps.get(0);
            suggestions.add(ImprovementSuggestion.builder()
                .type(SuggestionType.KNOWLEDGE_REINFORCEMENT)
                .priority(Priority.HIGH)
                .title("重点攻克薄弱知识点")
                .description(String.format("您在'%s'方面掌握不够，建议重点学习", 
                           primaryGap.getKnowledgePointName()))
                .actionItems(List.of(
                    "重新学习相关理论知识",
                    "完成专项练习题",
                    "寻找相关案例进行分析"
                ))
                .relatedKnowledgePoints(List.of(primaryGap.getKnowledgePointName()))
                .build());
        }
      
        // 3. 基于通过率预测的建议
        if (passPrediction.getProbability() < 0.7) {
            suggestions.add(ImprovementSuggestion.builder()
                .type(SuggestionType.EXAM_PREPARATION)
                .priority(Priority.URGENT)
                .title("强化考试准备")
                .description("根据预测，您当前的考试通过概率较低，需要加强准备")
                .actionItems(List.of(
                    "增加真题练习量",
                    "参加模拟考试",
                    "重点复习高频考点",
                    "制定冲刺学习计划"
                ))
                .build());
        }
      
        return suggestions;
    }
}

// MachineLearningService.java
@Service
@Slf4j
public class MachineLearningService {
  
    private final PythonMLServiceClient pythonMLClient;
    private final ModelRepository modelRepository;
    private final FeatureEngineeringService featureService;
  
    /**
     * 执行预测
     */
    public MLPredictionResponse predict(MLPredictionRequest request) {
        try {
            // 1. 验证模型是否存在
            MLModel model = modelRepository.findByName(request.getModelName())
                .orElseThrow(() -> new BusinessException("模型不存在: " + request.getModelName()));
          
            // 2. 特征预处理
            List<Double> processedFeatures = featureService.preprocessFeatures(
                request.getFeatures(), model.getFeatureConfig());
          
            // 3. 调用Python ML服务
            PythonMLRequest pythonRequest = PythonMLRequest.builder()
                .modelPath(model.getModelPath())
                .features(processedFeatures)
                .modelType(model.getModelType())
                .build();
          
            PythonMLResponse pythonResponse = pythonMLClient.predict(pythonRequest);
          
            // 4. 后处理预测结果
            return MLPredictionResponse.builder()
                .prediction(pythonResponse.getPrediction())
                .probability(pythonResponse.getProbability())
                .confidence(pythonResponse.getConfidence())
                .featureImportance(pythonResponse.getFeatureImportance())
                .build();
              
        } catch (Exception e) {
            log.error("ML预测服务异常: ", e);
            throw new BusinessException("预测服务暂时不可用");
        }
    }
  
    /**
     * 在线学习模型更新
     */
    @Scheduled(fixedDelay = 3600000) // 每小时执行一次
    public void updateModelsOnline() {
        List<MLModel> models = modelRepository.findByUpdateStrategy(UpdateStrategy.ONLINE);
      
        for (MLModel model : models) {
            try {
                // 1. 获取新的训练数据
                List<TrainingInstance> newData = collectNewTrainingData(model);
              
                if (newData.size() >= model.getMinUpdateBatchSize()) {
                    // 2. 增量更新模型
                    incrementalModelUpdate(model, newData);
                  
                    // 3. 验证模型性能
                    ModelPerformanceMetrics performance = validateModelPerformance(model);
                  
                    // 4. 如果性能下降，回滚到上一版本
                    if (performance.getAccuracy() < model.getMinAccuracyThreshold()) {
                        rollbackModel(model);
                        log.warn("模型性能下降，已回滚: {}", model.getName());
                    } else {
                        // 5. 更新模型元数据
                        updateModelMetadata(model, performance);
                        log.info("模型更新成功: {}", model.getName());
                    }
                }
              
            } catch (Exception e) {
                log.error("模型在线更新失败: {}", model.getName(), e);
            }
        }
    }
  
    /**
     * 增量更新模型
     */
    private void incrementalModelUpdate(MLModel model, List<TrainingInstance> newData) {
        IncrementalUpdateRequest request = IncrementalUpdateRequest.builder()
            .modelPath(model.getModelPath())
            .newTrainingData(newData)
            .learningRate(model.getLearningRate())
            .regularizationParam(model.getRegularizationParam())
            .build();
      
        pythonMLClient.incrementalUpdate(request);
    }
}
```

## 5. 数据库设计

### 5.1 核心表结构
```sql
-- 用户表
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    phone_number VARCHAR(20),
    real_name VARCHAR(100),
    status ENUM('ACTIVE', 'INACTIVE', 'SUSPENDED') DEFAULT 'ACTIVE',
    avatar_url VARCHAR(500),
    last_login_time TIMESTAMP,
    last_login_ip VARCHAR(45),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- 用户偏好设置表
CREATE TABLE user_preferences (
    user_id BIGINT PRIMARY KEY,
    daily_study_goal INT DEFAULT 60 COMMENT '每日学习目标(分钟)',
    study_reminder_enabled BOOLEAN DEFAULT TRUE,
    reminder_time TIME DEFAULT '20:00:00',
    preferred_difficulty ENUM('EASY', 'MEDIUM', 'HARD') DEFAULT 'MEDIUM',
    learning_mode ENUM('SYSTEMATIC', 'RANDOM', 'WEAK_POINT_FOCUSED') DEFAULT 'SYSTEMATIC',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 题目表
CREATE TABLE questions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    content TEXT NOT NULL,
    type ENUM('SINGLE_CHOICE', 'MULTIPLE_CHOICE', 'TRUE_FALSE', 'CASE_ANALYSIS') NOT NULL,
    difficulty ENUM('EASY', 'MEDIUM', 'HARD') NOT NULL,
    subject_code VARCHAR(20) NOT NULL,
    options JSON COMMENT '题目选项',
    correct_answer VARCHAR(500) NOT NULL,
    explanation TEXT,
    source ENUM('REAL_EXAM', 'SIMULATION', 'PRACTICE') DEFAULT 'PRACTICE',
    exam_year INT,
    question_number INT,
    total_attempts INT DEFAULT 0,
    correct_attempts INT DEFAULT 0,
    review_status ENUM('PENDING', 'APPROVED', 'REJECTED') DEFAULT 'PENDING',
    reviewer_id BIGINT,
    review_comment TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
    INDEX idx_subject_difficulty (subject_code, difficulty),
    INDEX idx_source_year (source, exam_year),
    INDEX idx_review_status (review_status),
    INDEX idx_correct_rate ((correct_attempts / NULLIF(total_attempts, 0))),
  
    FOREIGN KEY (reviewer_id) REFERENCES users(id)
);

-- 题目知识点关联表
CREATE TABLE question_knowledge_points (
    question_id BIGINT,
    knowledge_point VARCHAR(100),
  
    PRIMARY KEY (question_id, knowledge_point),
    FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE,
    INDEX idx_knowledge_point (knowledge_point)
);

-- 用户答题记录表
CREATE TABLE user_answers (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    question_id BIGINT NOT NULL,
    user_answer VARCHAR(500) NOT NULL,
    is_correct BOOLEAN NOT NULL,
    time_spent INT COMMENT '答题用时(秒)',
    answer_mode ENUM('PRACTICE', 'EXAM', 'REVIEW') DEFAULT 'PRACTICE',
    session_id VARCHAR(50),
    answered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
    INDEX idx_user_question (user_id, question_id),
    INDEX idx_user_time (user_id, answered_at),
    INDEX idx_session (session_id),
    INDEX idx_correct_time (user_id, is_correct, answered_at),
  
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE
);

-- 学习会话表
CREATE TABLE study_sessions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    subject_code VARCHAR(20),
    session_type ENUM('PRACTICE', 'REVIEW', 'EXAM') DEFAULT 'PRACTICE',
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    total_questions INT DEFAULT 0,
    correct_questions INT DEFAULT 0,
    session_data JSON COMMENT '会话详细数据',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
    INDEX idx_user_time (user_id, start_time),
    INDEX idx_subject_type (subject_code, session_type),
  
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 学习进度表
CREATE TABLE learning_progress (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    subject_code VARCHAR(20) NOT NULL,
    knowledge_point VARCHAR(100) NOT NULL,
    mastery_level DECIMAL(5,2) DEFAULT 0.00 COMMENT '掌握程度(0-100)',
    last_studied_at TIMESTAMP,
    study_count INT DEFAULT 0,
    correct_count INT DEFAULT 0,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
    UNIQUE KEY uk_user_subject_knowledge (user_id, subject_code, knowledge_point),
    INDEX idx_user_subject (user_id, subject_code),
    INDEX idx_mastery_level (mastery_level),
  
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- AI对话记录表
CREATE TABLE ai_conversations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    conversation_id VARCHAR(50) NOT NULL,
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    question_intent ENUM('DEFINITION', 'HOW_TO', 'WHY', 'COMPARISON', 'EXAMPLE', 'PROBLEM_SOLVING', 'GENERAL'),
    confidence_score DECIMAL(3,2),
    feedback_rating INT COMMENT '用户反馈评分(1-5)',
    feedback_comment TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
    INDEX idx_user_conversation (user_id, conversation_id),
    INDEX idx_intent (question_intent),
    INDEX idx_created_at (created_at),
  
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 学习推荐记录表
CREATE TABLE learning_recommendations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    recommendation_type ENUM('QUESTION', 'KNOWLEDGE_POINT', 'STUDY_PATH', 'DIFFICULTY_ADJUSTMENT'),
    content_id BIGINT,
    content_type VARCHAR(50),
    recommendation_reason TEXT,
    confidence_score DECIMAL(3,2),
    is_accepted BOOLEAN,
    user_feedback TEXT,
继续完成数据库设计：

```sql
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
    INDEX idx_user_type (user_id, recommendation_type),
    INDEX idx_confidence (confidence_score),
    INDEX idx_created_at (created_at),
  
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 考试试卷表
CREATE TABLE exam_papers (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    subject_code VARCHAR(20) NOT NULL,
    paper_type ENUM('REAL_EXAM', 'SIMULATION', 'PRACTICE', 'INTELLIGENT') NOT NULL,
    duration_minutes INT NOT NULL,
    total_score INT NOT NULL,
    passing_score INT NOT NULL,
    question_config JSON COMMENT '题目配置信息',
    status ENUM('ACTIVE', 'INACTIVE', 'ARCHIVED') DEFAULT 'ACTIVE',
    created_by BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
    INDEX idx_subject_type (subject_code, paper_type),
    INDEX idx_status (status),
    INDEX idx_created_by (created_by),
  
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 试卷题目关联表
CREATE TABLE paper_questions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    paper_id BIGINT NOT NULL,
    question_id BIGINT NOT NULL,
    question_order INT NOT NULL,
    score INT NOT NULL DEFAULT 1,
  
    UNIQUE KEY uk_paper_question (paper_id, question_id),
    INDEX idx_paper_order (paper_id, question_order),
  
    FOREIGN KEY (paper_id) REFERENCES exam_papers(id) ON DELETE CASCADE,
    FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE
);

-- 考试记录表
CREATE TABLE exam_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    paper_id BIGINT NOT NULL,
    session_id VARCHAR(50) UNIQUE NOT NULL,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    submit_time TIMESTAMP,
    total_score INT,
    user_score INT,
    correct_count INT DEFAULT 0,
    total_questions INT DEFAULT 0,
    is_passed BOOLEAN,
    time_spent_minutes INT,
    exam_data JSON COMMENT '考试详细数据',
    status ENUM('IN_PROGRESS', 'COMPLETED', 'TIMEOUT', 'ABANDONED') DEFAULT 'IN_PROGRESS',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
    INDEX idx_user_paper (user_id, paper_id),
    INDEX idx_session (session_id),
    INDEX idx_status_time (status, start_time),
    INDEX idx_score (user_score),
  
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (paper_id) REFERENCES exam_papers(id)
);

-- 知识点表
CREATE TABLE knowledge_points (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    subject_code VARCHAR(20) NOT NULL,
    parent_id BIGINT COMMENT '父知识点ID',
    level INT DEFAULT 1 COMMENT '知识点层级',
    description TEXT,
    difficulty ENUM('EASY', 'MEDIUM', 'HARD') DEFAULT 'MEDIUM',
    estimated_learning_time INT COMMENT '预估学习时间(分钟)',
    prerequisites JSON COMMENT '前置知识点',
    learning_resources JSON COMMENT '学习资源',
    is_core BOOLEAN DEFAULT FALSE COMMENT '是否核心知识点',
    exam_frequency DECIMAL(3,2) DEFAULT 0.00 COMMENT '考试频率',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
    UNIQUE KEY uk_name_subject (name, subject_code),
    INDEX idx_subject_level (subject_code, level),
    INDEX idx_parent (parent_id),
    INDEX idx_core (is_core),
  
    FOREIGN KEY (parent_id) REFERENCES knowledge_points(id)
);

-- 用户学习计划表
CREATE TABLE user_study_plans (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    subject_code VARCHAR(20) NOT NULL,
    plan_name VARCHAR(200) NOT NULL,
    target_exam_date DATE,
    total_study_hours INT,
    daily_study_minutes INT DEFAULT 60,
    plan_data JSON COMMENT '详细计划数据',
    status ENUM('ACTIVE', 'COMPLETED', 'PAUSED', 'CANCELLED') DEFAULT 'ACTIVE',
    progress DECIMAL(5,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
    INDEX idx_user_subject (user_id, subject_code),
    INDEX idx_status (status),
    INDEX idx_exam_date (target_exam_date),
  
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 学习提醒表
CREATE TABLE study_reminders (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    reminder_type ENUM('DAILY_STUDY', 'REVIEW', 'EXAM_PREPARATION', 'WEAK_POINT') NOT NULL,
    title VARCHAR(200) NOT NULL,
    content TEXT,
    scheduled_time TIMESTAMP NOT NULL,
    is_sent BOOLEAN DEFAULT FALSE,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
    INDEX idx_user_type (user_id, reminder_type),
    INDEX idx_scheduled_sent (scheduled_time, is_sent),
  
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 机器学习模型表
CREATE TABLE ml_models (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    model_type ENUM('CLASSIFICATION', 'REGRESSION', 'CLUSTERING', 'RECOMMENDATION') NOT NULL,
    model_path VARCHAR(500) NOT NULL,
    feature_config JSON,
    hyperparameters JSON,
    performance_metrics JSON,
    version VARCHAR(20) NOT NULL,
    update_strategy ENUM('OFFLINE', 'ONLINE', 'HYBRID') DEFAULT 'OFFLINE',
    min_accuracy_threshold DECIMAL(5,4) DEFAULT 0.8000,
    min_update_batch_size INT DEFAULT 100,
    learning_rate DECIMAL(6,5) DEFAULT 0.01000,
    regularization_param DECIMAL(6,5) DEFAULT 0.00100,
    status ENUM('TRAINING', 'ACTIVE', 'DEPRECATED') DEFAULT 'TRAINING',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
    INDEX idx_type_status (model_type, status),
    INDEX idx_name_version (name, version)
);

-- 用户行为日志表
CREATE TABLE user_behavior_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    session_id VARCHAR(50),
    action_type VARCHAR(50) NOT NULL,
    resource_type VARCHAR(50),
    resource_id BIGINT,
    action_data JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
    INDEX idx_user_action (user_id, action_type),
    INDEX idx_session (session_id),
    INDEX idx_created_at (created_at),
    INDEX idx_resource (resource_type, resource_id),
  
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

### 5.2 Redis缓存设计
```java
// Redis缓存配置
@Configuration
@EnableCaching
public class CacheConfig {
  
    @Bean
    public CacheManager cacheManager(RedisConnectionFactory connectionFactory) {
        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofMinutes(30))
            .serializeKeysWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new GenericJackson2JsonRedisSerializer()));
      
        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();
      
        // 用户信息缓存 - 1小时
        cacheConfigurations.put("users", config.entryTtl(Duration.ofHours(1)));
      
        // 题目缓存 - 30分钟
        cacheConfigurations.put("questions", config.entryTtl(Duration.ofMinutes(30)));
      
        // 学习进度缓存 - 10分钟
        cacheConfigurations.put("learning_progress", config.entryTtl(Duration.ofMinutes(10)));
      
        // 推荐结果缓存 - 15分钟
        cacheConfigurations.put("recommendations", config.entryTtl(Duration.ofMinutes(15)));
      
        // AI对话上下文 - 24小时
        cacheConfigurations.put("ai_context", config.entryTtl(Duration.ofHours(24)));
      
        return RedisCacheManager.builder(connectionFactory)
            .cacheDefaults(config)
            .withInitialCacheConfigurations(cacheConfigurations)
            .build();
    }
}

// 缓存服务
@Service
@Slf4j
public class CacheService {
  
    private final RedisTemplate<String, Object> redisTemplate;
  
    // 缓存键前缀
    private static final String USER_SESSION_KEY = "user:session:";
    private static final String LEARNING_PROGRESS_KEY = "learning:progress:";
    private static final String RECOMMENDATION_KEY = "recommendation:";
    private static final String AI_CONTEXT_KEY = "ai:context:";
  
    /**
     * 缓存用户会话信息
     */
    public void cacheUserSession(Long userId, String accessToken, String refreshToken) {
        String key = USER_SESSION_KEY + userId;
        UserSession session = UserSession.builder()
            .userId(userId)
            .accessToken(accessToken)
            .refreshToken(refreshToken)
            .loginTime(LocalDateTime.now())
            .build();
      
        redisTemplate.opsForValue().set(key, session, Duration.ofHours(24));
    }
  
    /**
     * 缓存学习进度
     */
    public void cacheLearningProgress(Long userId, String subjectCode, LearningProgressSummary progress) {
        String key = LEARNING_PROGRESS_KEY + userId + ":" + subjectCode;
        redisTemplate.opsForValue().set(key, progress, Duration.ofMinutes(10));
    }
  
    /**
     * 缓存推荐结果
     */
    public void cacheRecommendations(Long userId, String type, Object recommendations) {
        String key = RECOMMENDATION_KEY + userId + ":" + type;
        redisTemplate.opsForValue().set(key, recommendations, Duration.ofMinutes(15));
    }
  
    /**
     * 实时更新用户能力评估
     */
    public void updateUserAbilityRealTime(Long userId, String subjectCode, double abilityChange) {
        String key = "ability:realtime:" + userId + ":" + subjectCode;
        String script = """
            local current = redis.call('GET', KEYS[1])
            if current then
                local newValue = tonumber(current) + tonumber(ARGV[1])
                redis.call('SET', KEYS[1], newValue, 'EX', 3600)
                return newValue
            else
                redis.call('SET', KEYS[1], ARGV[1], 'EX', 3600)
                return tonumber(ARGV[1])
            end
            """;
      
        redisTemplate.execute(RedisScript.of(script, Double.class), 
                            List.of(key), String.valueOf(abilityChange));
    }
}
```

## 6. 部署和运维

### 6.1 Docker配置
```dockerfile
# Dockerfile for Spring Boot services
FROM openjdk:17-jdk-slim

LABEL maintainer="software-exam-system"

# 设置工作目录
WORKDIR /app

# 复制jar文件
COPY target/*.jar app.jar

# 创建非root用户
RUN groupadd -r appuser && useradd -r -g appuser appuser
RUN chown -R appuser:appuser /app
USER appuser

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8080/actuator/health || exit 1

# 暴露端口
EXPOSE 8080

# JVM参数优化
ENV JAVA_OPTS="-Xms512m -Xmx2g -XX:+UseG1GC -XX:+PrintGCDetails -XX:+PrintGCTimeStamps"

# 启动应用
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]
```

### 6.2 Kubernetes部署配置
```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-service
  labels:
    app: user-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: user-service
  template:
    metadata:
      labels:
        app: user-service
    spec:
      containers:
      - name: user-service
        image: software-exam/user-service:latest
        ports:
        - containerPort: 8081
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "k8s"
        - name: DB_HOST
          valueFrom:
            configMapKeyRef:
              name: app-config
              key: database.host
        - name: REDIS_HOST
          valueFrom:
            configMapKeyRef:
              name: app-config
              key: redis.host
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8081
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health/readiness
            port: 8081
          initialDelaySeconds: 30
          periodSeconds: 10

---
apiVersion: v1
kind: Service
metadata:
  name: user-service
spec:
  selector:
    app: user-service
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8081
  type: ClusterIP
```

### 6.3 监控配置
```yaml
继续完成监控配置：

```yaml
# prometheus-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s
    
    rule_files:
      - "software_exam_rules.yml"
    
    scrape_configs:
      # Spring Boot服务监控
      - job_name: 'software-exam-services'
        metrics_path: '/actuator/prometheus'
        static_configs:
          - targets: 
            - 'user-service:8081'
            - 'study-service:8082'
            - 'exercise-service:8083'
            - 'ai-service:8084'
            - 'analytics-service:8085'
            - 'notification-service:8086'
        scrape_interval: 10s
      
      # API网关监控
      - job_name: 'api-gateway'
        metrics_path: '/actuator/prometheus'
        static_configs:
          - targets: ['api-gateway:8080']
        scrape_interval: 5s
      
      # Redis监控
      - job_name: 'redis'
        static_configs:
          - targets: ['redis-exporter:9121']
        
      # MySQL监控
      - job_name: 'mysql'
        static_configs:
          - targets: ['mysql-exporter:9104']
        
      # Kafka监控
      - job_name: 'kafka'
        static_configs:
          - targets: ['kafka-exporter:9308']

    alerting:
      alertmanagers:
        - static_configs:
            - targets: ['alertmanager:9093']

---
# 告警规则配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-rules
data:
  software_exam_rules.yml: |
    groups:
    - name: software-exam-alerts
      rules:
      # 服务可用性告警
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "服务 {{ $labels.instance }} 已下线"
          description: "{{ $labels.job }} 服务已下线超过1分钟"
        
      # 响应时间告警
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "{{ $labels.instance }} 响应时间过高"
          description: "95%的请求响应时间超过2秒，持续5分钟"
        
      # 错误率告警
      - alert: HighErrorRate
        expr: (rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m])) > 0.05
        for: 3m
        labels:
          severity: warning
        annotations:
          summary: "{{ $labels.instance }} 错误率过高"
          description: "错误率超过5%，持续3分钟"
        
      # CPU使用率告警
      - alert: HighCpuUsage
        expr: cpu_usage_percent > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "{{ $labels.instance }} CPU使用率过高"
          description: "CPU使用率超过80%，持续5分钟"
        
      # 内存使用率告警
      - alert: HighMemoryUsage
        expr: memory_usage_percent > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "{{ $labels.instance }} 内存使用率过高"
          description: "内存使用率超过85%，持续5分钟"
        
      # 数据库连接数告警
      - alert: HighDatabaseConnections
        expr: database_connections_active / database_connections_max > 0.8
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "数据库连接数过高"
          description: "数据库连接使用率超过80%"
        
      # AI服务响应异常告警
      - alert: AIServiceError
        expr: increase(ai_service_errors_total[5m]) > 10
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "AI服务异常"
          description: "AI服务5分钟内错误超过10次"
```

### 6.4 日志配置
```yaml
# logback-spring.xml
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <springProfile name="!k8s">
        <!-- 开发环境配置 -->
        <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
            <encoder>
                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
            </encoder>
        </appender>
      
        <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>logs/application.log</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
                <fileNamePattern>logs/application.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
                <maxFileSize>100MB</maxFileSize>
                <maxHistory>30</maxHistory>
                <totalSizeCap>1GB</totalSizeCap>
            </rollingPolicy>
            <encoder>
                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
            </encoder>
        </appender>
      
        <root level="INFO">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="FILE"/>
        </root>
    </springProfile>
  
    <springProfile name="k8s">
        <!-- Kubernetes环境配置 - JSON格式便于日志收集 -->
        <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
            <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
                <providers>
                    <timestamp/>
                    <logLevel/>
                    <loggerName/>
                    <message/>
                    <mdc/>
                    <arguments/>
                    <pattern>
                        <pattern>
                            {
                                "service": "${SERVICE_NAME:-unknown}",
                                "instance": "${HOSTNAME:-unknown}",
                                "trace_id": "%X{traceId:-}",
                                "span_id": "%X{spanId:-}"
                            }
                        </pattern>
                    </pattern>
                </providers>
            </encoder>
        </appender>
      
        <root level="INFO">
            <appender-ref ref="STDOUT"/>
        </root>
    </springProfile>
  
    <!-- 特定包的日志级别 -->
    <logger name="com.softwareexam.ai" level="DEBUG"/>
    <logger name="com.softwareexam.analytics" level="DEBUG"/>
    <logger name="org.springframework.security" level="DEBUG"/>
    <logger name="org.springframework.web" level="INFO"/>
    <logger name="org.hibernate.SQL" level="DEBUG"/>
    <logger name="org.hibernate.type.descriptor.sql.BasicBinder" level="TRACE"/>
</configuration>
```

### 6.5 性能优化配置
```java
// JVM调优配置
@Configuration
public class JvmOptimizationConfig {
  
    @PostConstruct
    public void logJvmInfo() {
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
      
        log.info("JVM内存信息 - 最大内存: {}MB, 总内存: {}MB, 空闲内存: {}MB",
                maxMemory / 1024 / 1024,
                totalMemory / 1024 / 1024,
                freeMemory / 1024 / 1024);
              
        // 设置GC相关参数
        System.setProperty("java.awt.headless", "true");
    }
}

// 数据库连接池优化
@Configuration
public class DatabaseOptimizationConfig {
  
    @Bean
    @ConfigurationProperties("spring.datasource.hikari")
    public HikariConfig hikariConfig() {
        HikariConfig config = new HikariConfig();
      
        // 连接池大小优化
        config.setMaximumPoolSize(20);
        config.setMinimumIdle(5);
        config.setConnectionTimeout(30000);
        config.setIdleTimeout(600000);
        config.setMaxLifetime(1800000);
      
        // 性能优化参数
        config.addDataSourceProperty("cachePrepStmts", "true");
        config.addDataSourceProperty("prepStmtCacheSize", "250");
        config.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");
        config.addDataSourceProperty("useServerPrepStmts", "true");
        config.addDataSourceProperty("useLocalSessionState", "true");
        config.addDataSourceProperty("rewriteBatchedStatements", "true");
        config.addDataSourceProperty("cacheResultSetMetadata", "true");
        config.addDataSourceProperty("cacheServerConfiguration", "true");
        config.addDataSourceProperty("elideSetAutoCommits", "true");
        config.addDataSourceProperty("maintainTimeStats", "false");
      
        return config;
    }
}

// Redis连接池优化
@Configuration
public class RedisOptimizationConfig {
  
    @Bean
    public LettuceConnectionFactory redisConnectionFactory() {
        // 连接池配置
        GenericObjectPoolConfig<StatefulRedisConnection<String, String>> poolConfig = 
            new GenericObjectPoolConfig<>();
        poolConfig.setMaxTotal(20);
        poolConfig.setMaxIdle(10);
        poolConfig.setMinIdle(2);
        poolConfig.setMaxWaitMillis(30000);
        poolConfig.setTestOnBorrow(true);
        poolConfig.setTestOnReturn(false);
        poolConfig.setTestWhileIdle(true);
      
        // 客户端配置
        ClientOptions clientOptions = ClientOptions.builder()
            .autoReconnect(true)
            .pingBeforeActivateConnection(true)
            .build();
          
        LettucePoolingClientConfiguration clientConfig = 
            LettucePoolingClientConfiguration.builder()
                .poolConfig(poolConfig)
                .clientOptions(clientOptions)
                .commandTimeout(Duration.ofSeconds(5))
                .build();
      
        RedisStandaloneConfiguration serverConfig = new RedisStandaloneConfiguration();
        serverConfig.setHostName("${spring.redis.host}");
        serverConfig.setPort(Integer.parseInt("${spring.redis.port}"));
        serverConfig.setPassword("${spring.redis.password}");
        serverConfig.setDatabase(Integer.parseInt("${spring.redis.database}"));
      
        return new LettuceConnectionFactory(serverConfig, clientConfig);
    }
}
```

### 6.6 安全配置
```java
// 安全配置
@Configuration
@EnableWebSecurity
@EnableMethodSecurity
public class SecurityConfig {
  
    private final JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint;
    private final JwtAccessDeniedHandler jwtAccessDeniedHandler;
    private final JwtAuthenticationFilter jwtAuthenticationFilter;
  
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            // CSRF配置
            .csrf(csrf -> csrf.disable())
          
            // CORS配置
            .cors(cors -> cors.configurationSource(corsConfigurationSource()))
          
            // Session管理
            .sessionManagement(session -> 
                session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
          
            // 异常处理
            .exceptionHandling(exceptions -> exceptions
                .authenticationEntryPoint(jwtAuthenticationEntryPoint)
                .accessDeniedHandler(jwtAccessDeniedHandler))
          
            // URL授权配置
            .authorizeHttpRequests(authz -> authz
                // 公开接口
                .requestMatchers("/api/v1/users/register", "/api/v1/users/login").permitAll()
                .requestMatchers("/actuator/health/**").permitAll()
                .requestMatchers("/swagger-ui/**", "/v3/api-docs/**").permitAll()
              
                // AI服务接口需要认证
                .requestMatchers("/api/v1/ai/**").hasRole("USER")
              
                // 管理员接口
                .requestMatchers("/api/v1/admin/**").hasRole("ADMIN")
              
                // 其他接口需要认证
                .anyRequest().authenticated())
          
            // JWT过滤器
            .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class)
          
            // 安全头配置
            .headers(headers -> headers
                .frameOptions().deny()
                .contentTypeOptions().and()
                .httpStrictTransportSecurity(hstsConfig -> hstsConfig
                    .maxAgeInSeconds(31536000)
                    .includeSubdomains(true)));
      
        return http.build();
    }
  
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOriginPatterns(Arrays.asList("*"));
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
        configuration.setAllowedHeaders(Arrays.asList("*"));
        configuration.setAllowCredentials(true);
        configuration.setMaxAge(3600L);
      
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }
  
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder(12);
    }
}

// API限流配置
@Configuration
public class RateLimitConfig {
  
    @Bean
    public RedisRateLimiter redisRateLimiter() {
        // 每秒允许10个请求，突发允许20个
        return new RedisRateLimiter(10, 20, 1);
    }
  
    @Bean
    public KeyResolver userKeyResolver() {
        return exchange -> {
            Authentication authentication = exchange.getRequest()
                .getHeaders().getFirst("Authorization");
            if (authentication != null) {
                // 基于用户限流
                return Mono.just(getUserIdFromToken(authentication));
            }
            // 基于IP限流
            return Mono.just(exchange.getRequest().getRemoteAddress().getAddress().getHostAddress());
        };
    }
}
```

这个详细的后端设计方案涵盖了：

1. **微服务架构** - 清晰的服务划分和职责分离
2. **智能化功能** - AI推荐、自然语言处理、机器学习等核心AI能力
3. **数据库设计** - 完整的表结构和索引优化
4. **缓存策略** - Redis多级缓存提升性能
5. **部署运维** - 容器化部署和监控告警
6. **安全防护** - 认证授权和API限流

## 7. 核心算法实现

### 7.1 个性化推荐算法
```java
// 协同过滤推荐算法
@Service
@Slf4j
public class CollaborativeFilteringService {
  
    /**
     * 基于用户的协同过滤推荐
     */
    public List<RecommendationResult> recommendByUserSimilarity(Long userId, String subjectCode, int limit) {
        // 1. 构建用户-题目评分矩阵
        Map<Long, Map<Long, Double>> userItemMatrix = buildUserItemMatrix(subjectCode);
      
        // 2. 计算用户相似度
        Map<Long, Double> userSimilarities = calculateUserSimilarities(userId, userItemMatrix);
      
        // 3. 找到最相似的K个用户
        List<Long> similarUsers = userSimilarities.entrySet().stream()
            .sorted(Map.Entry.<Long, Double>comparingByValue().reversed())
            .limit(20)
            .map(Map.Entry::getKey)
            .collect(Collectors.toList());
      
        // 4. 基于相似用户的行为进行推荐
        Map<Long, Double> recommendationScores = new HashMap<>();
      
        for (Long similarUserId : similarUsers) {
            double similarity = userSimilarities.get(similarUserId);
            Map<Long, Double> similarUserRatings = userItemMatrix.get(similarUserId);
            Map<Long, Double> targetUserRatings = userItemMatrix.get(userId);
          
            for (Map.Entry<Long, Double> entry : similarUserRatings.entrySet()) {
                Long questionId = entry.getKey();
                Double rating = entry.getValue();
              
                // 只推荐目标用户未做过的题目
                if (!targetUserRatings.containsKey(questionId)) {
                    recommendationScores.merge(questionId, similarity * rating, Double::sum);
                }
            }
        }
      
        // 5. 排序并返回推荐结果
        return recommendationScores.entrySet().stream()
            .sorted(Map.Entry.<Long, Double>comparingByValue().reversed())
            .limit(limit)
            .map(this::convertToRecommendationResult)
            .collect(Collectors.toList());
    }
  
    /**
     * 计算用户相似度（皮尔逊相关系数）
     */
    private double calculatePearsonCorrelation(Map<Long, Double> ratings1, Map<Long, Double> ratings2) {
        Set<Long> commonItems = new HashSet<>(ratings1.keySet());
        commonItems.retainAll(ratings2.keySet());
      
        if (commonItems.size() < 2) {
            return 0.0;
        }
      
        double sum1 = 0.0, sum2 = 0.0, sum1Sq = 0.0, sum2Sq = 0.0, pSum = 0.0;
      
        for (Long item : commonItems) {
            double rating1 = ratings1.get(item);
            double rating2 = ratings2.get(item);
          
            sum1 += rating1;
            sum2 += rating2;
            sum1Sq += rating1 * rating1;
            sum2Sq += rating2 * rating2;
            pSum += rating1 * rating2;
        }
      
        double numerator = pSum - (sum1 * sum2 / commonItems.size());
        double denominator = Math.sqrt((sum1Sq - sum1 * sum1 / commonItems.size()) * 
                                     (sum2Sq - sum2 * sum2 / commonItems.size()));
      
        return denominator == 0 ? 0 : numerator / denominator;
    }
  
    /**
     * 矩阵分解推荐算法
     */
    @Async
    public CompletableFuture<List<RecommendationResult>> matrixFactorizationRecommend(
        Long userId, String subjectCode, int limit) {
      
        // 1. 准备训练数据
        List<RatingEntry> ratings = prepareRatingData(subjectCode);
      
        // 2. 矩阵分解
        MatrixFactorizationModel model = trainMatrixFactorization(ratings);
      
        // 3. 为用户生成推荐
        List<RecommendationResult> recommendations = generateRecommendationsFromModel(
            model, userId, limit);
      
        return CompletableFuture.completedFuture(recommendations);
    }
  
    /**
     * 训练矩阵分解模型
     */
    private MatrixFactorizationModel trainMatrixFactorization(List<RatingEntry> ratings) {
        int numUsers = getDistinctUserCount(ratings);
        int numItems = getDistinctItemCount(ratings);
        int factors = 50; // 潜在因子数量
        double learningRate = 0.01;
        double regularization = 0.01;
        int iterations = 100;
      
        // 初始化用户和物品的潜在因子矩阵
        double[][] userFactors = initializeMatrix(numUsers, factors);
        double[][] itemFactors = initializeMatrix(numItems, factors);
      
        // SGD训练
        for (int iter = 0; iter < iterations; iter++) {
            double totalError = 0.0;
          
            for (RatingEntry rating : ratings) {
                int userIdx = rating.getUserIndex();
                int itemIdx = rating.getItemIndex();
                double actualRating = rating.getRating();
              
                // 预测评分
                double predictedRating = dotProduct(userFactors[userIdx], itemFactors[itemIdx]);
                double error = actualRating - predictedRating;
                totalError += error * error;
              
                // 更新因子矩阵
                for (int f = 0; f < factors; f++) {
                    double userFactor = userFactors[userIdx][f];
                    double itemFactor = itemFactors[itemIdx][f];
                  
                    userFactors[userIdx][f] += learningRate * (error * itemFactor - regularization * userFactor);
                    itemFactors[itemIdx][f] += learningRate * (error * userFactor - regularization * itemFactor);
                }
            }
          
            double rmse = Math.sqrt(totalError / ratings.size());
            if (iter % 10 == 0) {
                log.debug("Matrix Factorization - Iteration {}, RMSE: {}", iter, rmse);
            }
          
            // 早停条件
            if (rmse < 0.01) {
                break;
            }
        }
      
        return new MatrixFactorizationModel(userFactors, itemFactors);
    }
}
```

### 7.2 自适应学习算法
```java
// IRT（项目反应理论）模型实现
@Service
@Slf4j
public class IRTModelService {
  
    /**
     * 3PL IRT模型计算正确概率
     * P(θ) = c + (1-c) / (1 + exp(-Da(θ-b)))
     * 其中：θ是能力水平，a是区分度，b是难度，c是猜测参数，D是常数1.7
     */
    public double calculateCorrectProbability(double ability, double discrimination, 
                                            double difficulty, double guessing) {
        double D = 1.7; // IRT常数
        double exponent = -D * discrimination * (ability - difficulty);
        return guessing + (1 - guessing) / (1 + Math.exp(exponent));
    }
  
    /**
     * 估计用户能力水平（最大似然估计）
     */
    public double estimateAbilityLevel(Long userId, String subjectCode) {
        List<UserAnswer> recentAnswers = userAnswerRepository
            .findRecentAnswersByUserIdAndSubject(userId, subjectCode, 50);
      
        if (recentAnswers.isEmpty()) {
            return 0.0; // 默认能力水平
        }
      
        // 准备IRT参数
        List<IRTItemParameters> itemParams = getIRTParameters(recentAnswers);
      
        // 使用牛顿-拉夫逊方法估计能力
        double theta = 0.0; // 初始能力估计
        double tolerance = 0.001;
        int maxIterations = 100;
      
        for (int iter = 0; iter < maxIterations; iter++) {
            double logLikelihoodDerivative = 0.0;
            double logLikelihoodSecondDerivative = 0.0;
          
            for (int i = 0; i < recentAnswers.size(); i++) {
                UserAnswer answer = recentAnswers.get(i);
                IRTItemParameters params = itemParams.get(i);
              
                double prob = calculateCorrectProbability(theta, params.getDiscrimination(),
                                                        params.getDifficulty(), params.getGuessing());
              
                int response = answer.getIsCorrect() ? 1 : 0;
              
                // 一阶导数
                double firstDerivative = params.getDiscrimination() * (response - prob);
                logLikelihoodDerivative += firstDerivative;
              
                // 二阶导数
                double secondDerivative = -params.getDiscrimination() * params.getDiscrimination() * 
                                        prob * (1 - prob);
                logLikelihoodSecondDerivative += secondDerivative;
            }
          
            // 牛顿-拉夫逊更新
            double thetaNew = theta - logLikelihoodDerivative / logLikelihoodSecondDerivative;
          
            if (Math.abs(thetaNew - theta) < tolerance) {
                return thetaNew;
            }
          
            theta = thetaNew;
        }
      
        return theta;
    }
  
    /**
     * 自适应选择下一题
     */
    public Question selectNextQuestion(Long userId, String subjectCode, double targetInformation) {
        double userAbility = estimateAbilityLevel(userId, subjectCode);
      
        // 获取候选题目
        List<Question> candidateQuestions = questionRepository
            .findCandidateQuestionsForAdaptive(subjectCode, userId);
      
        Question bestQuestion = null;
        double maxInformation = 0.0;
      
        for (Question question : candidateQuestions) {
            IRTItemParameters params = getQuestionIRTParameters(question.getId());
            double information = calculateInformation(userAbility, params);
          
            if (information > maxInformation) {
                maxInformation = information;
                bestQuestion = question;
            }
        }
      
        return bestQuestion;
    }
  
    /**
     * 计算题目信息函数
     */
    private double calculateInformation(double ability, IRTItemParameters params) {
        double prob = calculateCorrectProbability(ability, params.getDiscrimination(),
                                                params.getDifficulty(), params.getGuessing());
      
        double discriminationSquared = params.getDiscrimination() * params.getDiscrimination();
        double numerator = discriminationSquared * Math.pow(prob - params.getGuessing(), 2);
        double denominator = prob * (1 - prob) * Math.pow(1 - params.getGuessing(), 2);
      
        return numerator / denominator;
    }
  
    /**
     * CAT（计算机自适应测试）终止条件检查
     */
    public boolean shouldTerminateCAT(Long userId, String sessionId) {
        List<UserAnswer> sessionAnswers = userAnswerRepository
            .findBySessionIdOrderByAnsweredAt(sessionId);
      
        if (sessionAnswers.size() < 5) {
            return false; // 至少答5题
        }
      
        if (sessionAnswers.size() >= 30) {
            return true; // 最多30题
        }
      
        // 计算能力估计的标准误
        double standardError = calculateAbilityStandardError(sessionAnswers);
        return standardError < 0.3; // 精度足够时终止
    }
  
    private double calculateAbilityStandardError(List<UserAnswer> answers) {
        double totalInformation = 0.0;
      
        for (UserAnswer answer : answers) {
            IRTItemParameters params = getQuestionIRTParameters(answer.getQuestionId());
            double userAbility = estimateAbilityFromAnswers(answers);
            double information = calculateInformation(userAbility, params);
            totalInformation += information;
        }
      
        return 1.0 / Math.sqrt(totalInformation);
    }
}
```

### 7.3 知识追踪算法
```java
// 贝叶斯知识追踪（BKT）模型
@Service
@Slf4j
public class BayesianKnowledgeTracingService {
  
    /**
     * BKT模型参数
     */
    @Data
    @AllArgsConstructor
    public static class BKTParameters {
        private double initialKnowledge;    // P(L0) - 初始掌握概率
        private double learningRate;        // P(T) - 学习概率
        private double slipProbability;     // P(S) - 失误概率
        private double guessProbability;    // P(G) - 猜测概率
    }
  
    /**
     * 更新知识状态
     */
    public double updateKnowledgeState(String knowledgePoint, Long userId, boolean isCorrect) {
        // 获取当前知识状态
        double currentKnowledge = getCurrentKnowledgeState(userId, knowledgePoint);
      
        // 获取BKT参数
        BKTParameters params = getBKTParameters(knowledgePoint);
      
        // 计算观察到正确答案的概率
        double probCorrect = currentKnowledge * (1 - params.getSlipProbability()) + 
                           (1 - currentKnowledge) * params.getGuessProbability();
      
        double newKnowledge;
      
        if (isCorrect) {
            // 贝叶斯更新：答对的情况
            newKnowledge = (currentKnowledge * (1 - params.getSlipProbability())) / probCorrect;
        } else {
            // 贝叶斯更新：答错的情况
            double probIncorrect = 1 - probCorrect;
            newKnowledge = (currentKnowledge * params.getSlipProbability()) / probIncorrect;
        }
      
        // 考虑学习效应
        newKnowledge = newKnowledge + (1 - newKnowledge) * params.getLearningRate();
      
        // 保存更新后的知识状态
        saveKnowledgeState(userId, knowledgePoint, newKnowledge);
      
        return newKnowledge;
    }
  
   继续完成知识追踪算法：

```java
    /**
     * 预测答题正确概率
     */
    public double predictCorrectProbability(String knowledgePoint, Long userId) {
        double knowledgeState = getCurrentKnowledgeState(userId, knowledgePoint);
        BKTParameters params = getBKTParameters(knowledgePoint);
      
        return knowledgeState * (1 - params.getSlipProbability()) + 
               (1 - knowledgeState) * params.getGuessProbability();
    }
  
    /**
     * 深度知识追踪（DKT）神经网络模型
     */
    @Service
    public static class DeepKnowledgeTracingService {
      
        private final PythonMLServiceClient mlServiceClient;
      
        /**
         * 使用LSTM预测知识状态
         */
        public KnowledgeStateprediction predictKnowledgeState(Long userId, String subjectCode) {
            // 1. 构建用户学习序列
            List<LearningInteraction> learningSequence = buildLearningSequence(userId, subjectCode);
          
            // 2. 特征工程
            List<List<Double>> sequenceFeatures = extractSequenceFeatures(learningSequence);
          
            // 3. 调用DKT模型
            DKTRequest request = DKTRequest.builder()
                .userId(userId)
                .subjectCode(subjectCode)
                .sequenceFeatures(sequenceFeatures)
                .maxSequenceLength(100)
                .build();
          
            DKTResponse response = mlServiceClient.predictDKT(request);
          
            // 4. 解析预测结果
            return KnowledgeStateRegion.builder()
                .userId(userId)
                .subjectCode(subjectCode)
                .knowledgeStates(response.getKnowledgeStates())
                .nextQuestionProbabilities(response.getNextQuestionProbabilities())
                .confidence(response.getConfidence())
                .build();
        }
      
        /**
         * 构建学习交互序列
         */
        private List<LearningInteraction> buildLearningSequence(Long userId, String subjectCode) {
            List<UserAnswer> answers = userAnswerRepository
                .findByUserIdAndSubjectCodeOrderByAnsweredAt(userId, subjectCode);
          
            return answers.stream()
                .map(this::convertToLearningInteraction)
                .collect(Collectors.toList());
        }
      
        /**
         * 提取序列特征
         */
        private List<List<Double>> extractSequenceFeatures(List<LearningInteraction> interactions) {
            return interactions.stream()
                .map(interaction -> Arrays.asList(
                    (double) interaction.getQuestionId(),
                    interaction.isCorrect() ? 1.0 : 0.0,
                    (double) interaction.getTimeSpent(),
                    interaction.getDifficultyLevel(),
                    (double) interaction.getAttemptNumber()
                ))
                .collect(Collectors.toList());
        }
    }
}
```

### 7.4 学习路径优化算法
```java
// 学习路径优化服务
@Service
@Slf4j
public class LearningPathOptimizationService {
  
    /**
     * 基于遗传算法的学习路径优化
     */
    public OptimalLearningPath optimizeLearningPath(Long userId, String subjectCode, 
                                                   List<String> targetKnowledgePoints) {
      
        // 1. 初始化种群
        List<Chromosome<LearningPath>> population = initializePopulation(
            targetKnowledgePoints, 50);
      
        int generations = 100;
        double mutationRate = 0.01;
        double crossoverRate = 0.8;
      
        for (int generation = 0; generation < generations; generation++) {
            // 2. 计算适应度
            evaluateFitness(population, userId, subjectCode);
          
            // 3. 选择
            List<Chromosome<LearningPath>> parents = selection(population);
          
            // 4. 交叉
            List<Chromosome<LearningPath>> offspring = crossover(parents, crossoverRate);
          
            // 5. 变异
            mutate(offspring, mutationRate);
          
            // 6. 替换
            population = replacement(population, offspring);
          
            // 记录最佳个体
            if (generation % 10 == 0) {
                Chromosome<LearningPath> best = getBestChromosome(population);
                log.debug("Generation {}: Best fitness = {}", generation, best.getFitness());
            }
        }
      
        // 返回最优解
        Chromosome<LearningPath> bestSolution = getBestChromosome(population);
        return convertToOptimalLearningPath(bestSolution, userId, subjectCode);
    }
  
    /**
     * 计算学习路径适应度
     */
    private double calculatePathFitness(LearningPath path, Long userId, String subjectCode) {
        double totalFitness = 0.0;
      
        // 1. 学习效率评分（30%）
        double efficiencyScore = calculateLearningEfficiency(path, userId);
        totalFitness += efficiencyScore * 0.3;
      
        // 2. 知识依赖性评分（25%）
        double dependencyScore = calculateDependencyScore(path);
        totalFitness += dependencyScore * 0.25;
      
        // 3. 难度渐进性评分（20%）
        double difficultyScore = calculateDifficultyProgression(path);
        totalFitness += difficultyScore * 0.2;
      
        // 4. 用户偏好匹配度（15%）
        double preferenceScore = calculatePreferenceMatch(path, userId);
        totalFitness += preferenceScore * 0.15;
      
        // 5. 时间约束满足度（10%）
        double timeScore = calculateTimeConstraintScore(path, userId);
        totalFitness += timeScore * 0.1;
      
        return totalFitness;
    }
  
    /**
     * 强化学习路径优化
     */
    @Service
    public static class ReinforcementLearningPathOptimizer {
      
        /**
         * Q-Learning算法优化学习路径
         */
        public LearningPathOptimizationResult optimizeWithQLearning(
            Long userId, String subjectCode, int maxSteps) {
          
            // 1. 初始化Q表
            Map<StateActionPair, Double> qTable = initializeQTable();
          
            // 2. 设置参数
            double learningRate = 0.1;
            double discountFactor = 0.9;
            double explorationRate = 0.1;
          
            // 3. 训练过程
            for (int episode = 0; episode < 1000; episode++) {
                LearningState currentState = getInitialState(userId, subjectCode);
              
                for (int step = 0; step < maxSteps; step++) {
                    // 选择动作（ε-贪心策略）
                    LearningAction action = selectAction(currentState, qTable, explorationRate);
                  
                    // 执行动作并获得奖励
                    ActionResult result = executeAction(currentState, action);
                    LearningState nextState = result.getNextState();
                    double reward = result.getReward();
                  
                    // 更新Q值
                    StateActionPair stateActionPair = new StateActionPair(currentState, action);
                    double currentQ = qTable.getOrDefault(stateActionPair, 0.0);
                    double maxNextQ = getMaxQValue(nextState, qTable);
                  
                    double newQ = currentQ + learningRate * 
                                (reward + discountFactor * maxNextQ - currentQ);
                    qTable.put(stateActionPair, newQ);
                  
                    currentState = nextState;
                  
                    // 检查终止条件
                    if (isTerminalState(currentState)) {
                        break;
                    }
                }
              
                // 逐渐减少探索率
                explorationRate = Math.max(0.01, explorationRate * 0.995);
            }
          
            // 4. 提取最优策略
            return extractOptimalPolicy(qTable, userId, subjectCode);
        }
      
        /**
         * 计算奖励函数
         */
        private double calculateReward(LearningState state, LearningAction action, 
                                     LearningState nextState) {
            double reward = 0.0;
          
            // 学习进度奖励
            double progressImprovement = nextState.getOverallProgress() - state.getOverallProgress();
            reward += progressImprovement * 10;
          
            // 知识掌握奖励
            double masteryImprovement = nextState.getAverageMastery() - state.getAverageMastery();
            reward += masteryImprovement * 15;
          
            // 时间效率奖励
            double timeEfficiency = action.getExpectedLearningGain() / action.getTimeRequired();
            reward += timeEfficiency * 5;
          
            // 难度适应性奖励
            double difficultyMatch = calculateDifficultyMatch(action.getDifficulty(), 
                                                           state.getUserAbilityLevel());
            reward += difficultyMatch * 3;
          
            // 惩罚项
            if (action.violatesPrerequisites()) {
                reward -= 20; // 违反前置条件的重惩罚
            }
          
            if (action.isRepetitive()) {
                reward -= 5; // 重复学习的轻惩罚
            }
          
            return reward;
        }
    }
}
```

### 7.5 实时学习分析算法
```java
// 实时学习分析服务
@Service
@Slf4j
public class RealTimeLearningAnalyticsService {
  
    private final KafkaTemplate<String, Object> kafkaTemplate;
    private final RedisTemplate<String, Object> redisTemplate;
  
    /**
     * 实时处理学习事件
     */
    @KafkaListener(topics = "learning-events")
    public void processLearningEvent(LearningEvent event) {
        try {
            // 1. 实时更新用户画像
            updateUserProfileRealTime(event);
          
            // 2. 检测学习异常
            detectLearningAnomalies(event);
          
            // 3. 更新推荐模型
            updateRecommendationModel(event);
          
            // 4. 触发实时通知
            triggerRealTimeNotifications(event);
          
        } catch (Exception e) {
            log.error("处理学习事件失败: {}", event, e);
        }
    }
  
    /**
     * 实时学习状态追踪
     */
    public void trackLearningStateRealTime(Long userId, LearningInteraction interaction) {
        String stateKey = "learning:realtime:" + userId;
      
        // 使用Redis Streams记录学习轨迹
        Map<String, String> interactionData = Map.of(
            "timestamp", String.valueOf(System.currentTimeMillis()),
            "questionId", String.valueOf(interaction.getQuestionId()),
            "isCorrect", String.valueOf(interaction.isCorrect()),
            "timeSpent", String.valueOf(interaction.getTimeSpent()),
            "difficulty", interaction.getDifficulty().name()
        );
      
        redisTemplate.opsForStream().add(stateKey, interactionData);
      
        // 保持最近1000条记录
        redisTemplate.opsForStream().trim(stateKey, 1000);
      
        // 实时计算学习指标
        LearningMetrics metrics = calculateRealTimeMetrics(userId);
        cacheRealTimeMetrics(userId, metrics);
    }
  
    /**
     * 流式学习模式识别
     */
    @Component
    public static class StreamingPatternRecognition {
      
        /**
         * 滑动窗口异常检测
         */
        public boolean detectAnomalyInSlidingWindow(List<LearningInteraction> window) {
            if (window.size() < 5) return false;
          
            // 1. 计算窗口内统计指标
            double avgAccuracy = calculateAverageAccuracy(window);
            double avgTimeSpent = calculateAverageTimeSpent(window);
            double accuracyVariance = calculateAccuracyVariance(window);
          
            // 2. 与历史基线比较
            LearningBaseline baseline = getUserLearningBaseline(window.get(0).getUserId());
          
            // 3. 异常检测规则
            boolean accuracyAnomaly = Math.abs(avgAccuracy - baseline.getAvgAccuracy()) > 
                                    2 * baseline.getAccuracyStdDev();
          
            boolean timeAnomaly = Math.abs(avgTimeSpent - baseline.getAvgTimeSpent()) > 
                                2 * baseline.getTimeStdDev();
          
            boolean varianceAnomaly = accuracyVariance > baseline.getMaxAccuracyVariance();
          
            return accuracyAnomaly || timeAnomaly || varianceAnomaly;
        }
      
        /**
         * 在线学习模式挖掘
         */
        public LearningPattern mineOnlineLearningPattern(Stream<LearningInteraction> stream) {
            PatternMiner miner = new PatternMiner();
          
            return stream
                .sequential()
                .collect(
                    () -> miner,
                    (accumulator, interaction) -> accumulator.addInteraction(interaction),
                    (accumulator1, accumulator2) -> accumulator1.merge(accumulator2)
                )
                .extractPattern();
        }
    }
  
    /**
     * 实时学习效果预测
     */
    public LearningEffectPrediction predictLearningEffect(Long userId, 
                                                         List<PlannedLearningActivity> plannedActivities) {
      
        // 1. 获取用户当前状态
        UserLearningState currentState = getCurrentLearningState(userId);
      
        // 2. 模拟学习过程
        UserLearningState simulatedState = simulateLearningProcess(currentState, plannedActivities);
      
        // 3. 计算预期改进
        double expectedImprovement = calculateExpectedImprovement(currentState, simulatedState);
      
        // 4. 计算置信度
        double confidence = calculatePredictionConfidence(userId, plannedActivities);
      
        // 5. 识别风险因素
        List<RiskFactor> riskFactors = identifyRiskFactors(currentState, plannedActivities);
      
        return LearningEffectPrediction.builder()
            .userId(userId)
            .currentState(currentState)
            .predictedState(simulatedState)
            .expectedImprovement(expectedImprovement)
            .confidence(confidence)
            .riskFactors(riskFactors)
            .recommendations(generateImprovementRecommendations(riskFactors))
继续完成实时学习分析算法：

```java
            .predictedCompletionTime(calculatePredictedCompletionTime(plannedActivities))
            .build();
    }
  
    /**
     * 蒙特卡洛模拟学习过程
     */
    private UserLearningState simulateLearningProcess(UserLearningState currentState, 
                                                     List<PlannedLearningActivity> activities) {
        int simulations = 1000;
        List<UserLearningState> simulationResults = new ArrayList<>();
      
        for (int i = 0; i < simulations; i++) {
            UserLearningState simulatedState = currentState.copy();
          
            for (PlannedLearningActivity activity : activities) {
                // 模拟学习活动的随机结果
                LearningOutcome outcome = simulateLearningActivity(simulatedState, activity);
                simulatedState = applyLearningOutcome(simulatedState, outcome);
            }
          
            simulationResults.add(simulatedState);
        }
      
        // 计算平均结果
        return calculateAverageState(simulationResults);
    }
  
    /**
     * 自适应学习节奏调整
     */
    @Service
    public static class AdaptivePaceAdjustmentService {
      
        /**
         * 动态调整学习节奏
         */
        public PaceAdjustmentRecommendation adjustLearningPace(Long userId) {
            // 1. 分析最近的学习表现
            List<LearningSession> recentSessions = getRecentLearningSessions(userId, 7);
            LearningPerformanceMetrics performance = analyzePerformance(recentSessions);
          
            // 2. 检测疲劳程度
            FatigueLevel fatigueLevel = detectFatigueLevel(recentSessions);
          
            // 3. 评估认知负荷
            CognitiveLoad cognitiveLoad = assessCognitiveLoad(recentSessions);
          
            // 4. 生成节奏调整建议
            PaceAdjustmentRecommendation recommendation = new PaceAdjustmentRecommendation();
          
            if (fatigueLevel == FatigueLevel.HIGH) {
                recommendation.setRecommendedAction(PaceAction.SLOW_DOWN);
                recommendation.setReason("检测到学习疲劳，建议降低学习强度");
                recommendation.setSuggestedBreakDuration(Duration.ofMinutes(30));
            } else if (performance.getAccuracyTrend() < -0.1) {
                recommendation.setRecommendedAction(PaceAction.REVIEW_BASICS);
                recommendation.setReason("正确率下降趋势明显，建议复习基础知识");
            } else if (performance.getSpeedTrend() > 0.2 && performance.getAccuracy() > 0.8) {
                recommendation.setRecommendedAction(PaceAction.INCREASE_DIFFICULTY);
                recommendation.setReason("学习效率良好，可以增加难度");
            }
          
            return recommendation;
        }
      
        /**
         * 疲劳检测算法
         */
        private FatigueLevel detectFatigueLevel(List<LearningSession> sessions) {
            if (sessions.isEmpty()) return FatigueLevel.LOW;
          
            // 计算多个疲劳指标
            double avgAccuracyDecline = calculateAccuracyDecline(sessions);
            double avgResponseTimeIncrease = calculateResponseTimeIncrease(sessions);
            double sessionLengthTrend = calculateSessionLengthTrend(sessions);
            double errorPatternComplexity = calculateErrorPatternComplexity(sessions);
          
            // 疲劳评分加权计算
            double fatigueScore = 
                avgAccuracyDecline * 0.3 +
                avgResponseTimeIncrease * 0.25 +
                sessionLengthTrend * 0.2 +
                errorPatternComplexity * 0.25;
          
            if (fatigueScore > 0.7) return FatigueLevel.HIGH;
            if (fatigueScore > 0.4) return FatigueLevel.MEDIUM;
            return FatigueLevel.LOW;
        }
      
        /**
         * 认知负荷评估
         */
        private CognitiveLoad assessCognitiveLoad(List<LearningSession> sessions) {
            CognitiveLoadCalculator calculator = new CognitiveLoadCalculator();
          
            for (LearningSession session : sessions) {
                // 内在认知负荷（材料本身的复杂度）
                double intrinsicLoad = calculateIntrinsicLoad(session.getQuestions());
              
                // 外在认知负荷（呈现方式的复杂度）
                double extraneousLoad = calculateExtraneousLoad(session.getPresentationMode());
              
                // 相关认知负荷（构建知识结构的负荷）
                double germaneLoad = calculateGermaneLoad(session.getLearningProgress());
              
                calculator.addMeasurement(intrinsicLoad, extraneousLoad, germaneLoad);
            }
          
            return calculator.calculateOverallLoad();
        }
    }
  
    /**
     * 元认知策略推荐
     */
    @Service
    public static class MetacognitiveStrategyRecommender {
      
        /**
         * 推荐元认知策略
         */
        public List<MetacognitiveStrategy> recommendStrategies(Long userId) {
            // 1. 分析用户的元认知能力
            MetacognitiveProfile profile = analyzeMetacognitiveProfile(userId);
          
            // 2. 识别薄弱的元认知技能
            List<MetacognitiveSkill> weakSkills = identifyWeakMetacognitiveSkills(profile);
          
            // 3. 为每个薄弱技能推荐策略
            List<MetacognitiveStrategy> strategies = new ArrayList<>();
          
            for (MetacognitiveSkill skill : weakSkills) {
                switch (skill) {
                    case PLANNING:
                        strategies.add(MetacognitiveStrategy.builder()
                            .name("学习计划制定策略")
                            .description("在开始学习前，花5分钟制定具体的学习目标和计划")
                            .implementation("使用SMART目标法则设定学习目标")
                            .expectedBenefit("提高学习效率20-30%")
                            .build());
                        break;
                      
                    case MONITORING:
                        strategies.add(MetacognitiveStrategy.builder()
                            .name("学习进度监控策略")
                            .description("定期检查学习进度，评估理解程度")
                            .implementation("每学习15分钟，停下来问自己3个问题：我学到了什么？我理解了吗？还有什么不清楚？")
                            .expectedBenefit("及时发现并纠正学习偏差")
                            .build());
                        break;
                      
                    case EVALUATION:
                        strategies.add(MetacognitiveStrategy.builder()
                            .name("学习效果评估策略")
                            .description("学习结束后反思学习效果和改进方向")
                            .implementation("使用学习日志记录每次学习的收获和困难")
                            .expectedBenefit("持续改进学习方法")
                            .build());
                        break;
                      
                    case REGULATION:
                        strategies.add(MetacognitiveStrategy.builder()
                            .name("学习策略调节")
                            .description("根据学习效果动态调整学习方法")
                            .implementation("如果某种方法效果不好，及时尝试其他方法")
                            .expectedBenefit("找到最适合的学习方式")
                            .build());
                        break;
                }
            }
          
            return strategies;
        }
      
        /**
         * 分析元认知档案
         */
        private MetacognitiveProfile analyzeMetacognitiveProfile(Long userId) {
            // 获取用户的学习行为数据
            List<LearningSession> sessions = getRecentLearningSessions(userId, 30);
          
            MetacognitiveProfile profile = new MetacognitiveProfile();
          
            // 计划能力评估
            double planningScore = assessPlanningAbility(sessions);
            profile.setPlanningAbility(planningScore);
          
            // 监控能力评估
            double monitoringScore = assessMonitoringAbility(sessions);
            profile.setMonitoringAbility(monitoringScore);
          
            // 评估能力评估
            double evaluationScore = assessEvaluationAbility(sessions);
            profile.setEvaluationAbility(evaluationScore);
          
            // 调节能力评估
            double regulationScore = assessRegulationAbility(sessions);
            profile.setRegulationAbility(regulationScore);
          
            return profile;
        }
      
        /**
         * 评估计划能力
         */
        private double assessPlanningAbility(List<LearningSession> sessions) {
            // 分析学习会话的规律性和目标明确性
            double regularityScore = calculateSessionRegularity(sessions);
            double goalClarityScore = calculateGoalClarity(sessions);
            double timeAllocationScore = calculateTimeAllocation(sessions);
          
            return (regularityScore + goalClarityScore + timeAllocationScore) / 3.0;
        }
      
        /**
         * 评估监控能力
         */
        private double assessMonitoringAbility(List<LearningSession> sessions) {
            // 分析学习过程中的自我监控行为
            double selfCheckingFrequency = calculateSelfCheckingFrequency(sessions);
            double difficultyAwareness = calculateDifficultyAwareness(sessions);
            double progressTracking = calculateProgressTracking(sessions);
          
            return (selfCheckingFrequency + difficultyAwareness + progressTracking) / 3.0;
        }
    }
}
```

## 8. 系统集成与优化

### 8.1 服务间通信优化
```java
// 异步消息处理优化
@Configuration
@EnableAsync
public class AsyncConfig implements AsyncConfigurer {
  
    @Override
    @Bean(name = "taskExecutor")
    public Executor getAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(50);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("async-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(30);
        executor.initialize();
        return executor;
    }
  
    @Override
    public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
        return new SimpleAsyncUncaughtExceptionHandler();
    }
}

// 分布式锁实现
@Component
@Slf4j
public class DistributedLockManager {
  
    private final RedisTemplate<String, String> redisTemplate;
    private final String lockKeyPrefix = "lock:";
  
    /**
     * 获取分布式锁
     */
    public boolean acquireLock(String lockKey, String requestId, long expireTime) {
        String key = lockKeyPrefix + lockKey;
        String script = """
            if redis.call('get', KEYS[1]) == ARGV[1] then
                return redis.call('del', KEYS[1])
            else
                return 0
            end
            """;
      
        Boolean result = redisTemplate.execute(
            RedisScript.of(script, Boolean.class),
            Collections.singletonList(key),
            requestId,
            String.valueOf(expireTime)
        );
      
        return Boolean.TRUE.equals(result);
    }
  
    /**
     * 释放分布式锁
     */
    public boolean releaseLock(String lockKey, String requestId) {
        String key = lockKeyPrefix + lockKey;
        String script = """
            if redis.call('get', KEYS[1]) == ARGV[1] then
                return redis.call('del', KEYS[1])
            else
                return 0
            end
            """;
      
        Long result = redisTemplate.execute(
            RedisScript.of(script, Long.class),
            Collections.singletonList(key),
            requestId
        );
      
        return result != null && result > 0;
    }
}
```

### 8.2 性能监控和优化
```java
// 自定义性能监控
@Component
@Slf4j
public class PerformanceMonitor {
  
    private final MeterRegistry meterRegistry;
    private final Timer.Sample sample;
  
    @EventListener
    public void handleMethodExecutionTime(MethodExecutionEvent event) {
        Timer.builder("method.execution.time")
            .tag("class", event.getClassName())
            .tag("method", event.getMethodName())
            .register(meterRegistry)
            .record(event.getExecutionTime(), TimeUnit.MILLISECONDS);
    }
  
    @EventListener
    public void handleDatabaseQueryTime(DatabaseQueryEvent event) {
        Timer.builder("database.query.time")
            .tag("operation", event.getOperation())
            .tag("table", event.getTableName())
            .register(meterRegistry)
            .record(event.getQueryTime(), TimeUnit.MILLISECONDS);
    }
  
    /**
     * 监控AI服务调用
     */
    public void monitorAIServiceCall(String serviceName, Duration responseTime, boolean success) {
        Timer.builder("ai.service.call")
            .tag("service", serviceName)
            .tag("status", success ? "success" : "failure")
            .register(meterRegistry)
            .record(responseTime);
          
        if (!success) {
            Counter.builder("ai.service.errors")
                .tag("service", serviceName)
                .register(meterRegistry)
                .increment();
        }
    }
}
```

这个详细的后端设计方案提供了：

✅ **完整的架构设计** - 微服务架构、技术栈选择、项目结构
✅ **核心服务实现** - 用户服务、题库服务、AI服务等详细代码
✅ **智能算法集成** - 推荐算法、IRT模型、知识追踪、学习路径优化
✅ **数据库设计** - 完整的表结构和索引优化
✅ **缓存策略** - Redis多级缓存配置
✅ **部署运维** - Docker、Kubernetes、监控告警配置
✅ **性能优化** - JVM调优、连接池优化、异步处理
✅ **安全防护** - 认证授权、API限流、分布式锁

这个方案具备了构建现代化智能学习系统的所有核心要素，可以支撑大规模用户的