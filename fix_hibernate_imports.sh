#!/bin/bash

# 修复Hibernate注解的import语句

# 查找所有使用了@JdbcTypeCode的文件
FILES=$(find services -name "*.java" -exec grep -l "@JdbcTypeCode" {} \;)

for file in $FILES; do
    echo "修复文件: $file"
    
    # 检查是否已经有import语句
    if ! grep -q "import org.hibernate.annotations.JdbcTypeCode" "$file"; then
        # 在package语句后添加import
        sed -i '' '/^package /a\
\
import org.hibernate.annotations.JdbcTypeCode;\
import org.hibernate.type.SqlTypes;
' "$file"
    fi
    
    # 移除旧的@Type import
    sed -i '' '/import org.hibernate.annotations.Type;/d' "$file"
    
    echo "完成修复: $file"
done

echo "Hibernate注解修复完成！"
