version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: smartlearn-mysql
    environment:
      MYSQL_ROOT_PASSWORD: smartlearn123
      MYSQL_DATABASE: smartlearn
      MYSQL_USER: smartlearn
      MYSQL_PASSWORD: smartlearn123
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
    networks:
      - smartlearn-network

  # Redis缓存
  redis:
    image: redis:7.0-alpine
    container_name: smartlearn-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - smartlearn-network

  # MongoDB
  mongodb:
    image: mongo:6.0
    container_name: smartlearn-mongodb
    environment:
      MONGO_INITDB_ROOT_USERNAME: smartlearn
      MONGO_INITDB_ROOT_PASSWORD: smartlearn123
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    networks:
      - smartlearn-network

  # Elasticsearch
  elasticsearch:
    image: elasticsearch:8.10.4
    container_name: smartlearn-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - smartlearn-network

  # Kafka
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: smartlearn-zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - smartlearn-network

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: smartlearn-kafka
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
    networks:
      - smartlearn-network

  # Neo4j图数据库
  neo4j:
    image: neo4j:5.13
    container_name: smartlearn-neo4j
    environment:
      NEO4J_AUTH: neo4j/smartlearn123
      NEO4J_PLUGINS: '["apoc"]'
    ports:
      - "7474:7474"
      - "7687:7687"
    volumes:
      - neo4j_data:/data
    networks:
      - smartlearn-network

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: smartlearn-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - smartlearn-network

  # Grafana可视化
  grafana:
    image: grafana/grafana:latest
    container_name: smartlearn-grafana
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: smartlearn123
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - smartlearn-network

  # 服务注册中心
  eureka-server:
    build:
      context: ../
      dockerfile: infrastructure/eureka-server/Dockerfile
    container_name: smartlearn-eureka
    ports:
      - "8761:8761"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
    networks:
      - smartlearn-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8761/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 配置中心
  config-service:
    build:
      context: ../
      dockerfile: infrastructure/config-service/Dockerfile
    container_name: smartlearn-config
    ports:
      - "8888:8888"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE=http://eureka-server:8761/eureka/
      - SPRING_DATASOURCE_URL=**************************************************************************************************************************
      - SPRING_REDIS_HOST=redis
    depends_on:
      - mysql
      - redis
      - eureka-server
    networks:
      - smartlearn-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8888/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # API网关
  gateway-service:
    build:
      context: ../
      dockerfile: infrastructure/gateway-service/Dockerfile
    container_name: smartlearn-gateway
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE=http://eureka-server:8761/eureka/
      - SPRING_REDIS_HOST=redis
    depends_on:
      - redis
      - eureka-server
      - config-service
    networks:
      - smartlearn-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 监控中心
  admin-server:
    build:
      context: ../
      dockerfile: infrastructure/admin-server/Dockerfile
    container_name: smartlearn-admin
    ports:
      - "8090:8090"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE=http://eureka-server:8761/eureka/
      - SPRING_DATASOURCE_URL=*************************************************************************************************************************
      - SPRING_REDIS_HOST=redis
    depends_on:
      - mysql
      - redis
      - eureka-server
    networks:
      - smartlearn-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8090/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 用户服务
  user-service:
    build:
      context: ../
      dockerfile: services/user-service/Dockerfile
    container_name: smartlearn-user-service
    ports:
      - "8081:8081"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE=http://eureka-server:8761/eureka/
      - SPRING_DATASOURCE_URL=************************************************************************************************************************
      - SPRING_REDIS_HOST=redis
    depends_on:
      - mysql
      - redis
      - eureka-server
      - config-service
    networks:
      - smartlearn-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 题库服务
  question-service:
    build:
      context: ../
      dockerfile: services/question-service/Dockerfile
    container_name: smartlearn-question-service
    ports:
      - "8082:8082"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE=http://eureka-server:8761/eureka/
      - SPRING_DATASOURCE_URL=****************************************************************************************************************************
      - SPRING_REDIS_HOST=redis
    depends_on:
      - mysql
      - redis
      - eureka-server
      - config-service
    networks:
      - smartlearn-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8082/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 学习服务
  learning-service:
    build:
      context: ../
      dockerfile: services/learning-service/Dockerfile
    container_name: smartlearn-learning-service
    ports:
      - "8083:8083"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE=http://eureka-server:8761/eureka/
      - SPRING_DATASOURCE_URL=****************************************************************************************************************************
      - SPRING_REDIS_HOST=redis
    depends_on:
      - mysql
      - redis
      - eureka-server
      - config-service
    networks:
      - smartlearn-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8083/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # AI服务
  ai-service:
    build:
      context: ../
      dockerfile: services/ai-service/Dockerfile
    container_name: smartlearn-ai-service
    ports:
      - "8084:8084"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE=http://eureka-server:8761/eureka/
      - SPRING_DATASOURCE_URL=**********************************************************************************************************************
      - SPRING_REDIS_HOST=redis
    depends_on:
      - mysql
      - redis
      - eureka-server
      - config-service
    networks:
      - smartlearn-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8084/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 通知服务
  notification-service:
    build:
      context: ../
      dockerfile: services/notification-service/Dockerfile
    container_name: smartlearn-notification-service
    ports:
      - "8085:8085"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE=http://eureka-server:8761/eureka/
      - SPRING_DATASOURCE_URL=********************************************************************************************************************************
      - SPRING_REDIS_HOST=redis
    depends_on:
      - mysql
      - redis
      - eureka-server
      - config-service
    networks:
      - smartlearn-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8085/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 分析服务
  analytics-service:
    build:
      context: ../
      dockerfile: services/analytics-service/Dockerfile
    container_name: smartlearn-analytics-service
    ports:
      - "8086:8086"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE=http://eureka-server:8761/eureka/
      - SPRING_DATASOURCE_URL=*****************************************************************************************************************************
      - SPRING_REDIS_HOST=redis
    depends_on:
      - mysql
      - redis
      - eureka-server
      - config-service
    networks:
      - smartlearn-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8086/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  mysql_data:
  redis_data:
  mongodb_data:
  elasticsearch_data:
  neo4j_data:
  prometheus_data:
  grafana_data:

networks:
  smartlearn-network:
    driver: bridge
