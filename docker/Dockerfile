# 多阶段构建Dockerfile
FROM openjdk:17-jdk-slim as builder

# 设置工作目录
WORKDIR /app

# 复制Maven包装器和pom文件
COPY .mvn/ .mvn/
COPY mvnw pom.xml ./

# 复制所有模块的pom文件
COPY common/common-core/pom.xml common/common-core/
COPY common/common-security/pom.xml common/common-security/
COPY common/common-database/pom.xml common/common-database/
COPY common/common-message/pom.xml common/common-message/
COPY infrastructure/eureka-server/pom.xml infrastructure/eureka-server/
COPY infrastructure/config-server/pom.xml infrastructure/config-server/
COPY infrastructure/admin-server/pom.xml infrastructure/admin-server/
COPY gateway/pom.xml gateway/
COPY services/user-service/pom.xml services/user-service/
COPY services/study-service/pom.xml services/study-service/
COPY services/exercise-service/pom.xml services/exercise-service/
COPY services/ai-service/pom.xml services/ai-service/
COPY services/analytics-service/pom.xml services/analytics-service/
COPY services/notification-service/pom.xml services/notification-service/

# 下载依赖
RUN ./mvnw dependency:go-offline -B

# 复制源代码
COPY . .

# 构建应用
ARG SERVICE_NAME
RUN ./mvnw clean package -pl ${SERVICE_NAME} -am -DskipTests

# 运行阶段
FROM openjdk:17-jdk-slim

# 安装curl用于健康检查
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

# 创建非root用户
RUN groupadd -r appuser && useradd -r -g appuser appuser

# 设置工作目录
WORKDIR /app

# 复制jar文件
ARG SERVICE_NAME
COPY --from=builder /app/${SERVICE_NAME}/target/*.jar app.jar

# 更改文件所有者
RUN chown -R appuser:appuser /app
USER appuser

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8080/actuator/health || exit 1

# 暴露端口
EXPOSE 8080

# JVM参数优化
ENV JAVA_OPTS="-Xms512m -Xmx2g -XX:+UseG1GC -XX:+PrintGCDetails -XX:+PrintGCTimeStamps"

# 启动应用
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]
