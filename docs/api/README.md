# SmartLearn 智能学习系统 API 文档

## 📋 概述

SmartLearn 智能学习系统提供了完整的 RESTful API，支持用户管理、学习内容管理、智能推荐、数据分析等功能。

## 🌐 API 基础信息

- **基础URL**: `http://localhost:8080` (通过API网关访问)
- **API版本**: v1
- **认证方式**: JWT Bearer <PERSON>ken
- **数据格式**: JSON
- **字符编码**: UTF-8

## 🔐 认证说明

### JWT Token 获取

```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "username": "your_username",
  "password": "your_password"
}
```

### 响应示例

```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresIn": 86400,
    "user": {
      "id": 1,
      "username": "testuser",
      "email": "<EMAIL>",
      "nickname": "测试用户"
    }
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 请求头设置

```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json
```

## 📊 统一响应格式

### 成功响应

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 具体数据
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 错误响应

```json
{
  "code": 400,
  "message": "请求参数错误",
  "data": null,
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 分页响应

```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "content": [
      // 数据列表
    ],
    "totalElements": 100,
    "totalPages": 10,
    "size": 10,
    "number": 0,
    "first": true,
    "last": false
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## 🔢 HTTP 状态码

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误 |
| 401 | 未授权，需要登录 |
| 403 | 禁止访问，权限不足 |
| 404 | 资源不存在 |
| 429 | 请求过于频繁 |
| 500 | 服务器内部错误 |
| 503 | 服务不可用 |

## 📝 API 模块列表

### 1. 用户认证模块 (`/api/v1/auth`)

- [用户注册](./auth.md#用户注册)
- [用户登录](./auth.md#用户登录)
- [刷新Token](./auth.md#刷新token)
- [用户登出](./auth.md#用户登出)
- [忘记密码](./auth.md#忘记密码)
- [重置密码](./auth.md#重置密码)

### 2. 用户管理模块 (`/api/v1/users`)

- [获取用户信息](./users.md#获取用户信息)
- [更新用户资料](./users.md#更新用户资料)
- [修改密码](./users.md#修改密码)
- [用户列表](./users.md#用户列表)
- [用户统计](./users.md#用户统计)

### 3. 题库管理模块 (`/api/v1/questions`)

- [题目列表](./questions.md#题目列表)
- [创建题目](./questions.md#创建题目)
- [更新题目](./questions.md#更新题目)
- [删除题目](./questions.md#删除题目)
- [题目详情](./questions.md#题目详情)
- [题目搜索](./questions.md#题目搜索)

### 4. 考试管理模块 (`/api/v1/exams`)

- [考试列表](./exams.md#考试列表)
- [创建考试](./exams.md#创建考试)
- [开始考试](./exams.md#开始考试)
- [提交答案](./exams.md#提交答案)
- [完成考试](./exams.md#完成考试)
- [考试结果](./exams.md#考试结果)

### 5. 学习管理模块 (`/api/v1/learning`)

- [学习计划](./learning.md#学习计划)
- [学习进度](./learning.md#学习进度)
- [学习记录](./learning.md#学习记录)
- [学习统计](./learning.md#学习统计)

### 6. AI推荐模块 (`/api/v1/ai`)

- [个性化推荐](./ai.md#个性化推荐)
- [能力评估](./ai.md#能力评估)
- [学习路径](./ai.md#学习路径)
- [智能分析](./ai.md#智能分析)

### 7. 数据分析模块 (`/api/v1/analytics`)

- [学习报告](./analytics.md#学习报告)
- [性能分析](./analytics.md#性能分析)
- [趋势分析](./analytics.md#趋势分析)
- [统计数据](./analytics.md#统计数据)

### 8. 通知管理模块 (`/api/v1/notifications`)

- [通知列表](./notifications.md#通知列表)
- [发送通知](./notifications.md#发送通知)
- [标记已读](./notifications.md#标记已读)
- [通知设置](./notifications.md#通知设置)

## 🔄 WebSocket 接口

### 实时通知

```javascript
// 连接WebSocket
const socket = new WebSocket('ws://localhost:8080/ws/notifications');

// 监听消息
socket.onmessage = function(event) {
  const notification = JSON.parse(event.data);
  console.log('收到通知:', notification);
};
```

## 📊 限流说明

为了保护系统稳定性，API实施了限流策略：

| 限流类型 | 限制 | 时间窗口 |
|----------|------|----------|
| IP限流 | 100次/分钟 | 60秒 |
| 用户限流 | 200次/分钟 | 60秒 |
| API限流 | 根据接口而定 | 60秒 |

当触发限流时，会返回 `429 Too Many Requests` 状态码。

## 🛠️ 开发工具

### Postman 集合

我们提供了完整的 Postman 集合，包含所有API接口的示例请求：

- [下载 Postman 集合](./postman/SmartLearn-API.postman_collection.json)
- [下载环境变量](./postman/SmartLearn-Environment.postman_environment.json)

### Swagger UI

在开发环境中，可以通过以下地址访问 Swagger UI：

- **用户服务**: http://localhost:8081/swagger-ui.html
- **题库服务**: http://localhost:8082/swagger-ui.html
- **学习服务**: http://localhost:8083/swagger-ui.html
- **AI服务**: http://localhost:8084/swagger-ui.html
- **分析服务**: http://localhost:8086/swagger-ui.html
- **通知服务**: http://localhost:8085/swagger-ui.html

## 📞 技术支持

如有API使用问题，请联系：

- **邮箱**: <EMAIL>
- **文档**: https://docs.smartlearn.com
- **GitHub**: https://github.com/smartlearn/smartlearn-system

## 📄 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 完整的用户管理功能
- 题库和考试管理
- 学习计划和进度跟踪
- AI智能推荐
- 数据分析和报告
- 实时通知系统
