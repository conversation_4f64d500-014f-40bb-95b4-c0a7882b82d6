# SmartLearn 智能学习系统部署指南

## 📋 概述

本文档详细介绍了 SmartLearn 智能学习系统的部署方法，包括本地开发环境、Docker容器化部署和Kubernetes集群部署。

## 🛠️ 系统要求

### 硬件要求

| 环境 | CPU | 内存 | 存储 | 网络 |
|------|-----|------|------|------|
| 开发环境 | 4核+ | 8GB+ | 50GB+ | 100Mbps+ |
| 测试环境 | 8核+ | 16GB+ | 100GB+ | 1Gbps+ |
| 生产环境 | 16核+ | 32GB+ | 500GB+ | 10Gbps+ |

### 软件要求

- **操作系统**: Linux (Ubuntu 20.04+, CentOS 8+) / macOS / Windows 10+
- **Java**: OpenJDK 17+
- **Maven**: 3.8+
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **Kubernetes**: 1.24+ (生产环境)
- **MySQL**: 8.0+
- **Redis**: 7.0+

## 🏗️ 架构概览

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │    │   API Gateway   │    │  Config Center  │
│    (Nginx)      │────│   (Gateway)     │────│   (Config)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                ┌───────────────┼───────────────┐
                │               │               │
        ┌───────▼──────┐ ┌──────▼──────┐ ┌─────▼──────┐
        │ User Service │ │Question Svc │ │Learning Svc│
        └──────────────┘ └─────────────┘ └────────────┘
                │               │               │
        ┌───────▼──────┐ ┌──────▼──────┐ ┌─────▼──────┐
        │  AI Service  │ │Analytics Svc│ │Notification│
        └──────────────┘ └─────────────┘ └────────────┘
                │               │               │
        ┌───────▼───────────────▼───────────────▼──────┐
        │              Data Layer                      │
        │  ┌─────────┐  ┌─────────┐  ┌─────────┐      │
        │  │  MySQL  │  │  Redis  │  │  Files  │      │
        │  └─────────┘  └─────────┘  └─────────┘      │
        └──────────────────────────────────────────────┘
```

## 🚀 快速开始

### 1. 克隆项目

```bash
git clone https://github.com/smartlearn/smartlearn-system.git
cd smartlearn-system
```

### 2. 环境准备

```bash
# 安装Java 17
sudo apt update
sudo apt install openjdk-17-jdk

# 安装Maven
sudo apt install maven

# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.12.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 3. 构建项目

```bash
# 编译整个项目
mvn clean package -DskipTests

# 或者分模块编译
mvn clean package -pl common/common-core,common/common-security,common/common-database,common/common-message -DskipTests
mvn clean package -pl infrastructure/eureka-server,infrastructure/config-service,infrastructure/gateway-service,infrastructure/admin-server -DskipTests
mvn clean package -pl services/user-service,services/question-service,services/learning-service,services/ai-service,services/analytics-service,services/notification-service -DskipTests
```

## 🐳 Docker 部署

### 1. 使用 Docker Compose (推荐)

```bash
# 进入docker目录
cd docker

# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f [service-name]

# 停止服务
docker-compose down
```

### 2. 服务启动顺序

系统会按照以下顺序自动启动：

1. **基础设施**: MySQL, Redis
2. **注册中心**: Eureka Server
3. **配置中心**: Config Service
4. **网关服务**: Gateway Service
5. **监控中心**: Admin Server
6. **业务服务**: User, Question, Learning, AI, Analytics, Notification

### 3. 健康检查

```bash
# 检查所有服务健康状态
./scripts/health-check.sh

# 或者手动检查
curl http://localhost:8761/actuator/health  # Eureka
curl http://localhost:8888/actuator/health  # Config
curl http://localhost:8080/actuator/health  # Gateway
curl http://localhost:8090/actuator/health  # Admin
```

### 4. 访问地址

| 服务 | 地址 | 用户名/密码 |
|------|------|-------------|
| API网关 | http://localhost:8080 | - |
| 服务注册中心 | http://localhost:8761 | - |
| 配置中心 | http://localhost:8888 | config-admin/config-admin-password |
| 监控中心 | http://localhost:8090 | admin/admin123 |
| Grafana | http://localhost:3000 | admin/smartlearn123 |
| Prometheus | http://localhost:9090 | - |

## ☸️ Kubernetes 部署

### 1. 准备 Kubernetes 集群

```bash
# 使用 minikube (本地测试)
minikube start --memory=8192 --cpus=4

# 或者使用 kind (本地测试)
kind create cluster --config=k8s/kind-config.yaml

# 生产环境请使用云服务商的 Kubernetes 服务
```

### 2. 部署步骤

```bash
# 创建命名空间
kubectl apply -f k8s/namespace.yaml

# 部署配置和密钥
kubectl apply -f k8s/configmap.yaml

# 部署基础设施
kubectl apply -f k8s/mysql.yaml
kubectl apply -f k8s/redis.yaml

# 等待基础设施就绪
kubectl wait --for=condition=ready pod -l app=mysql -n smartlearn --timeout=300s
kubectl wait --for=condition=ready pod -l app=redis -n smartlearn --timeout=300s

# 部署微服务
kubectl apply -f k8s/eureka-server.yaml
kubectl apply -f k8s/config-service.yaml
kubectl apply -f k8s/gateway-service.yaml
kubectl apply -f k8s/admin-server.yaml

# 部署业务服务
kubectl apply -f k8s/user-service.yaml
kubectl apply -f k8s/question-service.yaml
kubectl apply -f k8s/learning-service.yaml
kubectl apply -f k8s/ai-service.yaml
kubectl apply -f k8s/analytics-service.yaml
kubectl apply -f k8s/notification-service.yaml

# 部署 Ingress
kubectl apply -f k8s/ingress.yaml
```

### 3. 监控部署状态

```bash
# 查看所有Pod状态
kubectl get pods -n smartlearn

# 查看服务状态
kubectl get services -n smartlearn

# 查看部署状态
kubectl get deployments -n smartlearn

# 查看Pod日志
kubectl logs -f deployment/user-service -n smartlearn
```

### 4. 扩缩容

```bash
# 扩容用户服务到3个副本
kubectl scale deployment user-service --replicas=3 -n smartlearn

# 自动扩缩容
kubectl apply -f k8s/hpa.yaml
```

## 🔧 配置管理

### 1. 环境配置

系统支持多环境配置：

- **dev**: 开发环境
- **test**: 测试环境
- **staging**: 预发布环境
- **prod**: 生产环境

### 2. 配置文件位置

```
config/
├── application.yml          # 公共配置
├── application-dev.yml      # 开发环境
├── application-test.yml     # 测试环境
├── application-staging.yml  # 预发布环境
└── application-prod.yml     # 生产环境
```

### 3. 动态配置更新

```bash
# 通过配置中心API更新配置
curl -X POST http://localhost:8888/actuator/bus-refresh

# 或者通过管理界面更新
# 访问 http://localhost:8090 进入监控中心
```

## 📊 监控和日志

### 1. 监控指标

- **应用监控**: Spring Boot Actuator + Micrometer
- **系统监控**: Prometheus + Grafana
- **链路追踪**: Zipkin (可选)
- **日志聚合**: ELK Stack (可选)

### 2. 告警配置

```yaml
# 在 k8s/monitoring/alertmanager.yaml 中配置告警规则
groups:
- name: smartlearn-alerts
  rules:
  - alert: ServiceDown
    expr: up == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Service {{ $labels.instance }} is down"
```

### 3. 日志查看

```bash
# Docker环境
docker-compose logs -f user-service

# Kubernetes环境
kubectl logs -f deployment/user-service -n smartlearn

# 实时日志流
kubectl logs -f -l app=user-service -n smartlearn --tail=100
```

## 🔒 安全配置

### 1. 网络安全

- 使用HTTPS/TLS加密
- 配置防火墙规则
- 限制端口访问

### 2. 应用安全

- JWT Token认证
- API限流保护
- 输入验证和过滤
- SQL注入防护

### 3. 数据安全

- 数据库连接加密
- 敏感数据加密存储
- 定期备份
- 访问审计

## 🚨 故障排除

### 1. 常见问题

#### 服务启动失败

```bash
# 检查端口占用
netstat -tulpn | grep :8080

# 检查Java进程
jps -l

# 检查内存使用
free -h
```

#### 数据库连接失败

```bash
# 检查MySQL服务状态
systemctl status mysql

# 测试数据库连接
mysql -h localhost -u smartlearn -p

# 检查网络连通性
telnet localhost 3306
```

#### Redis连接失败

```bash
# 检查Redis服务状态
systemctl status redis

# 测试Redis连接
redis-cli ping

# 检查Redis配置
redis-cli config get "*"
```

### 2. 性能调优

#### JVM调优

```bash
# 在启动脚本中添加JVM参数
export JAVA_OPTS="-Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
```

#### 数据库调优

```sql
-- MySQL配置优化
SET GLOBAL innodb_buffer_pool_size = 2147483648;  -- 2GB
SET GLOBAL max_connections = 1000;
SET GLOBAL query_cache_size = 268435456;  -- 256MB
```

#### Redis调优

```bash
# Redis配置优化
echo 'vm.overcommit_memory = 1' >> /etc/sysctl.conf
echo 'net.core.somaxconn = 65535' >> /etc/sysctl.conf
sysctl -p
```

## 📋 运维脚本

### 1. 启动脚本

```bash
#!/bin/bash
# scripts/start.sh

echo "Starting SmartLearn System..."

# 启动基础设施
docker-compose up -d mysql redis

# 等待基础设施就绪
sleep 30

# 启动微服务
docker-compose up -d

echo "SmartLearn System started successfully!"
```

### 2. 停止脚本

```bash
#!/bin/bash
# scripts/stop.sh

echo "Stopping SmartLearn System..."

# 停止所有服务
docker-compose down

echo "SmartLearn System stopped successfully!"
```

### 3. 备份脚本

```bash
#!/bin/bash
# scripts/backup.sh

BACKUP_DIR="/backup/smartlearn/$(date +%Y%m%d_%H%M%S)"
mkdir -p $BACKUP_DIR

# 备份数据库
mysqldump -h localhost -u root -p smartlearn > $BACKUP_DIR/smartlearn.sql

# 备份Redis数据
cp /var/lib/redis/dump.rdb $BACKUP_DIR/

# 备份配置文件
cp -r config/ $BACKUP_DIR/

echo "Backup completed: $BACKUP_DIR"
```

## 📞 技术支持

如有部署问题，请联系：

- **邮箱**: <EMAIL>
- **文档**: https://docs.smartlearn.com/deployment
- **GitHub Issues**: https://github.com/smartlearn/smartlearn-system/issues

## 📄 更新日志

### v1.0.0 (2024-01-01)
- 初始部署文档
- Docker容器化支持
- Kubernetes集群部署
- 监控和日志配置
- 安全和性能优化指南
