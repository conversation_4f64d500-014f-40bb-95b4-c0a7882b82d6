# SmartLearn System - 智能软考学习系统

## 📋 项目简介

SmartLearn System 是一个基于微服务架构的智能软考学习系统，采用 Spring Boot 3.2+ 和 Java 17 构建，提供完整的在线学习、智能推荐、考试练习等功能。

## 🏗️ 系统架构

### 微服务架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Gateway   │    │  Eureka Server  │    │  Config Server  │
│     (8080)      │    │     (8761)      │    │     (8888)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
    ┌────────────────────────────┼────────────────────────────┐
    │                            │                            │
┌─────────┐  ┌─────────┐  ┌─────────┐  ┌─────────┐  ┌─────────┐  ┌─────────┐
│  User   │  │ Study   │  │Exercise │  │   AI    │  │Analytics│  │Notification│
│Service  │  │Service  │  │Service  │  │Service  │  │Service  │  │ Service │
│ (8081)  │  │ (8082)  │  │ (8083)  │  │ (8084)  │  │ (8085)  │  │ (8086)  │
└─────────┘  └─────────┘  └─────────┘  └─────────┘  └─────────┘  └─────────┘
```

### 技术栈

- **框架**: Spring Boot 3.2+, Spring Cloud 2023.0.0
- **语言**: Java 17
- **数据库**: MySQL 8.0+ (主库), Redis 7.0+ (缓存), MongoDB 6.0+ (分析), Neo4j 5.13+ (知识图谱)
- **搜索**: Elasticsearch 8.10+
- **消息队列**: Apache Kafka 3.5+
- **安全**: Spring Security + JWT
- **监控**: Micrometer + Prometheus + Grafana
- **部署**: Docker + Kubernetes

## 📁 项目结构

```
smartlearn-system/
├── common/                          # 公共模块
│   ├── common-core/                # 核心工具类、常量、异常
│   ├── common-security/            # 安全组件、JWT
│   ├── common-database/            # 数据库组件、JPA配置
│   └── common-message/             # 消息组件、Kafka配置
├── infrastructure/                 # 基础设施
│   ├── eureka-server/             # 服务注册中心
│   ├── config-server/             # 配置中心
│   └── admin-server/              # 监控中心
├── gateway/                        # API网关
├── services/                       # 微服务
│   ├── user-service/              # 用户服务
│   ├── study-service/             # 学习服务
│   ├── exercise-service/          # 题库服务
│   ├── ai-service/                # AI服务
│   ├── analytics-service/         # 分析服务
│   └── notification-service/      # 通知服务
├── docker/                        # Docker配置
│   ├── docker-compose.yml         # Docker编排文件
│   └── Dockerfile                 # 应用镜像构建文件
├── scripts/                       # 脚本文件
│   ├── start-services.sh          # 启动脚本
│   └── stop-services.sh           # 停止脚本
└── README.md                      # 项目说明
```

## 🚀 快速开始

### 环境要求

- Java 17+
- Maven 3.8+
- Docker & Docker Compose
- MySQL 8.0+
- Redis 7.0+

### 1. 克隆项目

```bash
git clone <repository-url>
cd smartlearn-system
```

### 2. 启动基础设施

```bash
# 启动数据库、缓存等基础设施
cd docker
docker-compose up -d
cd ..
```

### 3. 构建项目

```bash
# 构建所有模块
mvn clean compile -DskipTests
```

### 4. 启动服务

#### 方式一：使用启动脚本（推荐）

```bash
# 给脚本执行权限
chmod +x scripts/start-services.sh
chmod +x scripts/stop-services.sh

# 启动所有服务
./scripts/start-services.sh
```

#### 方式二：手动启动

```bash
# 1. 启动Eureka服务注册中心
cd infrastructure/eureka-server
mvn spring-boot:run &
cd ../..

# 2. 启动配置中心
cd infrastructure/config-server
mvn spring-boot:run &
cd ../..

# 3. 启动API网关
cd gateway
mvn spring-boot:run &
cd ..

# 4. 启动业务服务
cd services/user-service && mvn spring-boot:run &
cd services/study-service && mvn spring-boot:run &
cd services/exercise-service && mvn spring-boot:run &
cd services/ai-service && mvn spring-boot:run &
cd services/analytics-service && mvn spring-boot:run &
cd services/notification-service && mvn spring-boot:run &
```

### 5. 验证服务

访问以下地址验证服务是否正常启动：

- **Eureka控制台**: http://localhost:8761
- **API网关**: http://localhost:8080
- **配置中心**: http://localhost:8888/actuator/health
- **API网关**: http://localhost:8080/actuator/health
- **监控中心**: http://localhost:8090 (admin/admin123)
- **用户服务**: http://localhost:8081/actuator/health
- **题库服务**: http://localhost:8082/actuator/health
- **学习服务**: http://localhost:8083/actuator/health
- **AI服务**: http://localhost:8084/actuator/health
- **通知服务**: http://localhost:8085/actuator/health
- **分析服务**: http://localhost:8086/actuator/health

## 🔗 服务端口

| 服务 | 端口 | 描述 | 访问地址 |
|------|------|------|----------|
| API网关 | 8080 | 统一入口 | http://localhost:8080 |
| 注册中心 | 8761 | Eureka Server | http://localhost:8761 |
| 配置中心 | 8888 | Config Server | http://localhost:8888 |
| 监控中心 | 8090 | Admin Server | http://localhost:8090 |
| 用户服务 | 8081 | 用户管理 | http://localhost:8081 |
| 题库服务 | 8082 | 题库管理 | http://localhost:8082 |
| 学习服务 | 8083 | 学习管理 | http://localhost:8083 |
| AI服务 | 8084 | 智能推荐 | http://localhost:8084 |
| 通知服务 | 8085 | 消息通知 | http://localhost:8085 |
| 分析服务 | 8086 | 数据分析 | http://localhost:8086 |

## 🐳 Docker部署

### 一键启动所有服务

```bash
# 进入docker目录
cd docker

# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

## 📚 文档

- [API文档](docs/api/README.md)
- [部署指南](docs/deployment/README.md)
- [开发指南](docs/development/README.md)
- [架构设计](docs/architecture/README.md)

## 🔧 开发指南

### 添加新的微服务

1. 在 `services/` 目录下创建新的服务模块
2. 在根 `pom.xml` 中添加模块引用
3. 创建启动类并添加必要的注解
4. 配置 `application.yml`
5. 在 Eureka 中注册服务

### 数据库迁移

使用 Flyway 进行数据库版本管理：

```bash
# 执行数据库迁移
mvn flyway:migrate -pl services/user-service
```

### 测试

```bash
# 运行单元测试
mvn test

# 运行集成测试
mvn verify
```

## 📊 监控和运维

### 健康检查

所有服务都提供健康检查端点：
- `/actuator/health` - 健康状态
- `/actuator/info` - 服务信息
- `/actuator/metrics` - 指标数据
- `/actuator/prometheus` - Prometheus指标

### 日志查看

```bash
# 查看服务日志
tail -f logs/user-service.log
tail -f logs/exercise-service.log
```

### 停止服务

```bash
# 使用脚本停止所有服务
./scripts/stop-services.sh

# 或手动停止Docker服务
cd docker && docker-compose down
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

- 项目维护者: SmartLearn Team
- 邮箱: <EMAIL>
- 项目地址: https://github.com/smartlearn/smartlearn-system

---

**SmartLearn System** - 让软考学习更智能！
