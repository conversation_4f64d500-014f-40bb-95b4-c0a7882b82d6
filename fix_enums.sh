#!/bin/bash

# 修复所有枚举类的构造函数问题

# 定义需要修复的枚举类文件
ENUM_FILES=(
    "services/question-service/src/main/java/com/chiron/smartlearnsystem/question/enums/QuestionSource.java"
    "services/question-service/src/main/java/com/chiron/smartlearnsystem/question/enums/ReviewStatus.java"
    "services/question-service/src/main/java/com/chiron/smartlearnsystem/question/enums/PaperType.java"
    "services/question-service/src/main/java/com/chiron/smartlearnsystem/question/enums/PaperStatus.java"
    "services/question-service/src/main/java/com/chiron/smartlearnsystem/question/enums/ExamStatus.java"
)

for file in "${ENUM_FILES[@]}"; do
    echo "修复文件: $file"
    
    # 移除 @AllArgsConstructor 注解
    sed -i '' '/@AllArgsConstructor/d' "$file"
    
    # 获取枚举类名
    enum_name=$(basename "$file" .java)
    
    # 在字段定义后添加构造函数
    sed -i '' '/private final String description;/a\
\
    '"$enum_name"'(String code, String name, String description) {\
        this.code = code;\
        this.name = name;\
        this.description = description;\
    }
' "$file"
    
    echo "完成修复: $file"
done

echo "所有枚举类修复完成！"
