#!/usr/bin/env python3

import re
import os

# 修复AI服务中的ApiResponse类型问题
files_to_fix = [
    "services/ai-service/src/main/java/com/chiron/smartlearnsystem/ai/controller/RecommendationController.java",
    "services/ai-service/src/main/java/com/chiron/smartlearnsystem/ai/controller/AbilityAssessmentController.java"
]

for file_path in files_to_fix:
    if os.path.exists(file_path):
        print(f"修复文件: {file_path}")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换 ApiResponse.success("message") 为 ApiResponse.<Void>success("message", null)
        pattern = r'return ResponseEntity\.ok\(ApiResponse\.success\("([^"]+)"\)\);'
        replacement = r'return ResponseEntity.ok(ApiResponse.<Void>success("\1", null));'
        
        new_content = re.sub(pattern, replacement, content)
        
        # 如果内容有变化，写回文件
        if new_content != content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            print(f"完成修复: {file_path}")
        else:
            print(f"无需修复: {file_path}")
    else:
        print(f"文件不存在: {file_path}")

print("AI服务ApiResponse修复完成！")
