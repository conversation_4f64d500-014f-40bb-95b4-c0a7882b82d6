apiVersion: v1
kind: ConfigMap
metadata:
  name: smartlearn-config
  namespace: smartlearn
data:
  # 数据库配置
  mysql.host: "mysql-service"
  mysql.port: "3306"
  mysql.database: "smartlearn"
  mysql.username: "smartlearn"
  
  # Redis配置
  redis.host: "redis-service"
  redis.port: "6379"
  redis.database: "0"
  
  # Eureka配置
  eureka.server.url: "http://eureka-service:8761/eureka/"
  
  # 应用配置
  spring.profiles.active: "k8s"
  
  # JVM配置
  java.opts: "-Xms512m -Xmx1024m -XX:+UseG1GC -XX:+UseContainerSupport"

---
apiVersion: v1
kind: Secret
metadata:
  name: smartlearn-secrets
  namespace: smartlearn
type: Opaque
data:
  # Base64编码的密码
  mysql.password: c21hcnRsZWFybjEyMw==  # smartlearn123
  redis.password: ""
  jwt.secret: c21hcnRsZWFybi1qd3Qtc2VjcmV0LWtleS1mb3ItYXV0aGVudGljYXRpb24tYW5kLWF1dGhvcml6YXRpb24=
