#!/bin/bash

# SmartLearn System 服务停止脚本
# 作者: SmartLearn System
# 版本: 1.0.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 停止服务函数
stop_service() {
    local service_name=$1
    local pid_file="pids/${service_name}.pid"
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if ps -p "$pid" > /dev/null 2>&1; then
            log_info "停止 $service_name (PID: $pid)..."
            kill "$pid"
            
            # 等待进程结束
            local count=0
            while ps -p "$pid" > /dev/null 2>&1 && [ $count -lt 30 ]; do
                sleep 1
                count=$((count + 1))
            done
            
            # 如果进程仍在运行，强制杀死
            if ps -p "$pid" > /dev/null 2>&1; then
                log_warn "强制停止 $service_name..."
                kill -9 "$pid"
            fi
            
            log_info "$service_name 已停止"
        else
            log_warn "$service_name 进程不存在 (PID: $pid)"
        fi
        
        rm -f "$pid_file"
    else
        log_warn "$service_name PID文件不存在"
    fi
}

# 停止Docker服务
stop_docker_services() {
    log_info "停止Docker基础设施服务..."
    
    if command -v docker-compose &> /dev/null; then
        cd docker && docker-compose down
        cd ..
        log_info "Docker服务已停止"
    else
        log_warn "Docker Compose未安装，跳过Docker服务停止"
    fi
}

# 主函数
main() {
    log_info "=== SmartLearn System 停止脚本 ==="
    log_info "开始停止SmartLearn微服务系统..."
    
    # 停止业务服务
    log_info "停止业务服务..."
    stop_service "notification-service"
    stop_service "analytics-service"
    stop_service "ai-service"
    stop_service "study-service"
    stop_service "exercise-service"
    stop_service "user-service"
    
    # 停止网关
    log_info "停止API网关..."
    stop_service "gateway"
    
    # 停止基础设施服务
    log_info "停止基础设施服务..."
    stop_service "config-server"
    stop_service "eureka-server"
    
    # 停止Docker服务
    stop_docker_services
    
    # 清理日志文件（可选）
    read -p "是否清理日志文件? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "清理日志文件..."
        rm -rf logs/*
        log_info "日志文件已清理"
    fi
    
    log_info "=== 所有服务已停止 ==="
}

# 执行主函数
main "$@"
