#!/bin/bash

# SmartLearn System 服务启动脚本
# 作者: SmartLearn System
# 版本: 1.0.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查Java环境
check_java() {
    if ! command -v java &> /dev/null; then
        log_error "Java未安装或未配置到PATH中"
        exit 1
    fi
    
    JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2 | cut -d'.' -f1)
    if [ "$JAVA_VERSION" -lt 17 ]; then
        log_error "需要Java 17或更高版本，当前版本: $JAVA_VERSION"
        exit 1
    fi
    
    log_info "Java环境检查通过: $(java -version 2>&1 | head -n 1)"
}

# 检查Maven环境
check_maven() {
    if ! command -v mvn &> /dev/null; then
        log_error "Maven未安装或未配置到PATH中"
        exit 1
    fi
    
    log_info "Maven环境检查通过: $(mvn -version | head -n 1)"
}

# 构建项目
build_project() {
    log_info "开始构建项目..."
    
    if mvn clean compile -DskipTests; then
        log_info "项目构建成功"
    else
        log_error "项目构建失败"
        exit 1
    fi
}

# 启动基础设施服务
start_infrastructure() {
    log_info "启动基础设施服务..."
    
    # 启动Docker Compose服务
    if command -v docker-compose &> /dev/null; then
        log_info "启动Docker基础设施..."
        cd docker && docker-compose up -d
        cd ..
        
        # 等待服务启动
        log_info "等待基础设施服务启动..."
        sleep 30
    else
        log_warn "Docker Compose未安装，跳过基础设施启动"
    fi
}

# 启动Eureka服务注册中心
start_eureka() {
    log_info "启动Eureka服务注册中心..."
    
    cd infrastructure/eureka-server
    nohup mvn spring-boot:run > ../../logs/eureka-server.log 2>&1 &
    echo $! > ../../pids/eureka-server.pid
    cd ../..
    
    # 等待Eureka启动
    log_info "等待Eureka服务启动..."
    sleep 20
    
    # 检查Eureka是否启动成功
    if curl -f http://localhost:8761/actuator/health > /dev/null 2>&1; then
        log_info "Eureka服务启动成功"
    else
        log_error "Eureka服务启动失败"
        exit 1
    fi
}

# 启动配置中心
start_config_server() {
    log_info "启动配置中心..."
    
    cd infrastructure/config-server
    nohup mvn spring-boot:run > ../../logs/config-server.log 2>&1 &
    echo $! > ../../pids/config-server.pid
    cd ../..
    
    sleep 15
}

# 启动API网关
start_gateway() {
    log_info "启动API网关..."
    
    cd gateway
    nohup mvn spring-boot:run > ../logs/gateway.log 2>&1 &
    echo $! > ../pids/gateway.pid
    cd ..
    
    sleep 15
}

# 启动业务服务
start_business_services() {
    log_info "启动业务服务..."
    
    # 用户服务
    log_info "启动用户服务..."
    cd services/user-service
    nohup mvn spring-boot:run > ../../logs/user-service.log 2>&1 &
    echo $! > ../../pids/user-service.pid
    cd ../..
    sleep 10
    
    # 题库服务
    log_info "启动题库服务..."
    cd services/exercise-service
    nohup mvn spring-boot:run > ../../logs/exercise-service.log 2>&1 &
    echo $! > ../../pids/exercise-service.pid
    cd ../..
    sleep 10
    
    # 学习服务
    log_info "启动学习服务..."
    cd services/study-service
    nohup mvn spring-boot:run > ../../logs/study-service.log 2>&1 &
    echo $! > ../../pids/study-service.pid
    cd ../..
    sleep 10
    
    # AI服务
    log_info "启动AI服务..."
    cd services/ai-service
    nohup mvn spring-boot:run > ../../logs/ai-service.log 2>&1 &
    echo $! > ../../pids/ai-service.pid
    cd ../..
    sleep 10
    
    # 分析服务
    log_info "启动分析服务..."
    cd services/analytics-service
    nohup mvn spring-boot:run > ../../logs/analytics-service.log 2>&1 &
    echo $! > ../../pids/analytics-service.pid
    cd ../..
    sleep 10
    
    # 通知服务
    log_info "启动通知服务..."
    cd services/notification-service
    nohup mvn spring-boot:run > ../../logs/notification-service.log 2>&1 &
    echo $! > ../../pids/notification-service.pid
    cd ../..
    sleep 10
}

# 创建必要的目录
create_directories() {
    mkdir -p logs
    mkdir -p pids
}

# 主函数
main() {
    log_info "=== SmartLearn System 启动脚本 ==="
    log_info "开始启动SmartLearn微服务系统..."
    
    # 创建目录
    create_directories
    
    # 环境检查
    check_java
    check_maven
    
    # 构建项目
    build_project
    
    # 启动服务
    start_infrastructure
    start_eureka
    start_config_server
    start_gateway
    start_business_services
    
    log_info "=== 所有服务启动完成 ==="
    log_info "服务访问地址:"
    log_info "  - Eureka控制台: http://localhost:8761"
    log_info "  - API网关: http://localhost:8080"
    log_info "  - 用户服务: http://localhost:8081"
    log_info "  - 学习服务: http://localhost:8082"
    log_info "  - 题库服务: http://localhost:8083"
    log_info "  - AI服务: http://localhost:8084"
    log_info "  - 分析服务: http://localhost:8085"
    log_info "  - 通知服务: http://localhost:8086"
    log_info ""
    log_info "日志文件位置: ./logs/"
    log_info "进程ID文件位置: ./pids/"
    log_info ""
    log_info "使用 './scripts/stop-services.sh' 停止所有服务"
}

# 执行主函数
main "$@"
