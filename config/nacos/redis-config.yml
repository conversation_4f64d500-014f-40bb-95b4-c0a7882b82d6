# SmartLearn System Redis配置
# Data ID: redis-config.yml
# Group: DEFAULT_GROUP
# Namespace: smartlearn

# Redis配置
redis:
  # 单机配置
  host: localhost
  port: 6379
  password: 
  database: 0
  timeout: 3000ms
  
  # 连接池配置
  lettuce:
    pool:
      max-active: 8
      max-wait: -1ms
      max-idle: 8
      min-idle: 0
      time-between-eviction-runs: 30s
    shutdown-timeout: 100ms
  
  # 集群配置（如果使用集群模式）
  cluster:
    nodes:
      - localhost:7001
      - localhost:7002
      - localhost:7003
      - localhost:7004
      - localhost:7005
      - localhost:7006
    max-redirects: 3
    
  # 哨兵配置（如果使用哨兵模式）
  sentinel:
    master: mymaster
    nodes:
      - localhost:26379
      - localhost:26380
      - localhost:26381

# 缓存配置
cache:
  redis:
    # 默认过期时间（秒）
    default-expiration: 3600
    # 缓存key前缀
    key-prefix: "smartlearn:"
    # 缓存空值防止缓存穿透
    cache-null-values: true
    # 使用缓存前缀
    use-key-prefix: true
    
    # 不同业务模块的缓存配置
    modules:
      user:
        expiration: 7200  # 2小时
        key-prefix: "user:"
      question:
        expiration: 3600  # 1小时
        key-prefix: "question:"
      learning:
        expiration: 1800  # 30分钟
        key-prefix: "learning:"
      ai:
        expiration: 600   # 10分钟
        key-prefix: "ai:"
      analytics:
        expiration: 300   # 5分钟
        key-prefix: "analytics:"
      notification:
        expiration: 86400 # 24小时
        key-prefix: "notification:"

# Session配置
session:
  store-type: redis
  redis:
    namespace: "smartlearn:session"
    flush-mode: on_save
    save-mode: on_set_attribute
  timeout: 1800s  # 30分钟

# 分布式锁配置
lock:
  redis:
    # 锁的默认过期时间（毫秒）
    default-lease-time: 30000
    # 等待锁的时间（毫秒）
    wait-time: 10000
    # 锁的自动续期时间（毫秒）
    watch-dog-timeout: 30000

# 限流配置
rate-limit:
  redis:
    # 默认限流规则
    default:
      count: 100
      period: 60  # 秒
    # 不同接口的限流配置
    apis:
      login:
        count: 5
        period: 60
      register:
        count: 3
        period: 300
      send-sms:
        count: 1
        period: 60
      upload:
        count: 10
        period: 60

# 消息队列配置（基于Redis）
message-queue:
  redis:
    # 队列配置
    queues:
      email:
        name: "smartlearn:queue:email"
        max-length: 10000
      sms:
        name: "smartlearn:queue:sms"
        max-length: 10000
      notification:
        name: "smartlearn:queue:notification"
        max-length: 10000
      analytics:
        name: "smartlearn:queue:analytics"
        max-length: 10000
    
    # 消费者配置
    consumer:
      batch-size: 10
      poll-timeout: 1000ms
      max-retries: 3
      retry-delay: 5000ms

# 布隆过滤器配置
bloom-filter:
  redis:
    # 用户相关的布隆过滤器
    user:
      name: "smartlearn:bloom:user"
      expected-insertions: 1000000
      false-positive-probability: 0.01
    # 题目相关的布隆过滤器
    question:
      name: "smartlearn:bloom:question"
      expected-insertions: 500000
      false-positive-probability: 0.01
