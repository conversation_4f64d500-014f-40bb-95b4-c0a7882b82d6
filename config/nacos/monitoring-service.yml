# SmartLearn System 监控服务配置
# Data ID: monitoring-service.yml
# Group: DEFAULT_GROUP
# Namespace: smartlearn

server:
  port: 9090

spring:
  application:
    name: monitoring-service
  
  # 数据库配置
  datasource:
    url: ${datasource.monitoring.url}
    username: ${datasource.monitoring.username}
    password: ${datasource.monitoring.password}
    driver-class-name: ${datasource.monitoring.driver-class-name}
    hikari:
      minimum-idle: ${hikari.minimum-idle}
      maximum-pool-size: ${hikari.maximum-pool-size}
      auto-commit: ${hikari.auto-commit}
      idle-timeout: ${hikari.idle-timeout}
      pool-name: MonitoringServiceHikariCP
      max-lifetime: ${hikari.max-lifetime}
      connection-timeout: ${hikari.connection-timeout}
  
  # Redis配置
  data:
    redis:
      host: ${redis.host}
      port: ${redis.port}
      password: ${redis.password}
      database: 9
      timeout: ${redis.timeout}
      lettuce:
        pool:
          max-active: ${redis.lettuce.pool.max-active}
          max-wait: ${redis.lettuce.pool.max-wait}
          max-idle: ${redis.lettuce.pool.max-idle}
          min-idle: ${redis.lettuce.pool.min-idle}
  
  # Spring Boot Admin配置
  boot:
    admin:
      ui:
        title: "SmartLearn System Monitoring"
        brand: "SmartLearn"
        favicon: "/static/favicon.ico"
        favicon-danger: "/static/favicon-danger.ico"
      server:
        enabled: true

# 监控配置
monitoring:
  # 健康检查配置
  health-check:
    interval: 30s
    timeout: 10s
    retry-count: 3
    
  # 指标收集配置
  metrics:
    collection-interval: 15s
    retention-period: 7d
    
  # 告警配置
  alerts:
    enabled: true
    rules:
      cpu-usage:
        threshold: 80
        duration: 5m
        severity: warning
      memory-usage:
        threshold: 85
        duration: 5m
        severity: warning
      disk-usage:
        threshold: 90
        duration: 1m
        severity: critical
      response-time:
        threshold: 2000ms
        duration: 5m
        severity: warning
      error-rate:
        threshold: 5
        duration: 5m
        severity: critical
    
    # 通知配置
    notifications:
      email:
        enabled: false
        smtp:
          host: smtp.gmail.com
          port: 587
          username: <EMAIL>
          password: your-app-password
          from: <EMAIL>
          to:
            - <EMAIL>
            - <EMAIL>
      webhook:
        enabled: false
        url: http://localhost:3000/webhook/alert
        timeout: 5s
      slack:
        enabled: false
        webhook-url: https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
        channel: "#alerts"
        username: "SmartLearn Monitor"
        
  # 日志监控配置
  log-monitoring:
    enabled: true
    log-levels:
      - ERROR
      - WARN
    patterns:
      - "OutOfMemoryError"
      - "SQLException"
      - "TimeoutException"
      - "ConnectionException"
    
  # 性能监控配置
  performance:
    enabled: true
    thresholds:
      response-time: 2000ms
      throughput: 1000
      error-rate: 5
    
  # 自定义指标配置
  custom-metrics:
    business:
      user-registrations: 
        type: counter
        description: "用户注册数量"
      question-submissions:
        type: counter
        description: "题目提交数量"
      learning-sessions:
        type: timer
        description: "学习会话时长"
      ai-recommendations:
        type: gauge
        description: "AI推荐准确率"

# Prometheus配置
prometheus:
  scrape-interval: 15s
  evaluation-interval: 15s
  external-labels:
    cluster: smartlearn
    environment: dev
  
# Grafana配置
grafana:
  url: http://localhost:3000
  username: admin
  password: admin
  datasource:
    prometheus-url: http://localhost:9090
