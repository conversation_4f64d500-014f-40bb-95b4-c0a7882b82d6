#!/bin/bash

# SmartLearn System Nacos配置导入脚本
# 使用方法: ./import-configs.sh [nacos-server-url] [namespace]

NACOS_SERVER=${1:-"http://localhost:8848"}
NAMESPACE=${2:-"smartlearn"}
GROUP="DEFAULT_GROUP"

echo "开始导入SmartLearn System配置到Nacos..."
echo "Nacos服务器: $NACOS_SERVER"
echo "命名空间: $NAMESPACE"
echo "分组: $GROUP"
echo "================================"

# 创建命名空间（如果不存在）
echo "创建命名空间: $NAMESPACE"
curl -X POST "$NACOS_SERVER/nacos/v1/console/namespaces" \
  -d "customNamespaceId=$NAMESPACE&namespaceName=SmartLearn System&namespaceDesc=SmartLearn System Configuration Namespace"

echo ""
echo "等待3秒..."
sleep 3

# 导入配置文件的函数
import_config() {
    local file_path=$1
    local data_id=$2
    
    if [ ! -f "$file_path" ]; then
        echo "警告: 配置文件 $file_path 不存在，跳过..."
        return
    fi
    
    echo "导入配置: $data_id"
    
    # 读取文件内容并进行URL编码
    content=$(cat "$file_path" | sed 's/+/%2B/g' | sed 's/ /%20/g' | sed 's/!/%21/g' | sed 's/"/%22/g' | sed 's/#/%23/g' | sed 's/\$/%24/g' | sed 's/&/%26/g' | sed "s/'/%27/g" | sed 's/(/%28/g' | sed 's/)/%29/g' | sed 's/\*/%2A/g' | sed 's/,/%2C/g' | sed 's/\//%2F/g' | sed 's/:/%3A/g' | sed 's/;/%3B/g' | sed 's/?/%3F/g' | sed 's/@/%40/g' | sed 's/\[/%5B/g' | sed 's/\]/%5D/g')
    
    # 发送配置到Nacos
    response=$(curl -s -X POST "$NACOS_SERVER/nacos/v1/cs/configs" \
      -d "dataId=$data_id&group=$GROUP&content=$content&tenant=$NAMESPACE&type=yaml")
    
    if [ "$response" = "true" ]; then
        echo "✓ 成功导入: $data_id"
    else
        echo "✗ 导入失败: $data_id (响应: $response)"
    fi
}

# 导入所有配置文件
echo "开始导入配置文件..."
echo "--------------------------------"

# 全局配置
import_config "application-common.yml" "application-common.yml"

# 数据库配置
import_config "database-config.yml" "database-config.yml"

# Redis配置
import_config "redis-config.yml" "redis-config.yml"

# 服务配置
import_config "user-service.yml" "user-service.yml"
import_config "question-service.yml" "question-service.yml"
import_config "monitoring-service.yml" "monitoring-service.yml"

# 检查其他服务配置文件是否存在并导入
if [ -f "learning-service.yml" ]; then
    import_config "learning-service.yml" "learning-service.yml"
fi

if [ -f "ai-service.yml" ]; then
    import_config "ai-service.yml" "ai-service.yml"
fi

if [ -f "analytics-service.yml" ]; then
    import_config "analytics-service.yml" "analytics-service.yml"
fi

if [ -f "notification-service.yml" ]; then
    import_config "notification-service.yml" "notification-service.yml"
fi

echo "================================"
echo "配置导入完成！"
echo ""
echo "请访问 $NACOS_SERVER/nacos 查看配置"
echo "用户名: nacos"
echo "密码: nacos"
echo ""
echo "配置列表:"
echo "- application-common.yml (全局配置)"
echo "- database-config.yml (数据库配置)"
echo "- redis-config.yml (Redis配置)"
echo "- user-service.yml (用户服务配置)"
echo "- question-service.yml (题目服务配置)"
echo "- monitoring-service.yml (监控服务配置)"
echo ""
echo "注意事项:"
echo "1. 请根据实际环境修改数据库连接信息"
echo "2. 请根据实际环境修改Redis连接信息"
echo "3. 请根据需要启用/禁用相关功能"
echo "4. 生产环境请修改默认密码和密钥"
