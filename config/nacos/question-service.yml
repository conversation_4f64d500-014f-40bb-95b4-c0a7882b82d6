# SmartLearn System 题目服务配置
# Data ID: question-service.yml
# Group: DEFAULT_GROUP
# Namespace: smartlearn

server:
  port: 8082

spring:
  application:
    name: question-service
  
  # 数据库配置
  datasource:
    url: ${datasource.question.url}
    username: ${datasource.question.username}
    password: ${datasource.question.password}
    driver-class-name: ${datasource.question.driver-class-name}
    hikari:
      minimum-idle: ${hikari.minimum-idle}
      maximum-pool-size: ${hikari.maximum-pool-size}
      auto-commit: ${hikari.auto-commit}
      idle-timeout: ${hikari.idle-timeout}
      pool-name: QuestionServiceHikariCP
      max-lifetime: ${hikari.max-lifetime}
      connection-timeout: ${hikari.connection-timeout}
  
  # Redis配置
  data:
    redis:
      host: ${redis.host}
      port: ${redis.port}
      password: ${redis.password}
      database: 2
      timeout: ${redis.timeout}
      lettuce:
        pool:
          max-active: ${redis.lettuce.pool.max-active}
          max-wait: ${redis.lettuce.pool.max-wait}
          max-idle: ${redis.lettuce.pool.max-idle}
          min-idle: ${redis.lettuce.pool.min-idle}

# 题目服务特定配置
question-service:
  # 题目导入配置
  import:
    batch-size: 100
    max-file-size: 10MB
    allowed-formats: xlsx,xls,csv,json
    template-path: /templates/question-import-template.xlsx
    
  # 题目图片配置
  image:
    upload-path: /data/uploads/questions/
    max-size: 5MB
    allowed-types: jpg,jpeg,png,gif,svg
    thumbnail:
      enabled: true
      width: 200
      height: 200
      
  # 题目审核配置
  review:
    auto-review: false
    review-timeout: 7  # 7天
    require-double-review: true
    
  # 题目搜索配置
  search:
    elasticsearch:
      enabled: true
      index-name: smartlearn-questions
      batch-size: 1000
    
  # 题目缓存配置
  cache:
    question-detail:
      expiration: 3600  # 1小时
    question-list:
      expiration: 1800  # 30分钟
    category-tree:
      expiration: 7200  # 2小时
      
  # 题目统计配置
  statistics:
    update-interval: 300  # 5分钟
    batch-size: 1000
    
  # 题目导出配置
  export:
    max-questions: 10000
    formats: pdf,word,excel
    template-path: /templates/export/

# 文件存储配置
file-storage:
  type: local  # local, oss, s3
  local:
    base-path: /data/storage/
  oss:
    endpoint: https://oss-cn-hangzhou.aliyuncs.com
    access-key-id: your-access-key-id
    access-key-secret: your-access-key-secret
    bucket-name: smartlearn-questions
  s3:
    endpoint: https://s3.amazonaws.com
    access-key: your-access-key
    secret-key: your-secret-key
    bucket-name: smartlearn-questions
    region: us-east-1
