# SmartLearn System Nacos配置中心使用指南

## 📋 概述

本文档介绍SmartLearn System项目中Nacos配置中心的使用方法和配置说明。

## 🚀 快速开始

### 1. 启动Nacos服务器

```bash
# 下载Nacos
wget https://github.com/alibaba/nacos/releases/download/2.3.0/nacos-server-2.3.0.tar.gz
tar -xzf nacos-server-2.3.0.tar.gz
cd nacos/bin

# 单机模式启动
./startup.sh -m standalone

# 访问控制台
# URL: http://localhost:8848/nacos
# 用户名: nacos
# 密码: nacos
```

### 2. 导入配置

```bash
# 进入配置目录
cd config/nacos

# 执行导入脚本
./import-configs.sh

# 或者手动指定Nacos服务器地址和命名空间
./import-configs.sh http://localhost:8848 smartlearn
```

### 3. 验证配置

访问Nacos控制台，检查以下配置是否已成功导入：
- `application-common.yml` - 全局配置
- `database-config.yml` - 数据库配置
- `redis-config.yml` - Redis配置
- `user-service.yml` - 用户服务配置
- `question-service.yml` - 题目服务配置
- `monitoring-service.yml` - 监控服务配置

## 📁 配置文件说明

### 全局配置 (application-common.yml)
- **用途**: 所有服务共享的通用配置
- **包含**: Spring Cloud、Eureka、Actuator、日志、安全等配置
- **Data ID**: `application-common.yml`
- **Group**: `DEFAULT_GROUP`

### 数据库配置 (database-config.yml)
- **用途**: 各服务的数据库连接配置
- **包含**: MySQL连接信息、HikariCP连接池、JPA、MyBatis配置
- **Data ID**: `database-config.yml`
- **Group**: `DEFAULT_GROUP`

### Redis配置 (redis-config.yml)
- **用途**: Redis缓存和会话配置
- **包含**: Redis连接、缓存策略、分布式锁、限流配置
- **Data ID**: `redis-config.yml`
- **Group**: `DEFAULT_GROUP`

### 服务专用配置
每个微服务都有独立的配置文件：
- `user-service.yml` - 用户服务配置
- `question-service.yml` - 题目服务配置
- `learning-service.yml` - 学习服务配置
- `ai-service.yml` - AI服务配置
- `analytics-service.yml` - 分析服务配置
- `notification-service.yml` - 通知服务配置
- `monitoring-service.yml` - 监控服务配置

## 🔧 配置使用方法

### 1. 在应用中引用配置

在各服务的`bootstrap.yml`中配置Nacos：

```yaml
spring:
  application:
    name: user-service
  cloud:
    nacos:
      config:
        server-addr: localhost:8848
        namespace: smartlearn
        group: DEFAULT_GROUP
        file-extension: yml
        shared-configs:
          - data-id: application-common.yml
            group: DEFAULT_GROUP
            refresh: true
          - data-id: database-config.yml
            group: DEFAULT_GROUP
            refresh: true
          - data-id: redis-config.yml
            group: DEFAULT_GROUP
            refresh: true
        extension-configs:
          - data-id: user-service.yml
            group: DEFAULT_GROUP
            refresh: true
```

### 2. 配置优先级

配置加载优先级（从高到低）：
1. 服务专用配置 (`user-service.yml`)
2. 共享配置 (`application-common.yml`, `database-config.yml`, `redis-config.yml`)
3. 本地配置文件 (`application.yml`)

### 3. 动态配置刷新

使用`@RefreshScope`注解实现配置热更新：

```java
@RestController
@RefreshScope
public class ConfigController {
    
    @Value("${custom.property:default}")
    private String customProperty;
    
    @GetMapping("/config")
    public String getConfig() {
        return customProperty;
    }
}
```

## 🔐 安全配置

### 1. 生产环境配置

生产环境请修改以下配置：

```yaml
# 数据库密码
datasource:
  user:
    password: your-production-password

# JWT密钥
security:
  jwt:
    secret: your-production-jwt-secret

# Redis密码
redis:
  password: your-production-redis-password
```

### 2. 配置加密

对敏感配置进行加密：

```bash
# 使用Nacos配置加密功能
# 在配置值前添加 {cipher} 前缀
password: '{cipher}AQB+7FWJdE5Rw8...'
```

## 🌍 环境配置

### 开发环境 (dev)
- 命名空间: `smartlearn-dev`
- 数据库: 本地MySQL
- Redis: 本地Redis

### 测试环境 (test)
- 命名空间: `smartlearn-test`
- 数据库: 测试环境MySQL
- Redis: 测试环境Redis

### 生产环境 (prod)
- 命名空间: `smartlearn-prod`
- 数据库: 生产环境MySQL集群
- Redis: 生产环境Redis集群

## 📊 监控配置

### 1. 配置变更监控

Nacos提供配置变更历史和回滚功能：
- 查看配置变更历史
- 对比配置版本差异
- 一键回滚到历史版本

### 2. 配置推送监控

监控配置推送状态：
- 推送成功/失败统计
- 客户端配置更新状态
- 配置生效时间

## 🔧 故障排查

### 1. 常见问题

**配置无法加载**
- 检查Nacos服务器是否正常运行
- 验证命名空间和Group配置
- 确认Data ID是否正确

**配置不生效**
- 检查配置优先级
- 验证`@RefreshScope`注解
- 查看应用日志

**连接超时**
- 检查网络连接
- 验证Nacos服务器地址
- 调整超时配置

### 2. 日志调试

启用Nacos客户端调试日志：

```yaml
logging:
  level:
    com.alibaba.nacos: DEBUG
```

## 📚 参考资料

- [Nacos官方文档](https://nacos.io/zh-cn/docs/what-is-nacos.html)
- [Spring Cloud Alibaba文档](https://spring-cloud-alibaba-group.github.io/github-pages/hoxton/zh-cn/index.html)
- [配置中心最佳实践](https://nacos.io/zh-cn/docs/best-practice.html)
