# SmartLearn System 用户服务配置
# Data ID: user-service.yml
# Group: DEFAULT_GROUP
# Namespace: smartlearn

server:
  port: 8081

spring:
  application:
    name: user-service
  
  # 数据库配置
  datasource:
    url: ${datasource.user.url}
    username: ${datasource.user.username}
    password: ${datasource.user.password}
    driver-class-name: ${datasource.user.driver-class-name}
    hikari:
      minimum-idle: ${hikari.minimum-idle}
      maximum-pool-size: ${hikari.maximum-pool-size}
      auto-commit: ${hikari.auto-commit}
      idle-timeout: ${hikari.idle-timeout}
      pool-name: UserServiceHikariCP
      max-lifetime: ${hikari.max-lifetime}
      connection-timeout: ${hikari.connection-timeout}
  
  # Redis配置
  data:
    redis:
      host: ${redis.host}
      port: ${redis.port}
      password: ${redis.password}
      database: 1
      timeout: ${redis.timeout}
      lettuce:
        pool:
          max-active: ${redis.lettuce.pool.max-active}
          max-wait: ${redis.lettuce.pool.max-wait}
          max-idle: ${redis.lettuce.pool.max-idle}
          min-idle: ${redis.lettuce.pool.min-idle}

# 用户服务特定配置
user-service:
  # 密码策略
  password:
    min-length: 8
    max-length: 20
    require-uppercase: true
    require-lowercase: true
    require-digit: true
    require-special-char: false
    
  # 验证码配置
  captcha:
    enabled: true
    length: 4
    expiration: 300  # 5分钟
    
  # 短信配置
  sms:
    provider: aliyun
    template-id: SMS_123456789
    sign-name: SmartLearn
    expiration: 300  # 5分钟
    daily-limit: 10
    
  # 邮箱配置
  email:
    enabled: true
    verification:
      expiration: 1800  # 30分钟
      template: email-verification
    
  # 登录配置
  login:
    max-attempts: 5
    lock-duration: 1800  # 30分钟
    session-timeout: 7200  # 2小时
    
  # 注册配置
  register:
    email-verification-required: true
    phone-verification-required: false
    auto-activation: false
    
  # 用户头像配置
  avatar:
    upload-path: /data/uploads/avatars/
    max-size: 2MB
    allowed-types: jpg,jpeg,png,gif
    default-avatar: /static/images/default-avatar.png

# JWT配置
jwt:
  secret: ${security.jwt.secret}
  expiration: ${security.jwt.expiration}
  refresh-expiration: ${security.jwt.refresh-expiration}
  header: Authorization
  token-prefix: "Bearer "

# 第三方登录配置
oauth2:
  github:
    client-id: your-github-client-id
    client-secret: your-github-client-secret
    redirect-uri: http://localhost:8081/oauth2/callback/github
  google:
    client-id: your-google-client-id
    client-secret: your-google-client-secret
    redirect-uri: http://localhost:8081/oauth2/callback/google
  wechat:
    app-id: your-wechat-app-id
    app-secret: your-wechat-app-secret
    redirect-uri: http://localhost:8081/oauth2/callback/wechat
