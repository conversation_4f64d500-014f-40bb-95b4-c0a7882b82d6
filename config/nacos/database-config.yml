# SmartLearn System 数据库配置
# Data ID: database-config.yml
# Group: DEFAULT_GROUP
# Namespace: smartlearn

# 数据库配置
datasource:
  # 主数据源配置
  primary:
    url: *********************************************************************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    
  # 用户服务数据库
  user:
    url: *********************************************************************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    
  # 题目服务数据库
  question:
    url: *************************************************************************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    
  # 学习服务数据库
  learning:
    url: *************************************************************************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    
  # AI服务数据库
  ai:
    url: *******************************************************************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    
  # 分析服务数据库
  analytics:
    url: **************************************************************************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    
  # 通知服务数据库
  notification:
    url: *****************************************************************************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    
  # 监控服务数据库
  monitoring:
    url: ***************************************************************************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver

# HikariCP连接池配置
hikari:
  minimum-idle: 5
  maximum-pool-size: 20
  auto-commit: true
  idle-timeout: 30000
  pool-name: SmartLearnHikariCP
  max-lifetime: 1800000
  connection-timeout: 30000
  connection-test-query: SELECT 1
  leak-detection-threshold: 60000

# JPA配置
jpa:
  hibernate:
    ddl-auto: update
    naming:
      physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
  show-sql: false
  properties:
    hibernate:
      dialect: org.hibernate.dialect.MySQL8Dialect
      format_sql: true
      use_sql_comments: true
      jdbc:
        batch_size: 20
        order_inserts: true
        order_updates: true
      cache:
        use_second_level_cache: true
        use_query_cache: true
        region:
          factory_class: org.hibernate.cache.jcache.JCacheRegionFactory

# MyBatis配置
mybatis:
  mapper-locations: classpath*:mapper/*.xml
  type-aliases-package: com.chiron.smartlearnsystem.*.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true
    lazy-loading-enabled: true
    multiple-result-sets-enabled: true
    use-column-label: true
    use-generated-keys: true
    auto-mapping-behavior: partial
    default-executor-type: reuse
    default-statement-timeout: 25000

# 数据库迁移配置
flyway:
  enabled: true
  baseline-on-migrate: true
  validate-on-migrate: true
  locations: classpath:db/migration
  encoding: UTF-8
  table: flyway_schema_history
