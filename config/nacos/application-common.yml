# SmartLearn System 全局配置
# Data ID: application-common.yml
# Group: DEFAULT_GROUP
# Namespace: smartlearn

# Spring Cloud配置
spring:
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: smartlearn
        group: DEFAULT_GROUP
        cluster-name: DEFAULT
        metadata:
          version: 1.0.0
          zone: zone1
      config:
        server-addr: localhost:8848
        namespace: smartlearn
        group: DEFAULT_GROUP
        file-extension: yml
        refresh-enabled: true
        
# Eureka配置
eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
    register-with-eureka: true
    fetch-registry: true
    registry-fetch-interval-seconds: 30
  instance:
    prefer-ip-address: true
    lease-renewal-interval-in-seconds: 30
    lease-expiration-duration-in-seconds: 90
    metadata-map:
      version: 1.0.0
      zone: zone1

# Actuator配置
management:
  endpoints:
    web:
      exposure:
        include: "*"
      base-path: /actuator
  endpoint:
    health:
      show-details: always
      show-components: always
    metrics:
      enabled: true
    prometheus:
      enabled: true
    info:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
        step: 30s
    distribution:
      percentiles-histogram:
        http.server.requests: true
      percentiles:
        http.server.requests: 0.5, 0.95, 0.99
      slo:
        http.server.requests: 10ms, 50ms, 100ms, 200ms, 500ms, 1s, 2s, 5s
  tracing:
    sampling:
      probability: 0.1

# 日志配置
logging:
  level:
    com.chiron.smartlearnsystem: INFO
    org.springframework.cloud: DEBUG
    org.springframework.security: INFO
    com.alibaba.nacos: INFO
  pattern:
    console: "%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
  file:
    name: logs/application.log
    max-size: 100MB
    max-history: 30

# Feign配置
feign:
  client:
    config:
      default:
        connect-timeout: 5000
        read-timeout: 10000
        logger-level: basic
  hystrix:
    enabled: true
  compression:
    request:
      enabled: true
      mime-types: text/xml,application/xml,application/json
      min-request-size: 2048
    response:
      enabled: true

# Hystrix配置
hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 10000
      circuitBreaker:
        requestVolumeThreshold: 20
        errorThresholdPercentage: 50
        sleepWindowInMilliseconds: 5000

# Ribbon配置
ribbon:
  ReadTimeout: 10000
  ConnectTimeout: 5000
  MaxAutoRetries: 1
  MaxAutoRetriesNextServer: 2
  OkToRetryOnAllOperations: false

# 安全配置
security:
  jwt:
    secret: smartlearn-jwt-secret-key-2024
    expiration: 86400000  # 24小时
    refresh-expiration: 604800000  # 7天
  cors:
    allowed-origins: "*"
    allowed-methods: "*"
    allowed-headers: "*"
    allow-credentials: true

# 分页配置
pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql

# 文件上传配置
file:
  upload:
    path: /data/uploads/
    max-size: 10MB
    allowed-types: jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx

# 缓存配置
cache:
  redis:
    default-expiration: 3600  # 1小时
    key-prefix: "smartlearn:"
  caffeine:
    spec: "maximumSize=1000,expireAfterWrite=30m"
