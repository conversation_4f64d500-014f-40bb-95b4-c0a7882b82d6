package com.chiron.smartlearnsystem.gateway.config;

import com.chiron.smartlearnsystem.gateway.filter.JwtAuthenticationFilter;
import com.chiron.smartlearnsystem.gateway.filter.RateLimitFilter;
import lombok.RequiredArgsConstructor;
import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.cloud.gateway.route.builder.RouteLocatorBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.reactive.CorsWebFilter;
import org.springframework.web.cors.reactive.UrlBasedCorsConfigurationSource;

import java.time.Duration;

/**
 * 网关配置
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Configuration
@RequiredArgsConstructor
public class GatewayConfig {

    private final JwtAuthenticationFilter jwtAuthenticationFilter;
    private final RateLimitFilter rateLimitFilter;

    /**
     * 路由配置
     */
    @Bean
    public RouteLocator customRouteLocator(RouteLocatorBuilder builder) {
        return builder.routes()
                // 用户服务路由
                .route("user-service", r -> r
                        .path("/api/v1/auth/**", "/api/v1/users/**")
                        .filters(f -> f
                                .filter(jwtAuthenticationFilter.apply(new JwtAuthenticationFilter.Config()))
                                .filter(rateLimitFilter.apply(createRateLimitConfig(100, 60, RateLimitFilter.KeyType.IP)))
                                .circuitBreaker(config -> config
                                        .setName("user-service-cb")
                                        .setFallbackUri("forward:/fallback/user-service"))
                                .retry(config -> config
                                        .setRetries(3)
                                        .setBackoff(Duration.ofMillis(100), Duration.ofMillis(1000), 2, false)))
                        .uri("lb://user-service"))
                
                // 题库服务路由
                .route("question-service", r -> r
                        .path("/api/v1/questions/**", "/api/v1/exams/**")
                        .filters(f -> f
                                .filter(jwtAuthenticationFilter.apply(new JwtAuthenticationFilter.Config()))
                                .filter(rateLimitFilter.apply(createRateLimitConfig(200, 60, RateLimitFilter.KeyType.USER_API)))
                                .circuitBreaker(config -> config
                                        .setName("question-service-cb")
                                        .setFallbackUri("forward:/fallback/question-service"))
                                .retry(config -> config
                                        .setRetries(3)
                                        .setBackoff(Duration.ofMillis(100), Duration.ofMillis(1000), 2, false)))
                        .uri("lb://question-service"))
                
                // 学习服务路由
                .route("learning-service", r -> r
                        .path("/api/v1/learning/**", "/api/v1/plans/**", "/api/v1/progress/**")
                        .filters(f -> f
                                .filter(jwtAuthenticationFilter.apply(new JwtAuthenticationFilter.Config()))
                                .filter(rateLimitFilter.apply(createRateLimitConfig(150, 60, RateLimitFilter.KeyType.USER)))
                                .circuitBreaker(config -> config
                                        .setName("learning-service-cb")
                                        .setFallbackUri("forward:/fallback/learning-service"))
                                .retry(config -> config
                                        .setRetries(3)
                                        .setBackoff(Duration.ofMillis(100), Duration.ofMillis(1000), 2, false)))
                        .uri("lb://learning-service"))
                
                // AI服务路由
                .route("ai-service", r -> r
                        .path("/api/v1/ai/**", "/api/v1/recommendations/**", "/api/v1/assessments/**")
                        .filters(f -> f
                                .filter(jwtAuthenticationFilter.apply(new JwtAuthenticationFilter.Config()))
                                .filter(rateLimitFilter.apply(createRateLimitConfig(50, 60, RateLimitFilter.KeyType.USER_API)))
                                .circuitBreaker(config -> config
                                        .setName("ai-service-cb")
                                        .setFallbackUri("forward:/fallback/ai-service"))
                                .retry(config -> config
                                        .setRetries(2)
                                        .setBackoff(Duration.ofMillis(200), Duration.ofMillis(2000), 2, false)))
                        .uri("lb://ai-service"))
                
                // 分析服务路由
                .route("analytics-service", r -> r
                        .path("/api/v1/analytics/**")
                        .filters(f -> f
                                .filter(jwtAuthenticationFilter.apply(new JwtAuthenticationFilter.Config()))
                                .filter(rateLimitFilter.apply(createRateLimitConfig(80, 60, RateLimitFilter.KeyType.USER)))
                                .circuitBreaker(config -> config
                                        .setName("analytics-service-cb")
                                        .setFallbackUri("forward:/fallback/analytics-service"))
                                .retry(config -> config
                                        .setRetries(3)
                                        .setBackoff(Duration.ofMillis(100), Duration.ofMillis(1000), 2, false)))
                        .uri("lb://analytics-service"))
                
                // 通知服务路由
                .route("notification-service", r -> r
                        .path("/api/v1/notifications/**", "/ws/notifications/**")
                        .filters(f -> f
                                .filter(jwtAuthenticationFilter.apply(new JwtAuthenticationFilter.Config()))
                                .filter(rateLimitFilter.apply(createRateLimitConfig(120, 60, RateLimitFilter.KeyType.USER)))
                                .circuitBreaker(config -> config
                                        .setName("notification-service-cb")
                                        .setFallbackUri("forward:/fallback/notification-service"))
                                .retry(config -> config
                                        .setRetries(3)
                                        .setBackoff(Duration.ofMillis(100), Duration.ofMillis(1000), 2, false)))
                        .uri("lb://notification-service"))
                
                // 健康检查路由（不需要认证）
                .route("health-check", r -> r
                        .path("/actuator/health", "/actuator/info")
                        .filters(f -> f
                                .filter(rateLimitFilter.apply(createRateLimitConfig(20, 60, RateLimitFilter.KeyType.IP)))
                                .stripPrefix(0))
                        .uri("lb://gateway-service"))
                
                .build();
    }

    /**
     * 创建限流配置
     */
    private RateLimitFilter.Config createRateLimitConfig(int limit, int windowSeconds, RateLimitFilter.KeyType keyType) {
        RateLimitFilter.Config config = new RateLimitFilter.Config();
        config.setLimit(limit);
        config.setWindowSeconds(windowSeconds);
        config.setKeyType(keyType);
        return config;
    }

    /**
     * CORS配置
     */
    @Bean
    public CorsWebFilter corsWebFilter() {
        CorsConfiguration corsConfig = new CorsConfiguration();
        corsConfig.setAllowCredentials(true);
        corsConfig.addAllowedOriginPattern("*");
        corsConfig.addAllowedHeader("*");
        corsConfig.addAllowedMethod("*");
        corsConfig.setMaxAge(3600L);

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", corsConfig);

        return new CorsWebFilter(source);
    }

}
