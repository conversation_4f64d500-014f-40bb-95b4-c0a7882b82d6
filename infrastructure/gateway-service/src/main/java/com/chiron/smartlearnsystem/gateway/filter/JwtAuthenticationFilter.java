package com.chiron.smartlearnsystem.gateway.filter;

import com.chiron.smartlearnsystem.gateway.service.JwtTokenService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;

/**
 * JWT认证过滤器
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JwtAuthenticationFilter extends AbstractGatewayFilterFactory<JwtAuthenticationFilter.Config> {

    private final JwtTokenService jwtTokenService;

    /**
     * 不需要认证的路径
     */
    private static final List<String> SKIP_AUTH_PATHS = Arrays.asList(
            "/api/v1/auth/login",
            "/api/v1/auth/register",
            "/api/v1/auth/refresh",
            "/api/v1/auth/logout",
            "/api/v1/auth/forgot-password",
            "/api/v1/auth/reset-password",
            "/api/v1/auth/verify-email",
            "/actuator/health",
            "/actuator/info",
            "/ws/notifications",
            "/favicon.ico"
    );

    public JwtAuthenticationFilter() {
        super(Config.class);
    }

    @Override
    public GatewayFilter apply(Config config) {
        return (exchange, chain) -> {
            ServerHttpRequest request = exchange.getRequest();
            String path = request.getURI().getPath();

            log.debug("处理请求: {} {}", request.getMethod(), path);

            // 检查是否需要跳过认证
            if (shouldSkipAuth(path)) {
                log.debug("跳过认证: {}", path);
                return chain.filter(exchange);
            }

            // 获取Authorization头
            String authHeader = request.getHeaders().getFirst(HttpHeaders.AUTHORIZATION);
            if (!StringUtils.hasText(authHeader) || !authHeader.startsWith("Bearer ")) {
                log.warn("缺少或无效的Authorization头: {}", path);
                return handleUnauthorized(exchange, "缺少认证令牌");
            }

            // 提取JWT令牌
            String token = authHeader.substring(7);
            
            try {
                // 验证JWT令牌
                if (!jwtTokenService.validateToken(token)) {
                    log.warn("无效的JWT令牌: {}", path);
                    return handleUnauthorized(exchange, "无效的认证令牌");
                }

                // 提取用户信息
                String userId = jwtTokenService.getUserIdFromToken(token);
                String username = jwtTokenService.getUsernameFromToken(token);
                List<String> roles = jwtTokenService.getRolesFromToken(token);

                // 将用户信息添加到请求头中，传递给下游服务
                ServerHttpRequest modifiedRequest = request.mutate()
                        .header("X-User-Id", userId)
                        .header("X-Username", username)
                        .header("X-User-Roles", String.join(",", roles))
                        .build();

                log.debug("认证成功: userId={}, username={}, roles={}", userId, username, roles);

                return chain.filter(exchange.mutate().request(modifiedRequest).build());

            } catch (Exception e) {
                log.error("JWT令牌验证失败: {}", e.getMessage());
                return handleUnauthorized(exchange, "认证令牌验证失败");
            }
        };
    }

    /**
     * 检查是否应该跳过认证
     */
    private boolean shouldSkipAuth(String path) {
        return SKIP_AUTH_PATHS.stream().anyMatch(skipPath -> 
                path.equals(skipPath) || path.startsWith(skipPath + "/"));
    }

    /**
     * 处理未授权请求
     */
    private Mono<Void> handleUnauthorized(org.springframework.web.server.ServerWebExchange exchange, String message) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.UNAUTHORIZED);
        response.getHeaders().add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);

        String body = String.format("{\"code\":401,\"message\":\"%s\",\"data\":null,\"timestamp\":\"%s\"}", 
                message, java.time.Instant.now());
        
        DataBuffer buffer = response.bufferFactory().wrap(body.getBytes(StandardCharsets.UTF_8));
        return response.writeWith(Mono.just(buffer));
    }

    /**
     * 配置类
     */
    public static class Config {
        // 可以添加配置参数
    }

}
