package com.chiron.smartlearnsystem.gateway;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;

/**
 * API网关服务启动类
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@SpringBootApplication(scanBasePackages = {
    "com.chiron.smartlearnsystem.gateway",
    "com.chiron.smartlearnsystem.common"
})
@EnableEurekaClient
public class GatewayServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(GatewayServiceApplication.class, args);
    }

}
