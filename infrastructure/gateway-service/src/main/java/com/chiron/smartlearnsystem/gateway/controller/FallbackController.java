package com.chiron.smartlearnsystem.gateway.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

/**
 * 降级处理控制器
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/fallback")
public class FallbackController {

    /**
     * 用户服务降级
     */
    @GetMapping("/user-service")
    public ResponseEntity<Map<String, Object>> userServiceFallback() {
        log.warn("用户服务降级处理");
        return createFallbackResponse("用户服务暂时不可用，请稍后重试");
    }

    /**
     * 题库服务降级
     */
    @GetMapping("/question-service")
    public ResponseEntity<Map<String, Object>> questionServiceFallback() {
        log.warn("题库服务降级处理");
        return createFallbackResponse("题库服务暂时不可用，请稍后重试");
    }

    /**
     * 学习服务降级
     */
    @GetMapping("/learning-service")
    public ResponseEntity<Map<String, Object>> learningServiceFallback() {
        log.warn("学习服务降级处理");
        return createFallbackResponse("学习服务暂时不可用，请稍后重试");
    }

    /**
     * AI服务降级
     */
    @GetMapping("/ai-service")
    public ResponseEntity<Map<String, Object>> aiServiceFallback() {
        log.warn("AI服务降级处理");
        return createFallbackResponse("AI服务暂时不可用，请稍后重试");
    }

    /**
     * 分析服务降级
     */
    @GetMapping("/analytics-service")
    public ResponseEntity<Map<String, Object>> analyticsServiceFallback() {
        log.warn("分析服务降级处理");
        return createFallbackResponse("分析服务暂时不可用，请稍后重试");
    }

    /**
     * 通知服务降级
     */
    @GetMapping("/notification-service")
    public ResponseEntity<Map<String, Object>> notificationServiceFallback() {
        log.warn("通知服务降级处理");
        return createFallbackResponse("通知服务暂时不可用，请稍后重试");
    }

    /**
     * 创建降级响应
     */
    private ResponseEntity<Map<String, Object>> createFallbackResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("code", 503);
        response.put("message", message);
        response.put("data", null);
        response.put("timestamp", Instant.now());
        response.put("fallback", true);
        
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(response);
    }

}
