package com.chiron.smartlearnsystem.gateway.filter;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.data.redis.core.ReactiveStringRedisTemplate;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Arrays;
import java.util.List;

/**
 * 限流过滤器
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RateLimitFilter extends AbstractGatewayFilterFactory<RateLimitFilter.Config> {

    private final ReactiveStringRedisTemplate redisTemplate;

    /**
     * Redis Lua脚本，实现滑动窗口限流
     */
    private static final String RATE_LIMIT_SCRIPT = 
            "local key = KEYS[1]\n" +
            "local window = tonumber(ARGV[1])\n" +
            "local limit = tonumber(ARGV[2])\n" +
            "local current = tonumber(redis.call('GET', key) or '0')\n" +
            "if current < limit then\n" +
            "    redis.call('INCR', key)\n" +
            "    redis.call('EXPIRE', key, window)\n" +
            "    return {1, limit - current - 1}\n" +
            "else\n" +
            "    return {0, 0}\n" +
            "end";

    public RateLimitFilter() {
        super(Config.class);
    }

    @Override
    public GatewayFilter apply(Config config) {
        return (exchange, chain) -> {
            ServerHttpRequest request = exchange.getRequest();
            
            // 获取限流键
            String rateLimitKey = getRateLimitKey(request, config);
            
            // 执行限流检查
            return checkRateLimit(rateLimitKey, config)
                    .flatMap(result -> {
                        boolean allowed = result.get(0) == 1;
                        long remaining = result.get(1);
                        
                        if (allowed) {
                            // 添加限流信息到响应头
                            ServerHttpResponse response = exchange.getResponse();
                            response.getHeaders().add("X-RateLimit-Limit", String.valueOf(config.getLimit()));
                            response.getHeaders().add("X-RateLimit-Remaining", String.valueOf(remaining));
                            response.getHeaders().add("X-RateLimit-Window", String.valueOf(config.getWindowSeconds()));
                            
                            log.debug("限流检查通过: key={}, remaining={}", rateLimitKey, remaining);
                            return chain.filter(exchange);
                        } else {
                            log.warn("触发限流: key={}, limit={}", rateLimitKey, config.getLimit());
                            return handleRateLimitExceeded(exchange, config);
                        }
                    });
        };
    }

    /**
     * 生成限流键
     */
    private String getRateLimitKey(ServerHttpRequest request, Config config) {
        String keyPrefix = "rate_limit:";
        
        switch (config.getKeyType()) {
            case IP:
                String clientIp = getClientIp(request);
                return keyPrefix + "ip:" + clientIp;
            case USER:
                String userId = request.getHeaders().getFirst("X-User-Id");
                return keyPrefix + "user:" + (userId != null ? userId : "anonymous");
            case API:
                String path = request.getURI().getPath();
                return keyPrefix + "api:" + path;
            case IP_API:
                String ip = getClientIp(request);
                String apiPath = request.getURI().getPath();
                return keyPrefix + "ip_api:" + ip + ":" + apiPath;
            case USER_API:
                String user = request.getHeaders().getFirst("X-User-Id");
                String api = request.getURI().getPath();
                return keyPrefix + "user_api:" + (user != null ? user : "anonymous") + ":" + api;
            default:
                return keyPrefix + "global";
        }
    }

    /**
     * 获取客户端IP
     */
    private String getClientIp(ServerHttpRequest request) {
        String xForwardedFor = request.getHeaders().getFirst("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeaders().getFirst("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return request.getRemoteAddress() != null ? 
                request.getRemoteAddress().getAddress().getHostAddress() : "unknown";
    }

    /**
     * 执行限流检查
     */
    private Mono<List<Long>> checkRateLimit(String key, Config config) {
        RedisScript<List> script = RedisScript.of(RATE_LIMIT_SCRIPT, List.class);
        
        return redisTemplate.execute(script, 
                Arrays.asList(key), 
                String.valueOf(config.getWindowSeconds()), 
                String.valueOf(config.getLimit()))
                .cast(List.class)
                .map(result -> Arrays.asList(
                        ((Number) result.get(0)).longValue(),
                        ((Number) result.get(1)).longValue()))
                .onErrorReturn(Arrays.asList(1L, config.getLimit() - 1L)); // 出错时允许通过
    }

    /**
     * 处理限流超出
     */
    private Mono<Void> handleRateLimitExceeded(org.springframework.web.server.ServerWebExchange exchange, Config config) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.TOO_MANY_REQUESTS);
        response.getHeaders().add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
        response.getHeaders().add("X-RateLimit-Limit", String.valueOf(config.getLimit()));
        response.getHeaders().add("X-RateLimit-Remaining", "0");
        response.getHeaders().add("X-RateLimit-Window", String.valueOf(config.getWindowSeconds()));
        response.getHeaders().add("Retry-After", String.valueOf(config.getWindowSeconds()));

        String body = String.format(
                "{\"code\":429,\"message\":\"请求过于频繁，请稍后再试\",\"data\":null,\"timestamp\":\"%s\"}", 
                java.time.Instant.now());
        
        DataBuffer buffer = response.bufferFactory().wrap(body.getBytes(StandardCharsets.UTF_8));
        return response.writeWith(Mono.just(buffer));
    }

    /**
     * 配置类
     */
    public static class Config {
        private int limit = 100; // 默认限制100次
        private int windowSeconds = 60; // 默认时间窗口60秒
        private KeyType keyType = KeyType.IP; // 默认按IP限流

        public int getLimit() {
            return limit;
        }

        public void setLimit(int limit) {
            this.limit = limit;
        }

        public int getWindowSeconds() {
            return windowSeconds;
        }

        public void setWindowSeconds(int windowSeconds) {
            this.windowSeconds = windowSeconds;
        }

        public KeyType getKeyType() {
            return keyType;
        }

        public void setKeyType(KeyType keyType) {
            this.keyType = keyType;
        }
    }

    /**
     * 限流键类型
     */
    public enum KeyType {
        IP,        // 按IP限流
        USER,      // 按用户限流
        API,       // 按API限流
        IP_API,    // 按IP+API限流
        USER_API,  // 按用户+API限流
        GLOBAL     // 全局限流
    }

}
