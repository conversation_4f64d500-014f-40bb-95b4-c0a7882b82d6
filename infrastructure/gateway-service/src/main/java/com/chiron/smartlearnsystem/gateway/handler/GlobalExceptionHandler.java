package com.chiron.smartlearnsystem.gateway.handler;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.reactive.error.ErrorWebExceptionHandler;
import org.springframework.cloud.gateway.support.NotFoundException;
import org.springframework.core.annotation.Order;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.time.Instant;

/**
 * 全局异常处理器
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@Order(-1)
@Component
public class GlobalExceptionHandler implements ErrorWebExceptionHandler {

    @Override
    public Mono<Void> handle(ServerWebExchange exchange, Throwable ex) {
        ServerHttpResponse response = exchange.getResponse();
        
        if (response.isCommitted()) {
            return Mono.error(ex);
        }

        // 设置响应头
        response.getHeaders().add("Content-Type", MediaType.APPLICATION_JSON_VALUE);

        HttpStatus status;
        String message;
        int code;

        if (ex instanceof NotFoundException) {
            status = HttpStatus.NOT_FOUND;
            code = 404;
            message = "服务不可用";
            log.warn("服务不可用: {}", ex.getMessage());
        } else if (ex instanceof ResponseStatusException) {
            ResponseStatusException responseStatusException = (ResponseStatusException) ex;
            status = responseStatusException.getStatus();
            code = status.value();
            message = responseStatusException.getReason() != null ? 
                     responseStatusException.getReason() : status.getReasonPhrase();
            log.warn("响应状态异常: {} - {}", code, message);
        } else if (ex instanceof java.net.ConnectException) {
            status = HttpStatus.SERVICE_UNAVAILABLE;
            code = 503;
            message = "服务连接失败";
            log.error("服务连接失败: {}", ex.getMessage());
        } else if (ex instanceof java.util.concurrent.TimeoutException) {
            status = HttpStatus.GATEWAY_TIMEOUT;
            code = 504;
            message = "服务响应超时";
            log.error("服务响应超时: {}", ex.getMessage());
        } else {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            code = 500;
            message = "网关内部错误";
            log.error("网关内部错误", ex);
        }

        response.setStatusCode(status);

        String body = buildErrorResponse(code, message);
        DataBuffer buffer = response.bufferFactory().wrap(body.getBytes(StandardCharsets.UTF_8));
        
        return response.writeWith(Mono.just(buffer));
    }

    /**
     * 构建错误响应
     */
    private String buildErrorResponse(int code, String message) {
        return String.format(
                "{\"code\":%d,\"message\":\"%s\",\"data\":null,\"timestamp\":\"%s\",\"path\":\"%s\"}", 
                code, message, Instant.now(), "gateway");
    }

}
