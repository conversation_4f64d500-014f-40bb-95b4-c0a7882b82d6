package com.chiron.smartlearnsystem.gateway.service;

import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.crypto.SecretKey;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * JWT令牌服务
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@Service
public class JwtTokenService {

    @Value("${smartlearn.jwt.secret:smartlearn-jwt-secret-key-for-authentication-and-authorization}")
    private String jwtSecret;

    @Value("${smartlearn.jwt.expiration:86400}")
    private int jwtExpirationInSeconds;

    private SecretKey secretKey;

    @PostConstruct
    public void init() {
        // 确保密钥长度足够
        if (jwtSecret.length() < 32) {
            jwtSecret = jwtSecret + "0".repeat(32 - jwtSecret.length());
        }
        this.secretKey = Keys.hmacShaKeyFor(jwtSecret.getBytes());
        log.info("JWT令牌服务初始化完成，过期时间: {}秒", jwtExpirationInSeconds);
    }

    /**
     * 验证JWT令牌
     */
    public boolean validateToken(String token) {
        try {
            Jwts.parserBuilder()
                    .setSigningKey(secretKey)
                    .build()
                    .parseClaimsJws(token);
            return true;
        } catch (SecurityException e) {
            log.error("无效的JWT签名: {}", e.getMessage());
        } catch (MalformedJwtException e) {
            log.error("无效的JWT令牌: {}", e.getMessage());
        } catch (ExpiredJwtException e) {
            log.error("JWT令牌已过期: {}", e.getMessage());
        } catch (UnsupportedJwtException e) {
            log.error("不支持的JWT令牌: {}", e.getMessage());
        } catch (IllegalArgumentException e) {
            log.error("JWT令牌为空: {}", e.getMessage());
        }
        return false;
    }

    /**
     * 从令牌中获取用户ID
     */
    public String getUserIdFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims.get("userId", String.class);
    }

    /**
     * 从令牌中获取用户名
     */
    public String getUsernameFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims.getSubject();
    }

    /**
     * 从令牌中获取用户角色
     */
    @SuppressWarnings("unchecked")
    public List<String> getRolesFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        Object rolesObj = claims.get("roles");
        
        if (rolesObj instanceof List) {
            return (List<String>) rolesObj;
        } else if (rolesObj instanceof String) {
            return Arrays.asList(((String) rolesObj).split(","));
        } else {
            return Arrays.asList("USER"); // 默认角色
        }
    }

    /**
     * 从令牌中获取过期时间
     */
    public Date getExpirationDateFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims.getExpiration();
    }

    /**
     * 检查令牌是否过期
     */
    public boolean isTokenExpired(String token) {
        Date expiration = getExpirationDateFromToken(token);
        return expiration.before(new Date());
    }

    /**
     * 从令牌中获取所有声明
     */
    private Claims getClaimsFromToken(String token) {
        return Jwts.parserBuilder()
                .setSigningKey(secretKey)
                .build()
                .parseClaimsJws(token)
                .getBody();
    }

    /**
     * 获取令牌剩余有效时间（秒）
     */
    public long getTokenRemainingTime(String token) {
        try {
            Date expiration = getExpirationDateFromToken(token);
            long remainingTime = (expiration.getTime() - System.currentTimeMillis()) / 1000;
            return Math.max(0, remainingTime);
        } catch (Exception e) {
            log.error("获取令牌剩余时间失败: {}", e.getMessage());
            return 0;
        }
    }

    /**
     * 检查令牌是否即将过期（30分钟内）
     */
    public boolean isTokenExpiringSoon(String token) {
        long remainingTime = getTokenRemainingTime(token);
        return remainingTime > 0 && remainingTime < 1800; // 30分钟
    }

    /**
     * 从令牌中获取用户权限
     */
    @SuppressWarnings("unchecked")
    public List<String> getAuthoritiesFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        Object authoritiesObj = claims.get("authorities");
        
        if (authoritiesObj instanceof List) {
            return (List<String>) authoritiesObj;
        } else if (authoritiesObj instanceof String) {
            return Arrays.asList(((String) authoritiesObj).split(","));
        } else {
            return Arrays.asList(); // 空权限列表
        }
    }

    /**
     * 检查用户是否有指定角色
     */
    public boolean hasRole(String token, String role) {
        List<String> roles = getRolesFromToken(token);
        return roles.contains(role);
    }

    /**
     * 检查用户是否有指定权限
     */
    public boolean hasAuthority(String token, String authority) {
        List<String> authorities = getAuthoritiesFromToken(token);
        return authorities.contains(authority);
    }

}
