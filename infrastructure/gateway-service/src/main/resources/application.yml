server:
  port: 8080

spring:
  application:
    name: gateway-service
  
  profiles:
    active: dev
  
  cloud:
    gateway:
      # 全局CORS配置
      globalcors:
        cors-configurations:
          '[/**]':
            allowedOriginPatterns: "*"
            allowedMethods: "*"
            allowedHeaders: "*"
            allowCredentials: true
            maxAge: 3600
      
      # 默认过滤器
      default-filters:
        - DedupeResponseHeader=Access-Control-Allow-Credentials Access-Control-Allow-Origin
        - AddResponseHeader=X-Response-Default-Foo, Default-Bar
      
      # 发现定位器
      discovery:
        locator:
          enabled: true
          lower-case-service-id: true
      
      # 路由配置
      routes:
        # 用户服务
        - id: user-service
          uri: lb://user-service
          predicates:
            - Path=/api/v1/auth/**,/api/v1/users/**
          filters:
            - StripPrefix=0
        
        # 题库服务
        - id: question-service
          uri: lb://question-service
          predicates:
            - Path=/api/v1/questions/**,/api/v1/exams/**
          filters:
            - StripPrefix=0
        
        # 学习服务
        - id: learning-service
          uri: lb://learning-service
          predicates:
            - Path=/api/v1/learning/**,/api/v1/plans/**,/api/v1/progress/**
          filters:
            - StripPrefix=0
        
        # AI服务
        - id: ai-service
          uri: lb://ai-service
          predicates:
            - Path=/api/v1/ai/**,/api/v1/recommendations/**,/api/v1/assessments/**
          filters:
            - StripPrefix=0
        
        # 分析服务
        - id: analytics-service
          uri: lb://analytics-service
          predicates:
            - Path=/api/v1/analytics/**
          filters:
            - StripPrefix=0
        
        # 通知服务
        - id: notification-service
          uri: lb://notification-service
          predicates:
            - Path=/api/v1/notifications/**,/ws/notifications/**
          filters:
            - StripPrefix=0
      
      # 熔断器配置
      httpclient:
        connect-timeout: 3000
        response-timeout: 10s
        pool:
          type: elastic
          max-idle-time: 15s
          max-life-time: 60s
  
  redis:
    host: localhost
    port: 6379
    password: 
    database: 6
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0

eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
    register-with-eureka: true
    fetch-registry: true
  instance:
    prefer-ip-address: true
    lease-renewal-interval-in-seconds: 30
    lease-expiration-duration-in-seconds: 90

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,gateway
  endpoint:
    health:
      show-details: when-authorized
    gateway:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true

logging:
  level:
    com.chiron.smartlearnsystem.gateway: INFO
    org.springframework.cloud.gateway: DEBUG
    org.springframework.security: DEBUG
    reactor.netty: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/gateway-service.log

# 自定义配置
smartlearn:
  # JWT配置
  jwt:
    secret: smartlearn-jwt-secret-key-for-authentication-and-authorization
    expiration: 86400  # 24小时
  
  # 网关配置
  gateway:
    # 限流配置
    rate-limit:
      default-limit: 100
      default-window: 60
      redis-key-prefix: "smartlearn:rate_limit:"
    
    # 熔断器配置
    circuit-breaker:
      failure-rate-threshold: 50
      wait-duration-in-open-state: 30s
      sliding-window-size: 10
      minimum-number-of-calls: 5
    
    # 重试配置
    retry:
      max-attempts: 3
      wait-duration: 100ms
      multiplier: 2
    
    # 超时配置
    timeout:
      connect: 3s
      response: 10s
    
    # 安全配置
    security:
      skip-auth-paths:
        - /api/v1/auth/login
        - /api/v1/auth/register
        - /api/v1/auth/refresh
        - /api/v1/auth/logout
        - /api/v1/auth/forgot-password
        - /api/v1/auth/reset-password
        - /api/v1/auth/verify-email
        - /actuator/health
        - /actuator/info
        - /ws/notifications
        - /favicon.ico
      
      cors:
        allowed-origins: "*"
        allowed-methods: "*"
        allowed-headers: "*"
        allow-credentials: true
        max-age: 3600
    
    # 监控配置
    monitoring:
      metrics-enabled: true
      tracing-enabled: true
      logging-enabled: true
