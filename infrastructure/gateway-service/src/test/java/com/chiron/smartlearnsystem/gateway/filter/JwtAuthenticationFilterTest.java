package com.chiron.smartlearnsystem.gateway.filter;

import com.chiron.smartlearnsystem.gateway.service.JwtTokenService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.mock.http.server.reactive.MockServerHttpRequest;
import org.springframework.mock.web.server.MockServerWebExchange;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.Arrays;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * JWT认证过滤器测试
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@ExtendWith(MockitoExtension.class)
class JwtAuthenticationFilterTest {

    @Mock
    private JwtTokenService jwtTokenService;

    @Mock
    private GatewayFilterChain filterChain;

    @InjectMocks
    private JwtAuthenticationFilter jwtAuthenticationFilter;

    private ServerWebExchange exchange;

    @BeforeEach
    void setUp() {
        MockServerHttpRequest request = MockServerHttpRequest.get("/api/v1/users/profile").build();
        exchange = MockServerWebExchange.from(request);
        
        when(filterChain.filter(any(ServerWebExchange.class))).thenReturn(Mono.empty());
    }

    @Test
    void testSkipAuthForWhitelistedPaths() {
        // Given
        MockServerHttpRequest request = MockServerHttpRequest.get("/api/v1/auth/login").build();
        ServerWebExchange whitelistExchange = MockServerWebExchange.from(request);

        // When
        Mono<Void> result = jwtAuthenticationFilter.apply(new JwtAuthenticationFilter.Config())
                .filter(whitelistExchange, filterChain);

        // Then
        StepVerifier.create(result)
                .verifyComplete();

        verify(filterChain).filter(whitelistExchange);
        verifyNoInteractions(jwtTokenService);
    }

    @Test
    void testMissingAuthorizationHeader() {
        // When
        Mono<Void> result = jwtAuthenticationFilter.apply(new JwtAuthenticationFilter.Config())
                .filter(exchange, filterChain);

        // Then
        StepVerifier.create(result)
                .verifyComplete();

        ServerHttpResponse response = exchange.getResponse();
        assert response.getStatusCode() == HttpStatus.UNAUTHORIZED;
        verifyNoInteractions(filterChain);
    }

    @Test
    void testInvalidAuthorizationHeaderFormat() {
        // Given
        MockServerHttpRequest request = MockServerHttpRequest.get("/api/v1/users/profile")
                .header(HttpHeaders.AUTHORIZATION, "InvalidFormat token")
                .build();
        ServerWebExchange invalidExchange = MockServerWebExchange.from(request);

        // When
        Mono<Void> result = jwtAuthenticationFilter.apply(new JwtAuthenticationFilter.Config())
                .filter(invalidExchange, filterChain);

        // Then
        StepVerifier.create(result)
                .verifyComplete();

        ServerHttpResponse response = invalidExchange.getResponse();
        assert response.getStatusCode() == HttpStatus.UNAUTHORIZED;
        verifyNoInteractions(filterChain);
    }

    @Test
    void testValidJwtToken() {
        // Given
        String validToken = "valid.jwt.token";
        MockServerHttpRequest request = MockServerHttpRequest.get("/api/v1/users/profile")
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + validToken)
                .build();
        ServerWebExchange validExchange = MockServerWebExchange.from(request);

        when(jwtTokenService.validateToken(validToken)).thenReturn(true);
        when(jwtTokenService.getUserIdFromToken(validToken)).thenReturn("123");
        when(jwtTokenService.getUsernameFromToken(validToken)).thenReturn("testuser");
        when(jwtTokenService.getRolesFromToken(validToken)).thenReturn(Arrays.asList("USER", "ADMIN"));

        // When
        Mono<Void> result = jwtAuthenticationFilter.apply(new JwtAuthenticationFilter.Config())
                .filter(validExchange, filterChain);

        // Then
        StepVerifier.create(result)
                .verifyComplete();

        verify(jwtTokenService).validateToken(validToken);
        verify(jwtTokenService).getUserIdFromToken(validToken);
        verify(jwtTokenService).getUsernameFromToken(validToken);
        verify(jwtTokenService).getRolesFromToken(validToken);
        verify(filterChain).filter(any(ServerWebExchange.class));

        // Verify headers are added
        ServerHttpRequest modifiedRequest = validExchange.getRequest();
        assert modifiedRequest.getHeaders().getFirst("X-User-Id").equals("123");
        assert modifiedRequest.getHeaders().getFirst("X-Username").equals("testuser");
        assert modifiedRequest.getHeaders().getFirst("X-User-Roles").equals("USER,ADMIN");
    }

    @Test
    void testInvalidJwtToken() {
        // Given
        String invalidToken = "invalid.jwt.token";
        MockServerHttpRequest request = MockServerHttpRequest.get("/api/v1/users/profile")
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + invalidToken)
                .build();
        ServerWebExchange invalidExchange = MockServerWebExchange.from(request);

        when(jwtTokenService.validateToken(invalidToken)).thenReturn(false);

        // When
        Mono<Void> result = jwtAuthenticationFilter.apply(new JwtAuthenticationFilter.Config())
                .filter(invalidExchange, filterChain);

        // Then
        StepVerifier.create(result)
                .verifyComplete();

        verify(jwtTokenService).validateToken(invalidToken);
        verifyNoMoreInteractions(jwtTokenService);
        verifyNoInteractions(filterChain);

        ServerHttpResponse response = invalidExchange.getResponse();
        assert response.getStatusCode() == HttpStatus.UNAUTHORIZED;
    }

    @Test
    void testJwtTokenValidationException() {
        // Given
        String tokenWithException = "exception.jwt.token";
        MockServerHttpRequest request = MockServerHttpRequest.get("/api/v1/users/profile")
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + tokenWithException)
                .build();
        ServerWebExchange exceptionExchange = MockServerWebExchange.from(request);

        when(jwtTokenService.validateToken(tokenWithException))
                .thenThrow(new RuntimeException("Token validation error"));

        // When
        Mono<Void> result = jwtAuthenticationFilter.apply(new JwtAuthenticationFilter.Config())
                .filter(exceptionExchange, filterChain);

        // Then
        StepVerifier.create(result)
                .verifyComplete();

        verify(jwtTokenService).validateToken(tokenWithException);
        verifyNoInteractions(filterChain);

        ServerHttpResponse response = exceptionExchange.getResponse();
        assert response.getStatusCode() == HttpStatus.UNAUTHORIZED;
    }

    @Test
    void testHealthCheckEndpoint() {
        // Given
        MockServerHttpRequest request = MockServerHttpRequest.get("/actuator/health").build();
        ServerWebExchange healthExchange = MockServerWebExchange.from(request);

        // When
        Mono<Void> result = jwtAuthenticationFilter.apply(new JwtAuthenticationFilter.Config())
                .filter(healthExchange, filterChain);

        // Then
        StepVerifier.create(result)
                .verifyComplete();

        verify(filterChain).filter(healthExchange);
        verifyNoInteractions(jwtTokenService);
    }

    @Test
    void testWebSocketEndpoint() {
        // Given
        MockServerHttpRequest request = MockServerHttpRequest.get("/ws/notifications").build();
        ServerWebExchange wsExchange = MockServerWebExchange.from(request);

        // When
        Mono<Void> result = jwtAuthenticationFilter.apply(new JwtAuthenticationFilter.Config())
                .filter(wsExchange, filterChain);

        // Then
        StepVerifier.create(result)
                .verifyComplete();

        verify(filterChain).filter(wsExchange);
        verifyNoInteractions(jwtTokenService);
    }

}
