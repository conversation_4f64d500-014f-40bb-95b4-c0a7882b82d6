package com.chiron.smartlearnsystem.gateway.filter;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.data.redis.core.ReactiveStringRedisTemplate;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.mock.http.server.reactive.MockServerHttpRequest;
import org.springframework.mock.web.server.MockServerWebExchange;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 限流过滤器测试
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@ExtendWith(MockitoExtension.class)
class RateLimitFilterTest {

    @Mock
    private ReactiveStringRedisTemplate redisTemplate;

    @Mock
    private GatewayFilterChain filterChain;

    @InjectMocks
    private RateLimitFilter rateLimitFilter;

    private ServerWebExchange exchange;
    private RateLimitFilter.Config config;

    @BeforeEach
    void setUp() {
        MockServerHttpRequest request = MockServerHttpRequest.get("/api/v1/users/profile")
                .remoteAddress(java.net.InetSocketAddress.createUnresolved("***********", 8080))
                .build();
        exchange = MockServerWebExchange.from(request);
        
        config = new RateLimitFilter.Config();
        config.setLimit(10);
        config.setWindowSeconds(60);
        config.setKeyType(RateLimitFilter.KeyType.IP);
        
        when(filterChain.filter(any(ServerWebExchange.class))).thenReturn(Mono.empty());
    }

    @Test
    void testRateLimitAllowed() {
        // Given
        List<Long> allowedResult = Arrays.asList(1L, 9L); // allowed=1, remaining=9
        when(redisTemplate.execute(any(RedisScript.class), anyList(), anyString(), anyString()))
                .thenReturn(Mono.just(allowedResult));

        // When
        Mono<Void> result = rateLimitFilter.apply(config).filter(exchange, filterChain);

        // Then
        StepVerifier.create(result)
                .verifyComplete();

        verify(filterChain).filter(any(ServerWebExchange.class));
        
        // Verify rate limit headers are added
        ServerHttpResponse response = exchange.getResponse();
        assert response.getHeaders().getFirst("X-RateLimit-Limit").equals("10");
        assert response.getHeaders().getFirst("X-RateLimit-Remaining").equals("9");
        assert response.getHeaders().getFirst("X-RateLimit-Window").equals("60");
    }

    @Test
    void testRateLimitExceeded() {
        // Given
        List<Long> blockedResult = Arrays.asList(0L, 0L); // allowed=0, remaining=0
        when(redisTemplate.execute(any(RedisScript.class), anyList(), anyString(), anyString()))
                .thenReturn(Mono.just(blockedResult));

        // When
        Mono<Void> result = rateLimitFilter.apply(config).filter(exchange, filterChain);

        // Then
        StepVerifier.create(result)
                .verifyComplete();

        verifyNoInteractions(filterChain);
        
        ServerHttpResponse response = exchange.getResponse();
        assert response.getStatusCode() == HttpStatus.TOO_MANY_REQUESTS;
        assert response.getHeaders().getFirst("X-RateLimit-Limit").equals("10");
        assert response.getHeaders().getFirst("X-RateLimit-Remaining").equals("0");
        assert response.getHeaders().getFirst("Retry-After").equals("60");
    }

    @Test
    void testRateLimitByUser() {
        // Given
        config.setKeyType(RateLimitFilter.KeyType.USER);
        MockServerHttpRequest request = MockServerHttpRequest.get("/api/v1/users/profile")
                .header("X-User-Id", "123")
                .build();
        ServerWebExchange userExchange = MockServerWebExchange.from(request);

        List<Long> allowedResult = Arrays.asList(1L, 5L);
        when(redisTemplate.execute(any(RedisScript.class), anyList(), anyString(), anyString()))
                .thenReturn(Mono.just(allowedResult));

        // When
        Mono<Void> result = rateLimitFilter.apply(config).filter(userExchange, filterChain);

        // Then
        StepVerifier.create(result)
                .verifyComplete();

        verify(filterChain).filter(any(ServerWebExchange.class));
        verify(redisTemplate).execute(any(RedisScript.class), 
                eq(Arrays.asList("rate_limit:user:123")), anyString(), anyString());
    }

    @Test
    void testRateLimitByAPI() {
        // Given
        config.setKeyType(RateLimitFilter.KeyType.API);
        
        List<Long> allowedResult = Arrays.asList(1L, 8L);
        when(redisTemplate.execute(any(RedisScript.class), anyList(), anyString(), anyString()))
                .thenReturn(Mono.just(allowedResult));

        // When
        Mono<Void> result = rateLimitFilter.apply(config).filter(exchange, filterChain);

        // Then
        StepVerifier.create(result)
                .verifyComplete();

        verify(filterChain).filter(any(ServerWebExchange.class));
        verify(redisTemplate).execute(any(RedisScript.class), 
                eq(Arrays.asList("rate_limit:api:/api/v1/users/profile")), anyString(), anyString());
    }

    @Test
    void testRateLimitByIPAndAPI() {
        // Given
        config.setKeyType(RateLimitFilter.KeyType.IP_API);
        
        List<Long> allowedResult = Arrays.asList(1L, 7L);
        when(redisTemplate.execute(any(RedisScript.class), anyList(), anyString(), anyString()))
                .thenReturn(Mono.just(allowedResult));

        // When
        Mono<Void> result = rateLimitFilter.apply(config).filter(exchange, filterChain);

        // Then
        StepVerifier.create(result)
                .verifyComplete();

        verify(filterChain).filter(any(ServerWebExchange.class));
        verify(redisTemplate).execute(any(RedisScript.class), 
                eq(Arrays.asList("rate_limit:ip_api:***********:/api/v1/users/profile")), 
                anyString(), anyString());
    }

    @Test
    void testRateLimitWithXForwardedFor() {
        // Given
        MockServerHttpRequest request = MockServerHttpRequest.get("/api/v1/users/profile")
                .header("X-Forwarded-For", "********, ***********")
                .build();
        ServerWebExchange forwardedExchange = MockServerWebExchange.from(request);

        List<Long> allowedResult = Arrays.asList(1L, 6L);
        when(redisTemplate.execute(any(RedisScript.class), anyList(), anyString(), anyString()))
                .thenReturn(Mono.just(allowedResult));

        // When
        Mono<Void> result = rateLimitFilter.apply(config).filter(forwardedExchange, filterChain);

        // Then
        StepVerifier.create(result)
                .verifyComplete();

        verify(filterChain).filter(any(ServerWebExchange.class));
        verify(redisTemplate).execute(any(RedisScript.class), 
                eq(Arrays.asList("rate_limit:ip:********")), anyString(), anyString());
    }

    @Test
    void testRateLimitRedisError() {
        // Given
        when(redisTemplate.execute(any(RedisScript.class), anyList(), anyString(), anyString()))
                .thenReturn(Mono.error(new RuntimeException("Redis connection error")));

        // When
        Mono<Void> result = rateLimitFilter.apply(config).filter(exchange, filterChain);

        // Then
        StepVerifier.create(result)
                .verifyComplete();

        // Should allow request when Redis fails
        verify(filterChain).filter(any(ServerWebExchange.class));
    }

    @Test
    void testRateLimitAnonymousUser() {
        // Given
        config.setKeyType(RateLimitFilter.KeyType.USER);
        // No X-User-Id header, should use "anonymous"
        
        List<Long> allowedResult = Arrays.asList(1L, 4L);
        when(redisTemplate.execute(any(RedisScript.class), anyList(), anyString(), anyString()))
                .thenReturn(Mono.just(allowedResult));

        // When
        Mono<Void> result = rateLimitFilter.apply(config).filter(exchange, filterChain);

        // Then
        StepVerifier.create(result)
                .verifyComplete();

        verify(filterChain).filter(any(ServerWebExchange.class));
        verify(redisTemplate).execute(any(RedisScript.class), 
                eq(Arrays.asList("rate_limit:user:anonymous")), anyString(), anyString());
    }

}
