server:
  port: 8761

spring:
  application:
    name: eureka-server
  profiles:
    active: dev

eureka:
  instance:
    hostname: localhost
    prefer-ip-address: true
    lease-renewal-interval-in-seconds: 10
    lease-expiration-duration-in-seconds: 30
  client:
    register-with-eureka: false
    fetch-registry: false
    service-url:
      defaultZone: http://${eureka.instance.hostname}:${server.port}/eureka/
  server:
    enable-self-preservation: false
    eviction-interval-timer-in-ms: 5000
    renewal-percent-threshold: 0.85

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

logging:
  level:
    com.netflix.eureka: INFO
    com.netflix.discovery: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

---
spring:
  config:
    activate:
      on-profile: dev

logging:
  level:
    root: INFO
    com.chiron.smartlearnsystem: DEBUG

---
spring:
  config:
    activate:
      on-profile: prod

eureka:
  instance:
    hostname: eureka-server
  client:
    service-url:
      defaultZone: http://eureka-server:8761/eureka/

logging:
  level:
    root: WARN
    com.chiron.smartlearnsystem: INFO
