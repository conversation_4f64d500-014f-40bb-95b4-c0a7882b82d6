package com.chiron.smartlearnsystem.config.service;

import com.chiron.smartlearnsystem.config.entity.ConfigHistory;
import com.chiron.smartlearnsystem.config.entity.ConfigProperty;
import com.chiron.smartlearnsystem.config.repository.ConfigHistoryRepository;
import com.chiron.smartlearnsystem.config.repository.ConfigPropertyRepository;
import com.chiron.smartlearnsystem.common.core.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.bus.event.RefreshRemoteApplicationEvent;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 配置管理服务
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class ConfigManagementService {

    private final ConfigPropertyRepository configPropertyRepository;
    private final ConfigHistoryRepository configHistoryRepository;
    private final ConfigEncryptionService encryptionService;
    private final ApplicationEventPublisher eventPublisher;
    private final RedisTemplate<String, Object> redisTemplate;

    private static final String CONFIG_CACHE_KEY = "config:";
    private static final int CACHE_EXPIRE_MINUTES = 30;

    /**
     * 获取配置属性
     */
    @Transactional(readOnly = true)
    public Optional<ConfigProperty> getConfigProperty(String application, String profile, 
                                                     String label, String key) {
        log.debug("获取配置: app={}, profile={}, label={}, key={}", application, profile, label, key);

        // 先从缓存获取
        String cacheKey = buildCacheKey(application, profile, label, key);
        ConfigProperty cachedConfig = (ConfigProperty) redisTemplate.opsForValue().get(cacheKey);
        if (cachedConfig != null) {
            return Optional.of(cachedConfig);
        }

        // 从数据库获取
        Optional<ConfigProperty> configProperty = configPropertyRepository
                .findByApplicationAndProfileAndLabelAndPropertyKeyAndIsEnabledTrue(
                        application, profile, label, key);

        // 解密敏感配置
        if (configProperty.isPresent() && configProperty.get().getIsEncrypted()) {
            ConfigProperty config = configProperty.get();
            String decryptedValue = encryptionService.decrypt(config.getPropertyValue());
            config.setPropertyValue(decryptedValue);
        }

        // 缓存结果
        configProperty.ifPresent(config -> 
                redisTemplate.opsForValue().set(cacheKey, config, CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES));

        return configProperty;
    }

    /**
     * 获取应用的所有配置
     */
    @Transactional(readOnly = true)
    public List<ConfigProperty> getApplicationConfigs(String application, String profile, String label) {
        log.info("获取应用配置: app={}, profile={}, label={}", application, profile, label);

        return configPropertyRepository.findByApplicationAndProfileAndLabelAndIsEnabledTrue(
                application, profile, label);
    }

    /**
     * 创建配置属性
     */
    public ConfigProperty createConfigProperty(ConfigProperty configProperty, String operator, String operatorIp) {
        log.info("创建配置: key={}, operator={}", configProperty.getPropertyKey(), operator);

        // 检查配置是否已存在
        Optional<ConfigProperty> existing = configPropertyRepository
                .findByApplicationAndProfileAndLabelAndPropertyKey(
                        configProperty.getApplication(),
                        configProperty.getProfile(),
                        configProperty.getLabel(),
                        configProperty.getPropertyKey());

        if (existing.isPresent()) {
            throw new BusinessException("配置已存在: " + configProperty.getPropertyKey());
        }

        // 加密敏感配置
        if (configProperty.getIsSensitive()) {
            String encryptedValue = encryptionService.encrypt(configProperty.getPropertyValue());
            configProperty.setPropertyValue(encryptedValue);
            configProperty.setIsEncrypted(true);
        }

        configProperty.setCreatedBy(operator);
        ConfigProperty savedConfig = configPropertyRepository.save(configProperty);

        // 记录历史
        recordConfigHistory(savedConfig, "CREATE", null, savedConfig.getPropertyValue(), 
                          operator, operatorIp, "创建配置");

        // 清除缓存
        clearConfigCache(savedConfig);

        // 发布配置更新事件
        publishConfigUpdateEvent(savedConfig);

        return savedConfig;
    }

    /**
     * 更新配置属性
     */
    public ConfigProperty updateConfigProperty(Long configId, String newValue, 
                                             String operator, String operatorIp, String reason) {
        log.info("更新配置: id={}, operator={}", configId, operator);

        ConfigProperty configProperty = configPropertyRepository.findById(configId)
                .orElseThrow(() -> new BusinessException("配置不存在"));

        String oldValue = configProperty.getPropertyValue();

        // 加密敏感配置
        if (configProperty.getIsSensitive()) {
            newValue = encryptionService.encrypt(newValue);
            configProperty.setIsEncrypted(true);
        }

        configProperty.setPropertyValue(newValue);
        configProperty.setUpdatedBy(operator);
        ConfigProperty savedConfig = configPropertyRepository.save(configProperty);

        // 记录历史
        recordConfigHistory(savedConfig, "UPDATE", oldValue, newValue, operator, operatorIp, reason);

        // 清除缓存
        clearConfigCache(savedConfig);

        // 发布配置更新事件
        publishConfigUpdateEvent(savedConfig);

        return savedConfig;
    }

    /**
     * 删除配置属性
     */
    public void deleteConfigProperty(Long configId, String operator, String operatorIp, String reason) {
        log.info("删除配置: id={}, operator={}", configId, operator);

        ConfigProperty configProperty = configPropertyRepository.findById(configId)
                .orElseThrow(() -> new BusinessException("配置不存在"));

        String oldValue = configProperty.getPropertyValue();

        // 软删除
        configProperty.setIsEnabled(false);
        configProperty.setUpdatedBy(operator);
        configPropertyRepository.save(configProperty);

        // 记录历史
        recordConfigHistory(configProperty, "DELETE", oldValue, null, operator, operatorIp, reason);

        // 清除缓存
        clearConfigCache(configProperty);

        // 发布配置更新事件
        publishConfigUpdateEvent(configProperty);
    }

    /**
     * 批量更新配置
     */
    public void batchUpdateConfigs(List<ConfigProperty> configs, String operator, String operatorIp) {
        log.info("批量更新配置: count={}, operator={}", configs.size(), operator);

        for (ConfigProperty config : configs) {
            if (config.getId() != null) {
                updateConfigProperty(config.getId(), config.getPropertyValue(), 
                                   operator, operatorIp, "批量更新");
            } else {
                createConfigProperty(config, operator, operatorIp);
            }
        }
    }

    /**
     * 回滚配置
     */
    public ConfigProperty rollbackConfig(Long historyId, String operator, String operatorIp) {
        log.info("回滚配置: historyId={}, operator={}", historyId, operator);

        ConfigHistory history = configHistoryRepository.findById(historyId)
                .orElseThrow(() -> new BusinessException("历史记录不存在"));

        ConfigProperty configProperty = configPropertyRepository.findById(history.getConfigId())
                .orElseThrow(() -> new BusinessException("配置不存在"));

        String currentValue = configProperty.getPropertyValue();
        String rollbackValue = history.getOldValue();

        configProperty.setPropertyValue(rollbackValue);
        configProperty.setUpdatedBy(operator);
        ConfigProperty savedConfig = configPropertyRepository.save(configProperty);

        // 记录回滚历史
        recordConfigHistory(savedConfig, "ROLLBACK", currentValue, rollbackValue, 
                          operator, operatorIp, "回滚到历史版本");

        // 清除缓存
        clearConfigCache(savedConfig);

        // 发布配置更新事件
        publishConfigUpdateEvent(savedConfig);

        return savedConfig;
    }

    /**
     * 获取配置历史
     */
    @Transactional(readOnly = true)
    public Page<ConfigHistory> getConfigHistory(Long configId, int page, int size) {
        log.debug("获取配置历史: configId={}", configId);

        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
        return configHistoryRepository.findByConfigId(configId, pageable);
    }

    /**
     * 刷新配置缓存
     */
    public void refreshConfigCache(String application, String profile, String label) {
        log.info("刷新配置缓存: app={}, profile={}, label={}", application, profile, label);

        // 清除应用相关的所有缓存
        String pattern = CONFIG_CACHE_KEY + application + ":" + profile + ":" + label + ":*";
        redisTemplate.delete(redisTemplate.keys(pattern));

        // 发布刷新事件
        RefreshRemoteApplicationEvent event = new RefreshRemoteApplicationEvent(
                this, "config-service", application + ":" + profile);
        eventPublisher.publishEvent(event);
    }

    /**
     * 记录配置历史
     */
    private void recordConfigHistory(ConfigProperty config, String operationType, 
                                   String oldValue, String newValue, String operator, 
                                   String operatorIp, String reason) {
        ConfigHistory history = ConfigHistory.builder()
                .configId(config.getId())
                .application(config.getApplication())
                .profile(config.getProfile())
                .label(config.getLabel())
                .propertyKey(config.getPropertyKey())
                .operationType(operationType)
                .oldValue(oldValue)
                .newValue(newValue)
                .operator(operator)
                .reason(reason)
                .operationIp(operatorIp)
                .operationTimestamp(System.currentTimeMillis())
                .configVersion(config.getConfigVersion())
                .build();

        configHistoryRepository.save(history);
    }

    /**
     * 构建缓存键
     */
    private String buildCacheKey(String application, String profile, String label, String key) {
        return CONFIG_CACHE_KEY + application + ":" + profile + ":" + label + ":" + key;
    }

    /**
     * 清除配置缓存
     */
    private void clearConfigCache(ConfigProperty config) {
        String cacheKey = buildCacheKey(config.getApplication(), config.getProfile(), 
                                       config.getLabel(), config.getPropertyKey());
        redisTemplate.delete(cacheKey);
    }

    /**
     * 发布配置更新事件
     */
    private void publishConfigUpdateEvent(ConfigProperty config) {
        RefreshRemoteApplicationEvent event = new RefreshRemoteApplicationEvent(
                this, "config-service", config.getApplication() + ":" + config.getProfile());
        eventPublisher.publishEvent(event);
    }

}
