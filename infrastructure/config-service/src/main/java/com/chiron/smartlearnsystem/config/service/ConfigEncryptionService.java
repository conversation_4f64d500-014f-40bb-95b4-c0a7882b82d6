package com.chiron.smartlearnsystem.config.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * 配置加密服务
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@Service
public class ConfigEncryptionService {

    @Value("${smartlearn.config.encryption.key:smartlearn-config-encryption-key-32}")
    private String encryptionKey;

    @Value("${smartlearn.config.encryption.algorithm:AES}")
    private String algorithm;

    private SecretKey secretKey;

    @PostConstruct
    public void init() {
        try {
            // 确保密钥长度为32字节（256位）
            if (encryptionKey.length() < 32) {
                encryptionKey = encryptionKey + "0".repeat(32 - encryptionKey.length());
            } else if (encryptionKey.length() > 32) {
                encryptionKey = encryptionKey.substring(0, 32);
            }
            
            this.secretKey = new SecretKeySpec(encryptionKey.getBytes(StandardCharsets.UTF_8), algorithm);
            log.info("配置加密服务初始化完成，算法: {}", algorithm);
        } catch (Exception e) {
            log.error("配置加密服务初始化失败", e);
            throw new RuntimeException("配置加密服务初始化失败", e);
        }
    }

    /**
     * 加密配置值
     */
    public String encrypt(String plainText) {
        if (plainText == null || plainText.isEmpty()) {
            return plainText;
        }

        try {
            Cipher cipher = Cipher.getInstance(algorithm);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);
            
            byte[] encryptedBytes = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
            String encryptedText = Base64.getEncoder().encodeToString(encryptedBytes);
            
            log.debug("配置值加密成功");
            return "{cipher}" + encryptedText;
        } catch (Exception e) {
            log.error("配置值加密失败", e);
            throw new RuntimeException("配置值加密失败", e);
        }
    }

    /**
     * 解密配置值
     */
    public String decrypt(String encryptedText) {
        if (encryptedText == null || encryptedText.isEmpty()) {
            return encryptedText;
        }

        // 检查是否为加密格式
        if (!encryptedText.startsWith("{cipher}")) {
            return encryptedText;
        }

        try {
            String cipherText = encryptedText.substring(8); // 移除 "{cipher}" 前缀
            byte[] encryptedBytes = Base64.getDecoder().decode(cipherText);
            
            Cipher cipher = Cipher.getInstance(algorithm);
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            
            byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
            String plainText = new String(decryptedBytes, StandardCharsets.UTF_8);
            
            log.debug("配置值解密成功");
            return plainText;
        } catch (Exception e) {
            log.error("配置值解密失败", e);
            throw new RuntimeException("配置值解密失败", e);
        }
    }

    /**
     * 检查是否为加密值
     */
    public boolean isEncrypted(String value) {
        return value != null && value.startsWith("{cipher}");
    }

    /**
     * 生成新的加密密钥
     */
    public String generateNewKey() {
        try {
            KeyGenerator keyGenerator = KeyGenerator.getInstance(algorithm);
            keyGenerator.init(256, new SecureRandom());
            SecretKey newKey = keyGenerator.generateKey();
            
            return Base64.getEncoder().encodeToString(newKey.getEncoded());
        } catch (NoSuchAlgorithmException e) {
            log.error("生成加密密钥失败", e);
            throw new RuntimeException("生成加密密钥失败", e);
        }
    }

    /**
     * 验证加密密钥
     */
    public boolean validateKey(String key) {
        try {
            SecretKey testKey = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), algorithm);
            Cipher cipher = Cipher.getInstance(algorithm);
            cipher.init(Cipher.ENCRYPT_MODE, testKey);
            
            // 测试加密解密
            String testText = "test";
            byte[] encrypted = cipher.doFinal(testText.getBytes(StandardCharsets.UTF_8));
            
            cipher.init(Cipher.DECRYPT_MODE, testKey);
            byte[] decrypted = cipher.doFinal(encrypted);
            
            return testText.equals(new String(decrypted, StandardCharsets.UTF_8));
        } catch (Exception e) {
            log.warn("密钥验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 重新加密（使用新密钥）
     */
    public String reEncrypt(String encryptedText, String oldKey) {
        if (!isEncrypted(encryptedText)) {
            return encryptedText;
        }

        try {
            // 使用旧密钥解密
            SecretKey oldSecretKey = new SecretKeySpec(oldKey.getBytes(StandardCharsets.UTF_8), algorithm);
            String cipherText = encryptedText.substring(8);
            byte[] encryptedBytes = Base64.getDecoder().decode(cipherText);
            
            Cipher cipher = Cipher.getInstance(algorithm);
            cipher.init(Cipher.DECRYPT_MODE, oldSecretKey);
            byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
            String plainText = new String(decryptedBytes, StandardCharsets.UTF_8);
            
            // 使用新密钥加密
            return encrypt(plainText);
        } catch (Exception e) {
            log.error("重新加密失败", e);
            throw new RuntimeException("重新加密失败", e);
        }
    }

}
