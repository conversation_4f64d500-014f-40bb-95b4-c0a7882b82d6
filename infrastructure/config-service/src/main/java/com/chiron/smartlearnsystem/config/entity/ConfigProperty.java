package com.chiron.smartlearnsystem.config.entity;

import com.chiron.smartlearnsystem.common.database.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

/**
 * 配置属性实体
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Entity
@Table(name = "config_properties", indexes = {
    @Index(name = "idx_application", columnList = "application"),
    @Index(name = "idx_profile", columnList = "profile"),
    @Index(name = "idx_label", columnList = "label"),
    @Index(name = "idx_key", columnList = "property_key"),
    @Index(name = "idx_app_profile_label", columnList = "application, profile, label")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConfigProperty extends BaseEntity {

    /**
     * 应用名称
     */
    @Column(nullable = false, length = 100)
    private String application;

    /**
     * 环境配置
     */
    @Column(nullable = false, length = 50)
    private String profile;

    /**
     * 标签
     */
    @Column(nullable = false, length = 50)
    private String label;

    /**
     * 配置键
     */
    @Column(name = "property_key", nullable = false, length = 200)
    private String propertyKey;

    /**
     * 配置值
     */
    @Column(name = "property_value", columnDefinition = "TEXT")
    private String propertyValue;

    /**
     * 配置类型
     */
    @Column(name = "property_type", length = 20)
    private String propertyType = "STRING";

    /**
     * 配置描述
     */
    @Column(columnDefinition = "TEXT")
    private String description;

    /**
     * 是否加密
     */
    @Column(name = "is_encrypted", nullable = false)
    private Boolean isEncrypted = false;

    /**
     * 是否敏感
     */
    @Column(name = "is_sensitive", nullable = false)
    private Boolean isSensitive = false;

    /**
     * 是否启用
     */
    @Column(name = "is_enabled", nullable = false)
    private Boolean isEnabled = true;

    /**
     * 配置分组
     */
    @Column(name = "config_group", length = 100)
    private String configGroup;

    /**
     * 配置来源
     */
    @Column(length = 50)
    private String source = "DATABASE";

    /**
     * 版本号
     */
    @Column(name = "config_version", length = 20)
    private String configVersion = "1.0";

    /**
     * 创建者
     */
    @Column(name = "created_by", length = 100)
    private String createdBy;

    /**
     * 更新者
     */
    @Column(name = "updated_by", length = 100)
    private String updatedBy;

    /**
     * 备注
     */
    @Column(columnDefinition = "TEXT")
    private String remarks;

    /**
     * 获取完整的配置键
     */
    public String getFullKey() {
        return String.format("%s-%s-%s:%s", application, profile, label, propertyKey);
    }

    /**
     * 检查是否为默认配置
     */
    public boolean isDefaultConfig() {
        return "default".equals(profile) && "master".equals(label);
    }

    /**
     * 检查是否需要加密
     */
    public boolean needsEncryption() {
        return isSensitive && !isEncrypted;
    }

}
