package com.chiron.smartlearnsystem.config.entity;

import com.chiron.smartlearnsystem.common.database.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

/**
 * 配置历史实体
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Entity
@Table(name = "config_history", indexes = {
    @Index(name = "idx_config_id", columnList = "config_id"),
    @Index(name = "idx_operation_type", columnList = "operation_type"),
    @Index(name = "idx_operator", columnList = "operator"),
    @Index(name = "idx_created_at", columnList = "created_at")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConfigHistory extends BaseEntity {

    /**
     * 配置ID
     */
    @Column(name = "config_id", nullable = false)
    private Long configId;

    /**
     * 应用名称
     */
    @Column(nullable = false, length = 100)
    private String application;

    /**
     * 环境配置
     */
    @Column(nullable = false, length = 50)
    private String profile;

    /**
     * 标签
     */
    @Column(nullable = false, length = 50)
    private String label;

    /**
     * 配置键
     */
    @Column(name = "property_key", nullable = false, length = 200)
    private String propertyKey;

    /**
     * 操作类型
     */
    @Column(name = "operation_type", nullable = false, length = 20)
    private String operationType; // CREATE, UPDATE, DELETE

    /**
     * 操作前的值
     */
    @Column(name = "old_value", columnDefinition = "TEXT")
    private String oldValue;

    /**
     * 操作后的值
     */
    @Column(name = "new_value", columnDefinition = "TEXT")
    private String newValue;

    /**
     * 操作者
     */
    @Column(nullable = false, length = 100)
    private String operator;

    /**
     * 操作原因
     */
    @Column(columnDefinition = "TEXT")
    private String reason;

    /**
     * 操作IP
     */
    @Column(name = "operation_ip", length = 50)
    private String operationIp;

    /**
     * 操作时间戳
     */
    @Column(name = "operation_timestamp", nullable = false)
    private Long operationTimestamp;

    /**
     * 版本号
     */
    @Column(name = "config_version", length = 20)
    private String configVersion;

    /**
     * 回滚标识
     */
    @Column(name = "rollback_id")
    private Long rollbackId;

    /**
     * 检查是否为创建操作
     */
    public boolean isCreateOperation() {
        return "CREATE".equals(operationType);
    }

    /**
     * 检查是否为更新操作
     */
    public boolean isUpdateOperation() {
        return "UPDATE".equals(operationType);
    }

    /**
     * 检查是否为删除操作
     */
    public boolean isDeleteOperation() {
        return "DELETE".equals(operationType);
    }

    /**
     * 检查是否为回滚操作
     */
    public boolean isRollbackOperation() {
        return rollbackId != null;
    }

}
