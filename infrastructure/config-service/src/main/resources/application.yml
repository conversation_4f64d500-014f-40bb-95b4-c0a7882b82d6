server:
  port: 8888

spring:
  application:
    name: config-service
  
  profiles:
    active: dev
  
  cloud:
    config:
      server:
        # Git配置
        git:
          uri: https://github.com/your-org/smartlearn-config-repo
          search-paths: '{application}'
          username: your-git-username
          password: your-git-password
          default-label: master
          timeout: 10
          clone-on-start: true
          force-pull: true
        
        # 数据库配置（作为备用配置源）
        jdbc:
          sql: SELECT property_key, property_value FROM config_properties WHERE application=? AND profile=? AND label=?
          order: 1
        
        # 本地文件配置
        native:
          search-locations: classpath:/config/,file:./config/
          version: 1.0
        
        # 加密配置
        encrypt:
          enabled: true
        
        # 健康检查
        health:
          repositories:
            smartlearn-config:
              label: master
              name: smartlearn-config
              profiles: dev,test,prod
  
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***********************************************************************************************************************************************************
    username: root
    password: 123456
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: ConfigServiceHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
        use_sql_comments: true
  
  redis:
    host: localhost
    port: 6379
    password: 
    database: 7
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0
  
  security:
    user:
      name: config-admin
      password: config-admin-password
      roles: ADMIN
  
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
    validate-on-migrate: true

eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
    register-with-eureka: true
    fetch-registry: true
  instance:
    prefer-ip-address: true
    lease-renewal-interval-in-seconds: 30
    lease-expiration-duration-in-seconds: 90

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,configprops,env,refresh,bus-refresh
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true

logging:
  level:
    com.chiron.smartlearnsystem.config: INFO
    org.springframework.cloud.config: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/config-service.log

# 自定义配置
smartlearn:
  config:
    # 加密配置
    encryption:
      key: smartlearn-config-encryption-key-32
      algorithm: AES
      enabled: true
    
    # Git配置
    git:
      uri: https://github.com/your-org/smartlearn-config-repo
      username: your-git-username
      password: your-git-password
      search-paths: '{application}'
      timeout: 10
      clone-on-start: true
      force-pull: true
    
    # 数据库配置
    database:
      enabled: true
      order: 1
    
    # 缓存配置
    cache:
      enabled: true
      expire-minutes: 30
      max-size: 1000
    
    # 安全配置
    security:
      enabled: true
      admin-username: config-admin
      admin-password: config-admin-password
    
    # 监控配置
    monitoring:
      enabled: true
      metrics-enabled: true
      health-check-enabled: true
    
    # 备份配置
    backup:
      enabled: true
      schedule: "0 0 2 * * ?"  # 每天凌晨2点备份
      retention-days: 30
      backup-path: "/data/config-backup"
