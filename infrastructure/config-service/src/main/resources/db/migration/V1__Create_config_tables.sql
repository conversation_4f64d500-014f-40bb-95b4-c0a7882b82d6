-- 配置属性表
CREATE TABLE config_properties (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    application VARCHAR(100) NOT NULL COMMENT '应用名称',
    profile VARCHAR(50) NOT NULL COMMENT '环境配置',
    label VARCHAR(50) NOT NULL COMMENT '标签',
    property_key VARCHAR(200) NOT NULL COMMENT '配置键',
    property_value TEXT COMMENT '配置值',
    property_type VARCHAR(20) DEFAULT 'STRING' COMMENT '配置类型',
    description TEXT COMMENT '配置描述',
    is_encrypted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否加密',
    is_sensitive BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否敏感',
    is_enabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    config_group VARCHAR(100) COMMENT '配置分组',
    source VARCHAR(50) DEFAULT 'DATABASE' COMMENT '配置来源',
    config_version VARCHAR(20) DEFAULT '1.0' COMMENT '版本号',
    created_by VA<PERSON>HA<PERSON>(100) COMMENT '创建者',
    updated_by VARCHAR(100) COMMENT '更新者',
    remarks TEXT COMMENT '备注',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT NOT NULL DEFAULT 0,
    version BIGINT NOT NULL DEFAULT 0,
    
    UNIQUE KEY uk_app_profile_label_key (application, profile, label, property_key),
    INDEX idx_application (application),
    INDEX idx_profile (profile),
    INDEX idx_label (label),
    INDEX idx_key (property_key),
    INDEX idx_app_profile_label (application, profile, label),
    INDEX idx_config_group (config_group),
    INDEX idx_enabled (is_enabled),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='配置属性表';

-- 配置历史表
CREATE TABLE config_history (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    config_id BIGINT NOT NULL COMMENT '配置ID',
    application VARCHAR(100) NOT NULL COMMENT '应用名称',
    profile VARCHAR(50) NOT NULL COMMENT '环境配置',
    label VARCHAR(50) NOT NULL COMMENT '标签',
    property_key VARCHAR(200) NOT NULL COMMENT '配置键',
    operation_type VARCHAR(20) NOT NULL COMMENT '操作类型',
    old_value TEXT COMMENT '操作前的值',
    new_value TEXT COMMENT '操作后的值',
    operator VARCHAR(100) NOT NULL COMMENT '操作者',
    reason TEXT COMMENT '操作原因',
    operation_ip VARCHAR(50) COMMENT '操作IP',
    operation_timestamp BIGINT NOT NULL COMMENT '操作时间戳',
    config_version VARCHAR(20) COMMENT '版本号',
    rollback_id BIGINT COMMENT '回滚标识',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT NOT NULL DEFAULT 0,
    version BIGINT NOT NULL DEFAULT 0,
    
    INDEX idx_config_id (config_id),
    INDEX idx_operation_type (operation_type),
    INDEX idx_operator (operator),
    INDEX idx_created_at (created_at),
    INDEX idx_operation_timestamp (operation_timestamp),
    FOREIGN KEY (config_id) REFERENCES config_properties(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='配置历史表';

-- 配置环境表
CREATE TABLE config_environments (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    env_name VARCHAR(50) NOT NULL UNIQUE COMMENT '环境名称',
    env_description VARCHAR(200) COMMENT '环境描述',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    sort_order INT NOT NULL DEFAULT 0 COMMENT '排序',
    created_by VARCHAR(100) COMMENT '创建者',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT NOT NULL DEFAULT 0,
    version BIGINT NOT NULL DEFAULT 0,
    
    INDEX idx_env_name (env_name),
    INDEX idx_is_active (is_active),
    INDEX idx_sort_order (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='配置环境表';

-- 配置应用表
CREATE TABLE config_applications (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    app_name VARCHAR(100) NOT NULL UNIQUE COMMENT '应用名称',
    app_description VARCHAR(200) COMMENT '应用描述',
    app_owner VARCHAR(100) COMMENT '应用负责人',
    app_contact VARCHAR(200) COMMENT '联系方式',
    git_repo_url VARCHAR(500) COMMENT 'Git仓库地址',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    sort_order INT NOT NULL DEFAULT 0 COMMENT '排序',
    created_by VARCHAR(100) COMMENT '创建者',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT NOT NULL DEFAULT 0,
    version BIGINT NOT NULL DEFAULT 0,
    
    INDEX idx_app_name (app_name),
    INDEX idx_is_active (is_active),
    INDEX idx_sort_order (sort_order),
    INDEX idx_app_owner (app_owner)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='配置应用表';

-- 配置模板表
CREATE TABLE config_templates (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    template_name VARCHAR(100) NOT NULL COMMENT '模板名称',
    template_description VARCHAR(200) COMMENT '模板描述',
    template_content TEXT NOT NULL COMMENT '模板内容',
    template_type VARCHAR(20) NOT NULL COMMENT '模板类型',
    template_category VARCHAR(50) COMMENT '模板分类',
    is_system BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否系统模板',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否激活',
    usage_count BIGINT NOT NULL DEFAULT 0 COMMENT '使用次数',
    created_by VARCHAR(100) COMMENT '创建者',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT NOT NULL DEFAULT 0,
    version BIGINT NOT NULL DEFAULT 0,
    
    INDEX idx_template_name (template_name),
    INDEX idx_template_type (template_type),
    INDEX idx_template_category (template_category),
    INDEX idx_is_active (is_active),
    INDEX idx_usage_count (usage_count)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='配置模板表';

-- 配置发布记录表
CREATE TABLE config_releases (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    release_name VARCHAR(100) NOT NULL COMMENT '发布名称',
    application VARCHAR(100) NOT NULL COMMENT '应用名称',
    profile VARCHAR(50) NOT NULL COMMENT '环境配置',
    label VARCHAR(50) NOT NULL COMMENT '标签',
    release_version VARCHAR(20) NOT NULL COMMENT '发布版本',
    release_description TEXT COMMENT '发布描述',
    config_count INT NOT NULL DEFAULT 0 COMMENT '配置数量',
    release_status VARCHAR(20) NOT NULL DEFAULT 'PENDING' COMMENT '发布状态',
    release_time DATETIME COMMENT '发布时间',
    rollback_time DATETIME COMMENT '回滚时间',
    released_by VARCHAR(100) COMMENT '发布者',
    rollback_by VARCHAR(100) COMMENT '回滚者',
    release_notes TEXT COMMENT '发布说明',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT NOT NULL DEFAULT 0,
    version BIGINT NOT NULL DEFAULT 0,
    
    INDEX idx_application (application),
    INDEX idx_profile (profile),
    INDEX idx_release_version (release_version),
    INDEX idx_release_status (release_status),
    INDEX idx_release_time (release_time),
    INDEX idx_released_by (released_by)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='配置发布记录表';

-- 初始化默认数据
INSERT INTO config_environments (env_name, env_description, is_active, sort_order, created_by) VALUES
('dev', '开发环境', TRUE, 1, 'system'),
('test', '测试环境', TRUE, 2, 'system'),
('staging', '预发布环境', TRUE, 3, 'system'),
('prod', '生产环境', TRUE, 4, 'system');

INSERT INTO config_applications (app_name, app_description, is_active, sort_order, created_by) VALUES
('user-service', '用户服务', TRUE, 1, 'system'),
('question-service', '题库服务', TRUE, 2, 'system'),
('learning-service', '学习服务', TRUE, 3, 'system'),
('ai-service', 'AI服务', TRUE, 4, 'system'),
('analytics-service', '分析服务', TRUE, 5, 'system'),
('notification-service', '通知服务', TRUE, 6, 'system'),
('gateway-service', '网关服务', TRUE, 7, 'system'),
('config-service', '配置服务', TRUE, 8, 'system');
