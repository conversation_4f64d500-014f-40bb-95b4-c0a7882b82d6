package com.chiron.smartlearnsystem.monitoring.controller;

import com.chiron.smartlearnsystem.common.core.domain.ApiResponse;
import com.chiron.smartlearnsystem.monitoring.service.MonitoringService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 监控控制器
 * 
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/monitoring")
@RequiredArgsConstructor
@Tag(name = "监控管理", description = "系统监控相关接口")
public class MonitoringController {

    private final MonitoringService monitoringService;

    /**
     * 获取系统健康状态
     */
    @GetMapping("/health")
    @Operation(summary = "获取系统健康状态", description = "获取所有服务的健康状态信息")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getSystemHealth() {
        log.info("获取系统健康状态");
        Map<String, Object> healthStatus = monitoringService.getSystemHealth();
        return ResponseEntity.ok(ApiResponse.success("获取系统健康状态成功", healthStatus));
    }

    /**
     * 获取系统指标
     */
    @GetMapping("/metrics")
    @Operation(summary = "获取系统指标", description = "获取系统性能指标信息")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getSystemMetrics() {
        log.info("获取系统指标");
        Map<String, Object> metrics = monitoringService.getSystemMetrics();
        return ResponseEntity.ok(ApiResponse.success("获取系统指标成功", metrics));
    }

    /**
     * 获取服务列表
     */
    @GetMapping("/services")
    @Operation(summary = "获取服务列表", description = "获取所有注册的服务列表")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getServices() {
        log.info("获取服务列表");
        Map<String, Object> services = monitoringService.getRegisteredServices();
        return ResponseEntity.ok(ApiResponse.success("获取服务列表成功", services));
    }

    /**
     * 获取特定服务的详细信息
     */
    @GetMapping("/services/{serviceName}")
    @Operation(summary = "获取服务详情", description = "获取特定服务的详细信息")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getServiceDetails(
            @PathVariable String serviceName) {
        log.info("获取服务详情: {}", serviceName);
        Map<String, Object> serviceDetails = monitoringService.getServiceDetails(serviceName);
        return ResponseEntity.ok(ApiResponse.success("获取服务详情成功", serviceDetails));
    }

    /**
     * 获取系统告警信息
     */
    @GetMapping("/alerts")
    @Operation(summary = "获取系统告警", description = "获取系统告警信息")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getAlerts() {
        log.info("获取系统告警信息");
        Map<String, Object> alerts = monitoringService.getSystemAlerts();
        return ResponseEntity.ok(ApiResponse.success("获取系统告警成功", alerts));
    }

    /**
     * 获取性能统计
     */
    @GetMapping("/performance")
    @Operation(summary = "获取性能统计", description = "获取系统性能统计信息")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getPerformanceStats() {
        log.info("获取性能统计");
        Map<String, Object> performance = monitoringService.getPerformanceStats();
        return ResponseEntity.ok(ApiResponse.success("获取性能统计成功", performance));
    }

    /**
     * 获取日志统计
     */
    @GetMapping("/logs/stats")
    @Operation(summary = "获取日志统计", description = "获取系统日志统计信息")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getLogStats() {
        log.info("获取日志统计");
        Map<String, Object> logStats = monitoringService.getLogStats();
        return ResponseEntity.ok(ApiResponse.success("获取日志统计成功", logStats));
    }

    /**
     * 触发健康检查
     */
    @PostMapping("/health/check")
    @Operation(summary = "触发健康检查", description = "手动触发系统健康检查")
    public ResponseEntity<ApiResponse<Void>> triggerHealthCheck() {
        log.info("触发健康检查");
        monitoringService.triggerHealthCheck();
        return ResponseEntity.ok(ApiResponse.<Void>success("健康检查已触发", null));
    }

    /**
     * 重置指标
     */
    @PostMapping("/metrics/reset")
    @Operation(summary = "重置指标", description = "重置系统指标统计")
    public ResponseEntity<ApiResponse<Void>> resetMetrics() {
        log.info("重置指标");
        monitoringService.resetMetrics();
        return ResponseEntity.ok(ApiResponse.<Void>success("指标已重置", null));
    }
}
