server:
  port: 9090

spring:
  application:
    name: monitoring-service
  profiles:
    active: dev
  
  # 数据库配置
  datasource:
    url: **********************************************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: MonitoringHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
  
  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      password: 
      database: 2
      timeout: 3000ms
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0
  
  # Spring Boot Admin配置
  boot:
    admin:
      ui:
        title: "SmartLearn System Monitoring"
        brand: "SmartLearn"
      server:
        enabled: true

# Actuator配置
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always
    metrics:
      enabled: true
    prometheus:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
    distribution:
      percentiles-histogram:
        http.server.requests: true
      percentiles:
        http.server.requests: 0.5, 0.95, 0.99
      slo:
        http.server.requests: 10ms, 50ms, 100ms, 200ms, 500ms

# 日志配置
logging:
  level:
    com.chiron.smartlearnsystem: DEBUG
    org.springframework.security: DEBUG
    de.codecentric.boot.admin: DEBUG
  pattern:
    console: "%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"

# Eureka配置
eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
    register-with-eureka: true
    fetch-registry: true
  instance:
    prefer-ip-address: true
    lease-renewal-interval-in-seconds: 30
    lease-expiration-duration-in-seconds: 90

# Nacos配置
nacos:
  discovery:
    server-addr: localhost:8848
    namespace: smartlearn
    group: DEFAULT_GROUP
  config:
    server-addr: localhost:8848
    namespace: smartlearn
    group: DEFAULT_GROUP
    file-extension: yml

# 监控配置
monitoring:
  health-check:
    interval: 30s
    timeout: 10s
  metrics:
    collection-interval: 15s
  alerts:
    enabled: true
    email:
      enabled: false
      smtp:
        host: smtp.gmail.com
        port: 587
        username: 
        password: 
    webhook:
      enabled: false
      url: 

# Swagger配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
