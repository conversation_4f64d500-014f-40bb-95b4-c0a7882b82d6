package com.chiron.smartlearnsystem.admin.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 告警级别枚举
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum AlertLevel {

    /**
     * 低级别
     */
    LOW("LOW", "低", "低级别告警", 1, "#28a745"),

    /**
     * 中级别
     */
    MEDIUM("MEDIUM", "中", "中级别告警", 2, "#ffc107"),

    /**
     * 高级别
     */
    HIGH("HIGH", "高", "高级别告警", 3, "#fd7e14"),

    /**
     * 严重级别
     */
    CRITICAL("CRITICAL", "严重", "严重级别告警", 4, "#dc3545");

    private final String code;
    private final String name;
    private final String description;
    private final int priority;
    private final String color;

    /**
     * 根据代码获取告警级别
     */
    public static AlertLevel getByCode(String code) {
        for (AlertLevel level : values()) {
            if (level.getCode().equals(code)) {
                return level;
            }
        }
        return MEDIUM; // 默认返回中级别
    }

    /**
     * 根据优先级获取告警级别
     */
    public static AlertLevel getByPriority(int priority) {
        for (AlertLevel level : values()) {
            if (level.getPriority() == priority) {
                return level;
            }
        }
        return MEDIUM; // 默认返回中级别
    }

    /**
     * 是否为高级别告警
     */
    public boolean isHighLevel() {
        return this == HIGH || this == CRITICAL;
    }

    /**
     * 是否为严重告警
     */
    public boolean isCritical() {
        return this == CRITICAL;
    }

    /**
     * 获取通知延迟（秒）
     */
    public int getNotificationDelay() {
        switch (this) {
            case CRITICAL:
                return 0; // 立即通知
            case HIGH:
                return 30; // 30秒延迟
            case MEDIUM:
                return 300; // 5分钟延迟
            case LOW:
                return 1800; // 30分钟延迟
            default:
                return 300;
        }
    }

    /**
     * 获取最大重试次数
     */
    public int getMaxRetries() {
        switch (this) {
            case CRITICAL:
                return 5; // 严重告警最多重试5次
            case HIGH:
                return 3; // 高级别重试3次
            case MEDIUM:
                return 2; // 中级别重试2次
            case LOW:
                return 1; // 低级别重试1次
            default:
                return 2;
        }
    }

    /**
     * 获取静默时间（分钟）
     */
    public int getSilenceMinutes() {
        switch (this) {
            case CRITICAL:
                return 15; // 严重告警15分钟静默
            case HIGH:
                return 30; // 高级别30分钟静默
            case MEDIUM:
                return 60; // 中级别60分钟静默
            case LOW:
                return 120; // 低级别120分钟静默
            default:
                return 60;
        }
    }

    /**
     * 是否需要立即处理
     */
    public boolean needsImmediateAction() {
        return this == CRITICAL || this == HIGH;
    }

    /**
     * 是否需要升级
     */
    public boolean needsEscalation(long durationMinutes) {
        switch (this) {
            case CRITICAL:
                return durationMinutes >= 5; // 严重告警5分钟后升级
            case HIGH:
                return durationMinutes >= 15; // 高级别15分钟后升级
            case MEDIUM:
                return durationMinutes >= 60; // 中级别60分钟后升级
            case LOW:
                return durationMinutes >= 240; // 低级别240分钟后升级
            default:
                return false;
        }
    }

}
