package com.chiron.smartlearnsystem.admin.entity;

import com.chiron.smartlearnsystem.admin.enums.AlertLevel;
import com.chiron.smartlearnsystem.admin.enums.AlertType;
import com.chiron.smartlearnsystem.common.database.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;

import jakarta.persistence.*;
import java.util.HashSet;
import java.util.Set;

/**
 * 告警规则实体
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Entity
@Table(name = "alert_rules", indexes = {
    @Index(name = "idx_rule_name", columnList = "rule_name"),
    @Index(name = "idx_service_name", columnList = "service_name"),
    @Index(name = "idx_alert_type", columnList = "alert_type"),
    @Index(name = "idx_alert_level", columnList = "alert_level"),
    @Index(name = "idx_enabled", columnList = "is_enabled"),
    @Index(name = "idx_created_at", columnList = "created_at")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlertRule extends BaseEntity {

    /**
     * 规则名称
     */
    @Column(name = "rule_name", nullable = false, unique = true, length = 100)
    private String ruleName;

    /**
     * 规则描述
     */
    @Column(name = "rule_description", columnDefinition = "TEXT")
    private String ruleDescription;

    /**
     * 服务名称
     */
    @Column(name = "service_name", length = 100)
    private String serviceName;

    /**
     * 告警类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "alert_type", nullable = false, length = 30)
    private AlertType alertType;

    /**
     * 告警级别
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "alert_level", nullable = false, length = 20)
    private AlertLevel alertLevel;

    /**
     * 监控指标
     */
    @Column(name = "metric_name", nullable = false, length = 100)
    private String metricName;

    /**
     * 比较操作符
     */
    @Column(name = "comparison_operator", nullable = false, length = 10)
    private String comparisonOperator; // >, <, >=, <=, ==, !=

    /**
     * 阈值
     */
    @Column(name = "threshold_value", nullable = false)
    private Double thresholdValue;

    /**
     * 持续时间（秒）
     */
    @Column(name = "duration_seconds", nullable = false)
    private Integer durationSeconds = 60;

    /**
     * 评估间隔（秒）
     */
    @Column(name = "evaluation_interval", nullable = false)
    private Integer evaluationInterval = 30;

    /**
     * 是否启用
     */
    @Column(name = "is_enabled", nullable = false)
    private Boolean isEnabled = true;

    /**
     * 告警条件表达式
     */
    @Column(name = "condition_expression", columnDefinition = "TEXT")
    private String conditionExpression;

    /**
     * 告警模板
     */
    @Column(name = "alert_template", columnDefinition = "TEXT")
    private String alertTemplate;

    /**
     * 通知渠道
     */
    @ElementCollection
    @CollectionTable(
        name = "alert_rule_channels",
        joinColumns = @JoinColumn(name = "rule_id")
    )
    @Column(name = "channel")
    private Set<String> notificationChannels = new HashSet<>();

    /**
     * 通知接收者
     */
    @ElementCollection
    @CollectionTable(
        name = "alert_rule_recipients",
        joinColumns = @JoinColumn(name = "rule_id")
    )
    @Column(name = "recipient")
    private Set<String> recipients = new HashSet<>();

    /**
     * 静默时间（分钟）
     */
    @Column(name = "silence_minutes")
    private Integer silenceMinutes = 60;

    /**
     * 最大告警次数
     */
    @Column(name = "max_alert_count")
    private Integer maxAlertCount = 10;

    /**
     * 告警标签
     */
    @Type(type = "json")
    @Column(name = "alert_labels", columnDefinition = "JSON")
    private String alertLabels;

    /**
     * 告警注解
     */
    @Type(type = "json")
    @Column(name = "alert_annotations", columnDefinition = "JSON")
    private String alertAnnotations;

    /**
     * 恢复通知
     */
    @Column(name = "send_resolved", nullable = false)
    private Boolean sendResolved = true;

    /**
     * 创建者
     */
    @Column(name = "created_by", length = 100)
    private String createdBy;

    /**
     * 更新者
     */
    @Column(name = "updated_by", length = 100)
    private String updatedBy;

    /**
     * 备注
     */
    @Column(columnDefinition = "TEXT")
    private String remarks;

    /**
     * 添加通知渠道
     */
    public void addNotificationChannel(String channel) {
        this.notificationChannels.add(channel);
    }

    /**
     * 添加接收者
     */
    public void addRecipient(String recipient) {
        this.recipients.add(recipient);
    }

    /**
     * 检查是否为高级别告警
     */
    public boolean isHighLevelAlert() {
        return alertLevel == AlertLevel.CRITICAL || alertLevel == AlertLevel.HIGH;
    }

    /**
     * 检查是否需要立即通知
     */
    public boolean needsImmediateNotification() {
        return isHighLevelAlert() && isEnabled;
    }

    /**
     * 启用规则
     */
    public void enable() {
        this.isEnabled = true;
    }

    /**
     * 禁用规则
     */
    public void disable() {
        this.isEnabled = false;
    }

}
