package com.chiron.smartlearnsystem.admin.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 告警类型枚举
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum AlertType {

    /**
     * 系统告警
     */
    SYSTEM("SYSTEM", "系统告警", "系统级别的告警"),

    /**
     * 服务告警
     */
    SERVICE("SERVICE", "服务告警", "微服务相关告警"),

    /**
     * 性能告警
     */
    PERFORMANCE("PERFORMANCE", "性能告警", "性能指标相关告警"),

    /**
     * 资源告警
     */
    RESOURCE("RESOURCE", "资源告警", "系统资源相关告警"),

    /**
     * 网络告警
     */
    NETWORK("NETWORK", "网络告警", "网络连接相关告警"),

    /**
     * 数据库告警
     */
    DATABASE("DATABASE", "数据库告警", "数据库相关告警"),

    /**
     * 缓存告警
     */
    CACHE("CACHE", "缓存告警", "缓存系统相关告警"),

    /**
     * 消息队列告警
     */
    MESSAGE_QUEUE("MESSAGE_QUEUE", "消息队列告警", "消息队列相关告警"),

    /**
     * 安全告警
     */
    SECURITY("SECURITY", "安全告警", "安全相关告警"),

    /**
     * 业务告警
     */
    BUSINESS("BUSINESS", "业务告警", "业务逻辑相关告警"),

    /**
     * 自定义告警
     */
    CUSTOM("CUSTOM", "自定义告警", "用户自定义告警");

    private final String code;
    private final String name;
    private final String description;

    /**
     * 根据代码获取告警类型
     */
    public static AlertType getByCode(String code) {
        for (AlertType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return SYSTEM; // 默认返回系统告警
    }

    /**
     * 是否为系统级告警
     */
    public boolean isSystemLevel() {
        return this == SYSTEM || this == RESOURCE || this == NETWORK;
    }

    /**
     * 是否为服务级告警
     */
    public boolean isServiceLevel() {
        return this == SERVICE || this == PERFORMANCE || this == DATABASE || this == CACHE;
    }

    /**
     * 是否为安全告警
     */
    public boolean isSecurityAlert() {
        return this == SECURITY;
    }

    /**
     * 是否为业务告警
     */
    public boolean isBusinessAlert() {
        return this == BUSINESS;
    }

    /**
     * 获取默认告警级别
     */
    public AlertLevel getDefaultLevel() {
        switch (this) {
            case SECURITY:
                return AlertLevel.CRITICAL;
            case SYSTEM:
            case DATABASE:
                return AlertLevel.HIGH;
            case SERVICE:
            case PERFORMANCE:
            case RESOURCE:
                return AlertLevel.MEDIUM;
            case NETWORK:
            case CACHE:
            case MESSAGE_QUEUE:
                return AlertLevel.LOW;
            case BUSINESS:
            case CUSTOM:
                return AlertLevel.MEDIUM;
            default:
                return AlertLevel.MEDIUM;
        }
    }

    /**
     * 获取默认通知渠道
     */
    public String[] getDefaultChannels() {
        switch (this) {
            case SECURITY:
            case SYSTEM:
                return new String[]{"EMAIL", "SMS", "WEBHOOK"};
            case DATABASE:
            case SERVICE:
                return new String[]{"EMAIL", "WEBHOOK"};
            case PERFORMANCE:
            case RESOURCE:
            case NETWORK:
                return new String[]{"EMAIL"};
            case CACHE:
            case MESSAGE_QUEUE:
            case BUSINESS:
            case CUSTOM:
                return new String[]{"EMAIL"};
            default:
                return new String[]{"EMAIL"};
        }
    }

}
