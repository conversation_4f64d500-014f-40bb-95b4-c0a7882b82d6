package com.chiron.smartlearnsystem.admin.service;

import com.chiron.smartlearnsystem.admin.entity.AlertRecord;
import com.chiron.smartlearnsystem.admin.entity.AlertRule;
import com.chiron.smartlearnsystem.admin.enums.AlertLevel;
import com.chiron.smartlearnsystem.admin.enums.AlertStatus;
import com.chiron.smartlearnsystem.admin.enums.AlertType;
import com.chiron.smartlearnsystem.admin.repository.AlertRecordRepository;
import com.chiron.smartlearnsystem.admin.repository.AlertRuleRepository;
import com.chiron.smartlearnsystem.common.core.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 告警服务
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class AlertService {

    private final AlertRuleRepository alertRuleRepository;
    private final AlertRecordRepository alertRecordRepository;
    private final AlertNotificationService notificationService;
    private final MetricsCollectionService metricsService;
    private final RedisTemplate<String, Object> redisTemplate;

    private static final String ALERT_CACHE_KEY = "alert:";
    private static final String SILENCE_CACHE_KEY = "alert:silence:";
    private static final int CACHE_EXPIRE_MINUTES = 30;

    /**
     * 创建告警规则
     */
    public AlertRule createAlertRule(AlertRule alertRule) {
        log.info("创建告警规则: {}", alertRule.getRuleName());

        // 检查规则名称是否已存在
        if (alertRuleRepository.existsByRuleName(alertRule.getRuleName())) {
            throw new BusinessException("告警规则名称已存在: " + alertRule.getRuleName());
        }

        // 验证规则配置
        validateAlertRule(alertRule);

        AlertRule savedRule = alertRuleRepository.save(alertRule);
        
        // 清除缓存
        clearAlertCache();
        
        log.info("告警规则创建成功: id={}", savedRule.getId());
        return savedRule;
    }

    /**
     * 更新告警规则
     */
    public AlertRule updateAlertRule(AlertRule alertRule) {
        log.info("更新告警规则: id={}", alertRule.getId());

        AlertRule existingRule = alertRuleRepository.findById(alertRule.getId())
                .orElseThrow(() -> new BusinessException("告警规则不存在"));

        // 验证规则配置
        validateAlertRule(alertRule);

        AlertRule savedRule = alertRuleRepository.save(alertRule);
        
        // 清除缓存
        clearAlertCache();
        
        log.info("告警规则更新成功: id={}", savedRule.getId());
        return savedRule;
    }

    /**
     * 删除告警规则
     */
    public void deleteAlertRule(Long ruleId) {
        log.info("删除告警规则: id={}", ruleId);

        AlertRule alertRule = alertRuleRepository.findById(ruleId)
                .orElseThrow(() -> new BusinessException("告警规则不存在"));

        // 软删除
        alertRule.setDeleted(true);
        alertRuleRepository.save(alertRule);
        
        // 清除缓存
        clearAlertCache();
        
        log.info("告警规则删除成功: id={}", ruleId);
    }

    /**
     * 启用/禁用告警规则
     */
    public void toggleAlertRule(Long ruleId, boolean enabled) {
        log.info("切换告警规则状态: id={}, enabled={}", ruleId, enabled);

        AlertRule alertRule = alertRuleRepository.findById(ruleId)
                .orElseThrow(() -> new BusinessException("告警规则不存在"));

        if (enabled) {
            alertRule.enable();
        } else {
            alertRule.disable();
        }
        
        alertRuleRepository.save(alertRule);
        
        // 清除缓存
        clearAlertCache();
        
        log.info("告警规则状态切换成功: id={}, enabled={}", ruleId, enabled);
    }

    /**
     * 触发告警
     */
    @Async
    public void triggerAlert(String ruleName, String serviceName, String metricName, 
                           Double currentValue, Double thresholdValue, String alertMessage) {
        log.warn("触发告警: rule={}, service={}, metric={}, current={}, threshold={}", 
                ruleName, serviceName, metricName, currentValue, thresholdValue);

        try {
            // 查找告警规则
            AlertRule rule = alertRuleRepository.findByRuleNameAndIsEnabledTrue(ruleName)
                    .orElse(null);
            
            if (rule == null) {
                log.warn("告警规则不存在或已禁用: {}", ruleName);
                return;
            }

            // 检查是否在静默期
            if (isInSilencePeriod(rule.getId())) {
                log.debug("告警在静默期内，跳过: {}", ruleName);
                return;
            }

            // 创建告警记录
            AlertRecord alertRecord = createAlertRecord(rule, serviceName, metricName, 
                                                      currentValue, thresholdValue, alertMessage);

            // 发送告警通知
            notificationService.sendAlertNotification(alertRecord);

            // 设置静默期
            setSilencePeriod(rule.getId(), rule.getSilenceMinutes());

            log.info("告警处理完成: recordId={}", alertRecord.getId());

        } catch (Exception e) {
            log.error("告警处理失败: rule={}", ruleName, e);
        }
    }

    /**
     * 确认告警
     */
    public void acknowledgeAlert(Long alertId, String acknowledgedBy, String note) {
        log.info("确认告警: id={}, by={}", alertId, acknowledgedBy);

        AlertRecord alertRecord = alertRecordRepository.findById(alertId)
                .orElseThrow(() -> new BusinessException("告警记录不存在"));

        if (!alertRecord.getAlertStatus().canAcknowledge()) {
            throw new BusinessException("告警状态不允许确认");
        }

        alertRecord.acknowledge(acknowledgedBy, note);
        alertRecordRepository.save(alertRecord);

        log.info("告警确认成功: id={}", alertId);
    }

    /**
     * 处理告警
     */
    public void handleAlert(Long alertId, String handledBy, String note) {
        log.info("处理告警: id={}, by={}", alertId, handledBy);

        AlertRecord alertRecord = alertRecordRepository.findById(alertId)
                .orElseThrow(() -> new BusinessException("告警记录不存在"));

        if (!alertRecord.getAlertStatus().canHandle()) {
            throw new BusinessException("告警状态不允许处理");
        }

        alertRecord.handle(handledBy, note);
        alertRecordRepository.save(alertRecord);

        log.info("告警处理成功: id={}", alertId);
    }

    /**
     * 解决告警
     */
    public void resolveAlert(Long alertId) {
        log.info("解决告警: id={}", alertId);

        AlertRecord alertRecord = alertRecordRepository.findById(alertId)
                .orElseThrow(() -> new BusinessException("告警记录不存在"));

        if (!alertRecord.getAlertStatus().canResolve()) {
            throw new BusinessException("告警状态不允许解决");
        }

        alertRecord.resolve();
        alertRecordRepository.save(alertRecord);

        // 发送恢复通知
        notificationService.sendRecoveryNotification(alertRecord);

        log.info("告警解决成功: id={}", alertId);
    }

    /**
     * 获取活跃告警
     */
    @Transactional(readOnly = true)
    public List<AlertRecord> getActiveAlerts() {
        return alertRecordRepository.findByAlertStatusIn(
                List.of(AlertStatus.FIRING, AlertStatus.ACKNOWLEDGED, AlertStatus.HANDLING));
    }

    /**
     * 获取告警记录
     */
    @Transactional(readOnly = true)
    public Page<AlertRecord> getAlertRecords(int page, int size, AlertLevel level, 
                                           AlertType type, AlertStatus status) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "alertTime"));
        
        if (level != null && type != null && status != null) {
            return alertRecordRepository.findByAlertLevelAndAlertTypeAndAlertStatus(
                    level, type, status, pageable);
        } else if (level != null && type != null) {
            return alertRecordRepository.findByAlertLevelAndAlertType(level, type, pageable);
        } else if (level != null) {
            return alertRecordRepository.findByAlertLevel(level, pageable);
        } else if (type != null) {
            return alertRecordRepository.findByAlertType(type, pageable);
        } else if (status != null) {
            return alertRecordRepository.findByAlertStatus(status, pageable);
        } else {
            return alertRecordRepository.findAll(pageable);
        }
    }

    /**
     * 获取告警统计
     */
    @Transactional(readOnly = true)
    public java.util.Map<String, Object> getAlertStatistics() {
        java.util.Map<String, Object> statistics = new java.util.HashMap<>();

        // 按级别统计
        List<Object[]> levelStats = alertRecordRepository.countAlertsByLevel();
        java.util.Map<String, Long> levelCounts = new java.util.HashMap<>();
        levelStats.forEach(stat -> levelCounts.put((String) stat[0], (Long) stat[1]));
        statistics.put("levelCounts", levelCounts);

        // 按类型统计
        List<Object[]> typeStats = alertRecordRepository.countAlertsByType();
        java.util.Map<String, Long> typeCounts = new java.util.HashMap<>();
        typeStats.forEach(stat -> typeCounts.put((String) stat[0], (Long) stat[1]));
        statistics.put("typeCounts", typeCounts);

        // 按状态统计
        List<Object[]> statusStats = alertRecordRepository.countAlertsByStatus();
        java.util.Map<String, Long> statusCounts = new java.util.HashMap<>();
        statusStats.forEach(stat -> statusCounts.put((String) stat[0], (Long) stat[1]));
        statistics.put("statusCounts", statusCounts);

        // 活跃告警数量
        Long activeCount = alertRecordRepository.countActiveAlerts();
        statistics.put("activeCount", activeCount);

        // 今日告警数量
        LocalDateTime todayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
        Long todayCount = alertRecordRepository.countAlertsSince(todayStart);
        statistics.put("todayCount", todayCount);

        return statistics;
    }

    /**
     * 创建告警记录
     */
    private AlertRecord createAlertRecord(AlertRule rule, String serviceName, String metricName,
                                        Double currentValue, Double thresholdValue, String alertMessage) {
        AlertRecord alertRecord = AlertRecord.builder()
                .ruleId(rule.getId())
                .ruleName(rule.getRuleName())
                .serviceName(serviceName)
                .alertType(rule.getAlertType())
                .alertLevel(rule.getAlertLevel())
                .alertStatus(AlertStatus.FIRING)
                .alertTime(LocalDateTime.now())
                .alertTitle(generateAlertTitle(rule, serviceName, metricName))
                .alertMessage(alertMessage)
                .metricName(metricName)
                .currentValue(currentValue)
                .thresholdValue(thresholdValue)
                .build();

        return alertRecordRepository.save(alertRecord);
    }

    /**
     * 生成告警标题
     */
    private String generateAlertTitle(AlertRule rule, String serviceName, String metricName) {
        return String.format("[%s] %s - %s 告警", 
                rule.getAlertLevel().getName(), serviceName, metricName);
    }

    /**
     * 验证告警规则
     */
    private void validateAlertRule(AlertRule alertRule) {
        if (alertRule.getThresholdValue() == null) {
            throw new BusinessException("阈值不能为空");
        }
        
        if (alertRule.getDurationSeconds() == null || alertRule.getDurationSeconds() <= 0) {
            throw new BusinessException("持续时间必须大于0");
        }
        
        if (alertRule.getEvaluationInterval() == null || alertRule.getEvaluationInterval() <= 0) {
            throw new BusinessException("评估间隔必须大于0");
        }
    }

    /**
     * 检查是否在静默期
     */
    private boolean isInSilencePeriod(Long ruleId) {
        String silenceKey = SILENCE_CACHE_KEY + ruleId;
        return redisTemplate.hasKey(silenceKey);
    }

    /**
     * 设置静默期
     */
    private void setSilencePeriod(Long ruleId, Integer silenceMinutes) {
        if (silenceMinutes != null && silenceMinutes > 0) {
            String silenceKey = SILENCE_CACHE_KEY + ruleId;
            redisTemplate.opsForValue().set(silenceKey, "1", silenceMinutes, TimeUnit.MINUTES);
        }
    }

    /**
     * 清除告警缓存
     */
    private void clearAlertCache() {
        String pattern = ALERT_CACHE_KEY + "*";
        redisTemplate.delete(redisTemplate.keys(pattern));
    }

}
