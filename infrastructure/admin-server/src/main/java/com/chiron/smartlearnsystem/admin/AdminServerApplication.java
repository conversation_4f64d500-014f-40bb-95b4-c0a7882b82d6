package com.chiron.smartlearnsystem.admin;

import de.codecentric.boot.admin.server.config.EnableAdminServer;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 监控中心服务启动类
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@SpringBootApplication(scanBasePackages = {
    "com.chiron.smartlearnsystem.admin",
    "com.chiron.smartlearnsystem.common"
})
@EnableAdminServer
@EnableEurekaClient
@EnableScheduling
public class AdminServerApplication {

    public static void main(String[] args) {
        SpringApplication.run(AdminServerApplication.class, args);
    }

}
