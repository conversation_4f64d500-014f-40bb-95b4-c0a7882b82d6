package com.chiron.smartlearnsystem.admin.entity;

import com.chiron.smartlearnsystem.admin.enums.AlertLevel;
import com.chiron.smartlearnsystem.admin.enums.AlertStatus;
import com.chiron.smartlearnsystem.admin.enums.AlertType;
import com.chiron.smartlearnsystem.common.database.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 告警记录实体
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Entity
@Table(name = "alert_records", indexes = {
    @Index(name = "idx_rule_id", columnList = "rule_id"),
    @Index(name = "idx_service_name", columnList = "service_name"),
    @Index(name = "idx_alert_type", columnList = "alert_type"),
    @Index(name = "idx_alert_level", columnList = "alert_level"),
    @Index(name = "idx_alert_status", columnList = "alert_status"),
    @Index(name = "idx_alert_time", columnList = "alert_time"),
    @Index(name = "idx_created_at", columnList = "created_at")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlertRecord extends BaseEntity {

    /**
     * 告警规则ID
     */
    @Column(name = "rule_id", nullable = false)
    private Long ruleId;

    /**
     * 规则名称
     */
    @Column(name = "rule_name", nullable = false, length = 100)
    private String ruleName;

    /**
     * 服务名称
     */
    @Column(name = "service_name", length = 100)
    private String serviceName;

    /**
     * 告警类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "alert_type", nullable = false, length = 30)
    private AlertType alertType;

    /**
     * 告警级别
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "alert_level", nullable = false, length = 20)
    private AlertLevel alertLevel;

    /**
     * 告警状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "alert_status", nullable = false, length = 20)
    private AlertStatus alertStatus = AlertStatus.FIRING;

    /**
     * 告警时间
     */
    @Column(name = "alert_time", nullable = false)
    private LocalDateTime alertTime;

    /**
     * 恢复时间
     */
    @Column(name = "resolved_time")
    private LocalDateTime resolvedTime;

    /**
     * 告警标题
     */
    @Column(name = "alert_title", nullable = false, length = 200)
    private String alertTitle;

    /**
     * 告警消息
     */
    @Column(name = "alert_message", nullable = false, columnDefinition = "TEXT")
    private String alertMessage;

    /**
     * 监控指标
     */
    @Column(name = "metric_name", nullable = false, length = 100)
    private String metricName;

    /**
     * 当前值
     */
    @Column(name = "current_value")
    private Double currentValue;

    /**
     * 阈值
     */
    @Column(name = "threshold_value")
    private Double thresholdValue;

    /**
     * 告警详情
     */
    @Type(type = "json")
    @Column(name = "alert_details", columnDefinition = "JSON")
    private String alertDetails;

    /**
     * 告警标签
     */
    @Type(type = "json")
    @Column(name = "alert_labels", columnDefinition = "JSON")
    private String alertLabels;

    /**
     * 告警注解
     */
    @Type(type = "json")
    @Column(name = "alert_annotations", columnDefinition = "JSON")
    private String alertAnnotations;

    /**
     * 通知状态
     */
    @Column(name = "notification_status", length = 20)
    private String notificationStatus = "PENDING";

    /**
     * 通知时间
     */
    @Column(name = "notification_time")
    private LocalDateTime notificationTime;

    /**
     * 通知次数
     */
    @Column(name = "notification_count", nullable = false)
    private Integer notificationCount = 0;

    /**
     * 确认者
     */
    @Column(name = "acknowledged_by", length = 100)
    private String acknowledgedBy;

    /**
     * 确认时间
     */
    @Column(name = "acknowledged_time")
    private LocalDateTime acknowledgedTime;

    /**
     * 确认备注
     */
    @Column(name = "acknowledgment_note", columnDefinition = "TEXT")
    private String acknowledgmentNote;

    /**
     * 处理者
     */
    @Column(name = "handled_by", length = 100)
    private String handledBy;

    /**
     * 处理时间
     */
    @Column(name = "handled_time")
    private LocalDateTime handledTime;

    /**
     * 处理备注
     */
    @Column(name = "handling_note", columnDefinition = "TEXT")
    private String handlingNote;

    /**
     * 持续时间（秒）
     */
    @Column(name = "duration_seconds")
    private Long durationSeconds;

    /**
     * 影响范围
     */
    @Column(name = "impact_scope", length = 200)
    private String impactScope;

    /**
     * 根本原因
     */
    @Column(name = "root_cause", columnDefinition = "TEXT")
    private String rootCause;

    /**
     * 解决方案
     */
    @Column(name = "solution", columnDefinition = "TEXT")
    private String solution;

    /**
     * 标记为已确认
     */
    public void acknowledge(String acknowledgedBy, String note) {
        this.acknowledgedBy = acknowledgedBy;
        this.acknowledgedTime = LocalDateTime.now();
        this.acknowledgmentNote = note;
        this.alertStatus = AlertStatus.ACKNOWLEDGED;
    }

    /**
     * 标记为已处理
     */
    public void handle(String handledBy, String note) {
        this.handledBy = handledBy;
        this.handledTime = LocalDateTime.now();
        this.handlingNote = note;
        this.alertStatus = AlertStatus.HANDLED;
    }

    /**
     * 标记为已解决
     */
    public void resolve() {
        this.resolvedTime = LocalDateTime.now();
        this.alertStatus = AlertStatus.RESOLVED;
        
        if (this.alertTime != null) {
            this.durationSeconds = java.time.Duration.between(this.alertTime, this.resolvedTime).getSeconds();
        }
    }

    /**
     * 增加通知次数
     */
    public void incrementNotificationCount() {
        this.notificationCount++;
        this.notificationTime = LocalDateTime.now();
        this.notificationStatus = "SENT";
    }

    /**
     * 检查是否为活跃告警
     */
    public boolean isActive() {
        return alertStatus == AlertStatus.FIRING || alertStatus == AlertStatus.ACKNOWLEDGED;
    }

    /**
     * 检查是否已解决
     */
    public boolean isResolved() {
        return alertStatus == AlertStatus.RESOLVED;
    }

    /**
     * 检查是否需要升级
     */
    public boolean needsEscalation(int maxNotificationCount, long maxDurationMinutes) {
        if (!isActive()) {
            return false;
        }
        
        // 通知次数超限
        if (notificationCount >= maxNotificationCount) {
            return true;
        }
        
        // 持续时间超限
        if (alertTime != null) {
            long durationMinutes = java.time.Duration.between(alertTime, LocalDateTime.now()).toMinutes();
            return durationMinutes >= maxDurationMinutes;
        }
        
        return false;
    }

}
