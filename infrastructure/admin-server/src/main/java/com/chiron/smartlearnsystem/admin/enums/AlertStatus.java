package com.chiron.smartlearnsystem.admin.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 告警状态枚举
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum AlertStatus {

    /**
     * 触发中
     */
    FIRING("FIRING", "触发中", "告警正在触发"),

    /**
     * 已确认
     */
    ACKNOWLEDGED("ACKNOWLEDGED", "已确认", "告警已被确认"),

    /**
     * 处理中
     */
    HANDLING("HANDLING", "处理中", "告警正在处理"),

    /**
     * 已处理
     */
    HANDLED("HANDLED", "已处理", "告警已被处理"),

    /**
     * 已解决
     */
    RESOLVED("RESOLVED", "已解决", "告警已解决"),

    /**
     * 已忽略
     */
    IGNORED("IGNORED", "已忽略", "告警已被忽略"),

    /**
     * 已静默
     */
    SILENCED("SILENCED", "已静默", "告警已被静默");

    private final String code;
    private final String name;
    private final String description;

    /**
     * 根据代码获取告警状态
     */
    public static AlertStatus getByCode(String code) {
        for (AlertStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return FIRING; // 默认返回触发中
    }

    /**
     * 是否为活跃状态
     */
    public boolean isActive() {
        return this == FIRING || this == ACKNOWLEDGED || this == HANDLING;
    }

    /**
     * 是否为已完成状态
     */
    public boolean isCompleted() {
        return this == HANDLED || this == RESOLVED || this == IGNORED;
    }

    /**
     * 是否需要处理
     */
    public boolean needsAction() {
        return this == FIRING || this == ACKNOWLEDGED;
    }

    /**
     * 是否可以确认
     */
    public boolean canAcknowledge() {
        return this == FIRING;
    }

    /**
     * 是否可以处理
     */
    public boolean canHandle() {
        return this == FIRING || this == ACKNOWLEDGED;
    }

    /**
     * 是否可以解决
     */
    public boolean canResolve() {
        return this == FIRING || this == ACKNOWLEDGED || this == HANDLING || this == HANDLED;
    }

    /**
     * 是否可以忽略
     */
    public boolean canIgnore() {
        return this == FIRING || this == ACKNOWLEDGED;
    }

    /**
     * 是否可以静默
     */
    public boolean canSilence() {
        return this == FIRING || this == ACKNOWLEDGED;
    }

    /**
     * 获取下一个可能的状态
     */
    public AlertStatus[] getNextPossibleStatuses() {
        switch (this) {
            case FIRING:
                return new AlertStatus[]{ACKNOWLEDGED, HANDLING, RESOLVED, IGNORED, SILENCED};
            case ACKNOWLEDGED:
                return new AlertStatus[]{HANDLING, RESOLVED, IGNORED, SILENCED};
            case HANDLING:
                return new AlertStatus[]{HANDLED, RESOLVED};
            case HANDLED:
                return new AlertStatus[]{RESOLVED};
            case RESOLVED:
            case IGNORED:
            case SILENCED:
                return new AlertStatus[]{}; // 终态，无后续状态
            default:
                return new AlertStatus[]{};
        }
    }

}
