server:
  port: 8090

spring:
  application:
    name: admin-server
  
  profiles:
    active: dev
  
  boot:
    admin:
      ui:
        title: "SmartLearn 监控中心"
        brand: "SmartLearn System"
        favicon: "classpath:/static/favicon.ico"
        login-icon: "classpath:/static/login-icon.png"
        cache:
          no-cache: true
        cache-templates: false
        template-location: "classpath:/templates/"
        resource-locations: "classpath:/static/"
        external-views:
          - label: "Grafana"
            url: "http://localhost:3000"
            order: 1000
          - label: "Prometheus"
            url: "http://localhost:9090"
            order: 1001
      
      discovery:
        enabled: true
        converter:
          management-context-path: "/actuator"
          health-endpoint-path: "health"
        ignored-services:
          - "consul"
          - "eureka-server"
      
      notify:
        mail:
          enabled: true
          ignore-changes: "UNKNOWN:UP"
          template: "classpath:/templates/notify-mail.html"
          to: "<EMAIL>"
          from: "<EMAIL>"
          subject: "SmartLearn 服务状态变更"
        
        webhook:
          enabled: true
          url: "http://localhost:8085/api/v1/notifications/webhook/admin"
  
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **********************************************************************************************************************************************************
    username: root
    password: 123456
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: AdminServerHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
        use_sql_comments: true
  
  redis:
    host: localhost
    port: 6379
    password: 
    database: 8
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0
  
  security:
    user:
      name: admin
      password: admin123
      roles: ADMIN
  
  mail:
    host: smtp.gmail.com
    port: 587
    username: <EMAIL>
    password: your-email-password
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
  
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
    validate-on-migrate: true

eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
    register-with-eureka: true
    fetch-registry: true
  instance:
    prefer-ip-address: true
    lease-renewal-interval-in-seconds: 30
    lease-expiration-duration-in-seconds: 90

management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

logging:
  level:
    com.chiron.smartlearnsystem.admin: INFO
    de.codecentric.boot.admin: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/admin-server.log

# 自定义配置
smartlearn:
  admin:
    # 监控配置
    monitoring:
      enabled: true
      check-interval: 30  # 检查间隔（秒）
      timeout: 10         # 超时时间（秒）
      
      # 健康检查配置
      health-check:
        enabled: true
        endpoints:
          - "/actuator/health"
          - "/actuator/info"
        
      # 性能监控配置
      performance:
        enabled: true
        cpu-threshold: 80.0      # CPU使用率阈值
        memory-threshold: 85.0   # 内存使用率阈值
        disk-threshold: 90.0     # 磁盘使用率阈值
        response-time-threshold: 5000  # 响应时间阈值（毫秒）
    
    # 告警配置
    alert:
      enabled: true
      
      # 默认告警规则
      default-rules:
        service-down:
          enabled: true
          level: "CRITICAL"
          silence-minutes: 15
        
        high-cpu:
          enabled: true
          threshold: 80.0
          level: "HIGH"
          duration-seconds: 300
          silence-minutes: 30
        
        high-memory:
          enabled: true
          threshold: 85.0
          level: "HIGH"
          duration-seconds: 300
          silence-minutes: 30
        
        slow-response:
          enabled: true
          threshold: 5000
          level: "MEDIUM"
          duration-seconds: 180
          silence-minutes: 60
      
      # 通知配置
      notification:
        enabled: true
        channels:
          - "EMAIL"
          - "WEBHOOK"
        
        email:
          enabled: true
          recipients:
            - "<EMAIL>"
            - "<EMAIL>"
        
        webhook:
          enabled: true
          url: "http://localhost:8085/api/v1/notifications/webhook/alert"
          timeout: 5000
    
    # 数据收集配置
    metrics:
      enabled: true
      collection-interval: 30  # 收集间隔（秒）
      retention-days: 30       # 数据保留天数
      
      # 自定义指标
      custom-metrics:
        - name: "user_login_count"
          type: "COUNTER"
          description: "用户登录次数"
        
        - name: "question_answer_time"
          type: "HISTOGRAM"
          description: "答题时间分布"
        
        - name: "ai_recommendation_accuracy"
          type: "GAUGE"
          description: "AI推荐准确率"
    
    # 报告配置
    report:
      enabled: true
      schedule: "0 0 8 * * ?"  # 每天8点生成报告
      recipients:
        - "<EMAIL>"
      
      # 报告内容
      content:
        - "service_status"
        - "performance_summary"
        - "alert_summary"
        - "usage_statistics"
