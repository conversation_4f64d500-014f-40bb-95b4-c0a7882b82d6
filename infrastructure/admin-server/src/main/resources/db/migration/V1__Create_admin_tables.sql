-- 告警规则表
CREATE TABLE alert_rules (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    rule_name VARCHAR(100) NOT NULL UNIQUE COMMENT '规则名称',
    rule_description TEXT COMMENT '规则描述',
    service_name VARCHAR(100) COMMENT '服务名称',
    alert_type VARCHAR(30) NOT NULL COMMENT '告警类型',
    alert_level VARCHAR(20) NOT NULL COMMENT '告警级别',
    metric_name VARCHAR(100) NOT NULL COMMENT '监控指标',
    comparison_operator VARCHAR(10) NOT NULL COMMENT '比较操作符',
    threshold_value DOUBLE NOT NULL COMMENT '阈值',
    duration_seconds INT NOT NULL DEFAULT 60 COMMENT '持续时间（秒）',
    evaluation_interval INT NOT NULL DEFAULT 30 COMMENT '评估间隔（秒）',
    is_enabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    condition_expression TEXT COMMENT '告警条件表达式',
    alert_template TEXT COMMENT '告警模板',
    silence_minutes INT DEFAULT 60 COMMENT '静默时间（分钟）',
    max_alert_count INT DEFAULT 10 COMMENT '最大告警次数',
    alert_labels JSON COMMENT '告警标签',
    alert_annotations JSON COMMENT '告警注解',
    send_resolved BOOLEAN NOT NULL DEFAULT TRUE COMMENT '恢复通知',
    created_by VARCHAR(100) COMMENT '创建者',
    updated_by VARCHAR(100) COMMENT '更新者',
    remarks TEXT COMMENT '备注',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT NOT NULL DEFAULT 0,
    version BIGINT NOT NULL DEFAULT 0,
    
    INDEX idx_rule_name (rule_name),
    INDEX idx_service_name (service_name),
    INDEX idx_alert_type (alert_type),
    INDEX idx_alert_level (alert_level),
    INDEX idx_enabled (is_enabled),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='告警规则表';

-- 告警规则通知渠道表
CREATE TABLE alert_rule_channels (
    rule_id BIGINT NOT NULL,
    channel VARCHAR(50) NOT NULL,
    PRIMARY KEY (rule_id, channel),
    FOREIGN KEY (rule_id) REFERENCES alert_rules(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='告警规则通知渠道表';

-- 告警规则接收者表
CREATE TABLE alert_rule_recipients (
    rule_id BIGINT NOT NULL,
    recipient VARCHAR(100) NOT NULL,
    PRIMARY KEY (rule_id, recipient),
    FOREIGN KEY (rule_id) REFERENCES alert_rules(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='告警规则接收者表';

-- 告警记录表
CREATE TABLE alert_records (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    rule_id BIGINT NOT NULL COMMENT '告警规则ID',
    rule_name VARCHAR(100) NOT NULL COMMENT '规则名称',
    service_name VARCHAR(100) COMMENT '服务名称',
    alert_type VARCHAR(30) NOT NULL COMMENT '告警类型',
    alert_level VARCHAR(20) NOT NULL COMMENT '告警级别',
    alert_status VARCHAR(20) NOT NULL DEFAULT 'FIRING' COMMENT '告警状态',
    alert_time DATETIME NOT NULL COMMENT '告警时间',
    resolved_time DATETIME COMMENT '恢复时间',
    alert_title VARCHAR(200) NOT NULL COMMENT '告警标题',
    alert_message TEXT NOT NULL COMMENT '告警消息',
    metric_name VARCHAR(100) NOT NULL COMMENT '监控指标',
    current_value DOUBLE COMMENT '当前值',
    threshold_value DOUBLE COMMENT '阈值',
    alert_details JSON COMMENT '告警详情',
    alert_labels JSON COMMENT '告警标签',
    alert_annotations JSON COMMENT '告警注解',
    notification_status VARCHAR(20) DEFAULT 'PENDING' COMMENT '通知状态',
    notification_time DATETIME COMMENT '通知时间',
    notification_count INT NOT NULL DEFAULT 0 COMMENT '通知次数',
    acknowledged_by VARCHAR(100) COMMENT '确认者',
    acknowledged_time DATETIME COMMENT '确认时间',
    acknowledgment_note TEXT COMMENT '确认备注',
    handled_by VARCHAR(100) COMMENT '处理者',
    handled_time DATETIME COMMENT '处理时间',
    handling_note TEXT COMMENT '处理备注',
    duration_seconds BIGINT COMMENT '持续时间（秒）',
    impact_scope VARCHAR(200) COMMENT '影响范围',
    root_cause TEXT COMMENT '根本原因',
    solution TEXT COMMENT '解决方案',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT NOT NULL DEFAULT 0,
    version BIGINT NOT NULL DEFAULT 0,
    
    INDEX idx_rule_id (rule_id),
    INDEX idx_service_name (service_name),
    INDEX idx_alert_type (alert_type),
    INDEX idx_alert_level (alert_level),
    INDEX idx_alert_status (alert_status),
    INDEX idx_alert_time (alert_time),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (rule_id) REFERENCES alert_rules(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='告警记录表';

-- 服务监控表
CREATE TABLE service_monitors (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    service_name VARCHAR(100) NOT NULL COMMENT '服务名称',
    service_url VARCHAR(500) NOT NULL COMMENT '服务URL',
    service_status VARCHAR(20) NOT NULL DEFAULT 'UNKNOWN' COMMENT '服务状态',
    health_check_url VARCHAR(500) COMMENT '健康检查URL',
    last_check_time DATETIME COMMENT '最后检查时间',
    response_time BIGINT COMMENT '响应时间（毫秒）',
    cpu_usage DOUBLE COMMENT 'CPU使用率',
    memory_usage DOUBLE COMMENT '内存使用率',
    disk_usage DOUBLE COMMENT '磁盘使用率',
    active_connections INT COMMENT '活跃连接数',
    error_rate DOUBLE COMMENT '错误率',
    throughput DOUBLE COMMENT '吞吐量',
    availability DOUBLE COMMENT '可用性',
    version VARCHAR(50) COMMENT '服务版本',
    build_info VARCHAR(200) COMMENT '构建信息',
    environment VARCHAR(50) COMMENT '环境',
    tags JSON COMMENT '标签',
    metadata JSON COMMENT '元数据',
    is_monitored BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否监控',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT NOT NULL DEFAULT 0,
    version_num BIGINT NOT NULL DEFAULT 0,
    
    UNIQUE KEY uk_service_name (service_name),
    INDEX idx_service_status (service_status),
    INDEX idx_last_check_time (last_check_time),
    INDEX idx_is_monitored (is_monitored),
    INDEX idx_environment (environment)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='服务监控表';

-- 性能指标表
CREATE TABLE performance_metrics (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    service_name VARCHAR(100) NOT NULL COMMENT '服务名称',
    metric_name VARCHAR(100) NOT NULL COMMENT '指标名称',
    metric_value DOUBLE NOT NULL COMMENT '指标值',
    metric_type VARCHAR(20) NOT NULL COMMENT '指标类型',
    metric_unit VARCHAR(20) COMMENT '指标单位',
    metric_timestamp DATETIME NOT NULL COMMENT '指标时间戳',
    metric_labels JSON COMMENT '指标标签',
    aggregation_type VARCHAR(20) COMMENT '聚合类型',
    collection_interval INT COMMENT '收集间隔（秒）',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_service_name (service_name),
    INDEX idx_metric_name (metric_name),
    INDEX idx_metric_timestamp (metric_timestamp),
    INDEX idx_service_metric_time (service_name, metric_name, metric_timestamp)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='性能指标表';

-- 系统事件表
CREATE TABLE system_events (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    event_type VARCHAR(50) NOT NULL COMMENT '事件类型',
    event_level VARCHAR(20) NOT NULL COMMENT '事件级别',
    event_source VARCHAR(100) NOT NULL COMMENT '事件源',
    event_title VARCHAR(200) NOT NULL COMMENT '事件标题',
    event_message TEXT NOT NULL COMMENT '事件消息',
    event_data JSON COMMENT '事件数据',
    event_timestamp DATETIME NOT NULL COMMENT '事件时间戳',
    user_id BIGINT COMMENT '用户ID',
    session_id VARCHAR(100) COMMENT '会话ID',
    ip_address VARCHAR(50) COMMENT 'IP地址',
    user_agent VARCHAR(500) COMMENT '用户代理',
    request_id VARCHAR(100) COMMENT '请求ID',
    trace_id VARCHAR(100) COMMENT '追踪ID',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_event_type (event_type),
    INDEX idx_event_level (event_level),
    INDEX idx_event_source (event_source),
    INDEX idx_event_timestamp (event_timestamp),
    INDEX idx_user_id (user_id),
    INDEX idx_trace_id (trace_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统事件表';

-- 初始化默认告警规则
INSERT INTO alert_rules (rule_name, rule_description, alert_type, alert_level, metric_name, comparison_operator, threshold_value, duration_seconds, evaluation_interval, created_by) VALUES
('服务下线告警', '检测服务是否下线', 'SERVICE', 'CRITICAL', 'service_status', '==', 0, 60, 30, 'system'),
('CPU使用率过高', 'CPU使用率超过80%', 'PERFORMANCE', 'HIGH', 'cpu_usage', '>', 80.0, 300, 30, 'system'),
('内存使用率过高', '内存使用率超过85%', 'PERFORMANCE', 'HIGH', 'memory_usage', '>', 85.0, 300, 30, 'system'),
('磁盘使用率过高', '磁盘使用率超过90%', 'RESOURCE', 'HIGH', 'disk_usage', '>', 90.0, 600, 60, 'system'),
('响应时间过长', '响应时间超过5秒', 'PERFORMANCE', 'MEDIUM', 'response_time', '>', 5000, 180, 30, 'system'),
('错误率过高', '错误率超过5%', 'SERVICE', 'HIGH', 'error_rate', '>', 5.0, 300, 30, 'system'),
('数据库连接池耗尽', '数据库连接池使用率超过95%', 'DATABASE', 'CRITICAL', 'db_pool_usage', '>', 95.0, 120, 30, 'system');

-- 初始化告警规则通知渠道
INSERT INTO alert_rule_channels (rule_id, channel) VALUES
(1, 'EMAIL'), (1, 'WEBHOOK'),
(2, 'EMAIL'), (3, 'EMAIL'), (4, 'EMAIL'),
(5, 'EMAIL'), (6, 'EMAIL'), (6, 'WEBHOOK'),
(7, 'EMAIL'), (7, 'WEBHOOK');
