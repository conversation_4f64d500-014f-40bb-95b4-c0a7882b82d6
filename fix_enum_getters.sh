#!/bin/bash

# 为所有枚举类添加getter方法

# 定义需要修复的枚举类文件
ENUM_FILES=(
    "services/question-service/src/main/java/com/chiron/smartlearnsystem/question/enums/QuestionSource.java"
    "services/question-service/src/main/java/com/chiron/smartlearnsystem/question/enums/ReviewStatus.java"
    "services/question-service/src/main/java/com/chiron/smartlearnsystem/question/enums/PaperType.java"
    "services/question-service/src/main/java/com/chiron/smartlearnsystem/question/enums/PaperStatus.java"
    "services/question-service/src/main/java/com/chiron/smartlearnsystem/question/enums/ExamStatus.java"
)

for file in "${ENUM_FILES[@]}"; do
    echo "修复文件: $file"
    
    # 获取枚举类名
    enum_name=$(basename "$file" .java)
    
    # 在构造函数后添加getter方法
    sed -i '' '/'"$enum_name"'(String code, String name, String description) {/,/}/a\
\
    public String getCode() {\
        return code;\
    }\
\
    public String getName() {\
        return name;\
    }\
\
    public String getDescription() {\
        return description;\
    }
' "$file"
    
    echo "完成修复: $file"
done

echo "所有枚举类getter方法添加完成！"
