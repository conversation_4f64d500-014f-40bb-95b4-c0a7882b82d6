#!/usr/bin/env python3

import re
import os

# 需要修复的枚举类文件
enum_files = [
    "services/question-service/src/main/java/com/chiron/smartlearnsystem/question/enums/PaperType.java",
    "services/question-service/src/main/java/com/chiron/smartlearnsystem/question/enums/PaperStatus.java",
    "services/question-service/src/main/java/com/chiron/smartlearnsystem/question/enums/ExamStatus.java"
]

getter_methods = """
    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }
"""

for file_path in enum_files:
    if os.path.exists(file_path):
        print(f"修复文件: {file_path}")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 获取枚举类名
        enum_name = os.path.basename(file_path).replace('.java', '')
        
        # 查找构造函数结束位置
        pattern = rf'{enum_name}\(String code, String name, String description\) \{{\s*this\.code = code;\s*this\.name = name;\s*this\.description = description;\s*\}}'
        
        def replace_constructor(match):
            return match.group(0) + getter_methods
        
        # 替换构造函数，添加getter方法
        new_content = re.sub(pattern, replace_constructor, content, flags=re.DOTALL)
        
        # 如果内容有变化，写回文件
        if new_content != content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            print(f"完成修复: {file_path}")
        else:
            print(f"无需修复: {file_path}")
    else:
        print(f"文件不存在: {file_path}")

print("所有枚举类getter方法添加完成！")
