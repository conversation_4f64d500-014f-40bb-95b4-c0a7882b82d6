package com.chiron.smartlearnsystem.exercise.entity;

import com.chiron.smartlearnsystem.common.database.entity.BaseEntity;
import com.chiron.smartlearnsystem.exercise.enums.ExamStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 考试记录实体
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Entity
@Table(name = "exam_records", indexes = {
    @Index(name = "idx_user_paper", columnList = "user_id, paper_id"),
    @Index(name = "idx_session", columnList = "session_id"),
    @Index(name = "idx_status_time", columnList = "status, start_time"),
    @Index(name = "idx_score", columnList = "user_score"),
    @Index(name = "idx_start_time", columnList = "start_time")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExamRecord extends BaseEntity {

    /**
     * 用户ID
     */
    @Column(name = "user_id", nullable = false)
    private Long userId;

    /**
     * 试卷ID
     */
    @Column(name = "paper_id", nullable = false)
    private Long paperId;

    /**
     * 考试会话ID（唯一标识）
     */
    @Column(name = "session_id", unique = true, nullable = false, length = 50)
    private String sessionId;

    /**
     * 开始时间
     */
    @Column(name = "start_time", nullable = false)
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @Column(name = "end_time")
    private LocalDateTime endTime;

    /**
     * 提交时间
     */
    @Column(name = "submit_time")
    private LocalDateTime submitTime;

    /**
     * 试卷总分
     */
    @Column(name = "total_score")
    private Integer totalScore;

    /**
     * 用户得分
     */
    @Column(name = "user_score")
    private Integer userScore;

    /**
     * 正确题目数量
     */
    @Column(name = "correct_count", nullable = false)
    private Integer correctCount = 0;

    /**
     * 总题目数量
     */
    @Column(name = "total_questions", nullable = false)
    private Integer totalQuestions = 0;

    /**
     * 是否通过考试
     */
    @Column(name = "is_passed")
    private Boolean isPassed;

    /**
     * 用时（分钟）
     */
    @Column(name = "time_spent_minutes")
    private Integer timeSpentMinutes;

    /**
     * 考试详细数据（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "exam_data", columnDefinition = "JSON")
    private String examData;

    /**
     * 考试状态
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private ExamStatus status = ExamStatus.IN_PROGRESS;

    /**
     * 正确率
     */
    @Column(name = "accuracy_rate", precision = 5, scale = 4)
    private BigDecimal accuracyRate;

    /**
     * 排名（在同试卷中的排名）
     */
    @Column(name = "ranking")
    private Integer ranking;

    /**
     * 超越百分比
     */
    @Column(name = "beat_percentage", precision = 5, 2)
    private BigDecimal beatPercentage;

    /**
     * 考试环境信息
     */
    @Column(name = "exam_environment", columnDefinition = "TEXT")
    private String examEnvironment;

    /**
     * IP地址
     */
    @Column(name = "ip_address", length = 50)
    private String ipAddress;

    /**
     * 用户代理
     */
    @Column(name = "user_agent", columnDefinition = "TEXT")
    private String userAgent;

    /**
     * 关联的试卷
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "paper_id", insertable = false, updatable = false)
    private ExamPaper examPaper;

    /**
     * 答题记录
     */
    @OneToMany(mappedBy = "examRecord", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<AnswerRecord> answerRecords = new ArrayList<>();

    /**
     * 计算正确率
     */
    public BigDecimal calculateAccuracyRate() {
        if (totalQuestions == 0) {
            return BigDecimal.ZERO;
        }
        this.accuracyRate = BigDecimal.valueOf(correctCount)
                .divide(BigDecimal.valueOf(totalQuestions), 4, BigDecimal.ROUND_HALF_UP);
        return this.accuracyRate;
    }

    /**
     * 计算用时
     */
    public void calculateTimeSpent() {
        if (startTime != null && endTime != null) {
            this.timeSpentMinutes = (int) java.time.Duration.between(startTime, endTime).toMinutes();
        }
    }

    /**
     * 检查是否超时
     */
    public boolean isTimeout(Integer durationMinutes) {
        if (startTime == null || durationMinutes == null) {
            return false;
        }
        LocalDateTime timeoutTime = startTime.plusMinutes(durationMinutes);
        return LocalDateTime.now().isAfter(timeoutTime);
    }

    /**
     * 完成考试
     */
    public void completeExam() {
        this.endTime = LocalDateTime.now();
        this.submitTime = this.endTime;
        this.status = ExamStatus.COMPLETED;
        calculateTimeSpent();
        calculateAccuracyRate();
    }

    /**
     * 检查是否通过
     */
    public boolean checkPassed(Integer passingScore) {
        if (userScore == null || passingScore == null) {
            return false;
        }
        this.isPassed = userScore >= passingScore;
        return this.isPassed;
    }

}
