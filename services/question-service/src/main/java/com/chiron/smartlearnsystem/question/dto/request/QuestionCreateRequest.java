package com.chiron.smartlearnsystem.exercise.dto.request;

import com.chiron.smartlearnsystem.common.core.enums.Difficulty;
import com.chiron.smartlearnsystem.exercise.enums.QuestionSource;
import com.chiron.smartlearnsystem.exercise.enums.QuestionType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.*;
import java.util.Set;

/**
 * 题目创建请求
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuestionCreateRequest {

    /**
     * 题目内容
     */
    @NotBlank(message = "题目内容不能为空")
    @Size(max = 5000, message = "题目内容长度不能超过5000个字符")
    private String content;

    /**
     * 题目类型
     */
    @NotNull(message = "题目类型不能为空")
    private QuestionType type;

    /**
     * 难度等级
     */
    @NotNull(message = "难度等级不能为空")
    private Difficulty difficulty;

    /**
     * 科目代码
     */
    @NotBlank(message = "科目代码不能为空")
    @Size(max = 20, message = "科目代码长度不能超过20个字符")
    private String subjectCode;

    /**
     * 题目选项（JSON格式）
     */
    @Size(max = 2000, message = "题目选项长度不能超过2000个字符")
    private String options;

    /**
     * 正确答案
     */
    @NotBlank(message = "正确答案不能为空")
    @Size(max = 500, message = "正确答案长度不能超过500个字符")
    private String correctAnswer;

    /**
     * 详细解析
     */
    @Size(max = 5000, message = "详细解析长度不能超过5000个字符")
    private String explanation;

    /**
     * 题目来源
     */
    private QuestionSource source;

    /**
     * 所属年份（对于真题）
     */
    @Min(value = 2000, message = "年份不能小于2000")
    @Max(value = 2100, message = "年份不能大于2100")
    private Integer examYear;

    /**
     * 题目分值
     */
    @Min(value = 1, message = "题目分值不能小于1")
    @Max(value = 100, message = "题目分值不能大于100")
    private Integer score;

    /**
     * 预估答题时间（秒）
     */
    @Min(value = 10, message = "预估答题时间不能小于10秒")
    @Max(value = 3600, message = "预估答题时间不能大于3600秒")
    private Integer estimatedTime;

    /**
     * 题目标签
     */
    @Size(max = 10, message = "标签数量不能超过10个")
    private Set<String> tags;

    /**
     * 关联知识点
     */
    @Size(max = 20, message = "知识点数量不能超过20个")
    private Set<String> knowledgePoints;

    /**
     * 验证选择题是否有选项
     */
    public boolean isValidChoiceQuestion() {
        if (type == QuestionType.SINGLE_CHOICE || type == QuestionType.MULTIPLE_CHOICE) {
            return options != null && !options.trim().isEmpty();
        }
        return true;
    }

    /**
     * 验证真题是否有年份
     */
    public boolean isValidRealExam() {
        if (source == QuestionSource.REAL_EXAM) {
            return examYear != null;
        }
        return true;
    }

}
