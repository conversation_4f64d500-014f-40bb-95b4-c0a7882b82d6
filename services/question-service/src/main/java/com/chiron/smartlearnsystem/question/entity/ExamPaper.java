package com.chiron.smartlearnsystem.question.entity;

import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import com.chiron.smartlearnsystem.common.database.entity.BaseEntity;
import com.chiron.smartlearnsystem.question.enums.PaperStatus;
import com.chiron.smartlearnsystem.question.enums.PaperType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 考试试卷实体
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Entity
@Table(name = "exam_papers", indexes = {
    @Index(name = "idx_subject_type", columnList = "subject_code, paper_type"),
    @Index(name = "idx_status", columnList = "status"),
    @Index(name = "idx_created_by", columnList = "created_by"),
    @Index(name = "idx_created_at", columnList = "created_at")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExamPaper extends BaseEntity {

    /**
     * 试卷标题
     */
    @Column(nullable = false, length = 200)
    private String title;

    /**
     * 试卷描述
     */
    @Column(columnDefinition = "TEXT")
    private String description;

    /**
     * 科目代码
     */
    @Column(name = "subject_code", nullable = false, length = 20)
    private String subjectCode;

    /**
     * 试卷类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "paper_type", nullable = false, length = 20)
    private PaperType paperType;

    /**
     * 考试时长（分钟）
     */
    @Column(name = "duration_minutes", nullable = false)
    private Integer durationMinutes;

    /**
     * 总分
     */
    @Column(name = "total_score", nullable = false)
    private Integer totalScore;

    /**
     * 及格分数
     */
    @Column(name = "passing_score", nullable = false)
    private Integer passingScore;

    /**
     * 题目配置信息（JSON格式）
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "question_config", columnDefinition = "JSON")
    private String questionConfig;

    /**
     * 试卷状态
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private PaperStatus status = PaperStatus.ACTIVE;

    /**
     * 创建人ID
     */
    @Column(name = "created_by")
    private Long createdBy;

    /**
     * 试卷年份（对于真题）
     */
    @Column(name = "exam_year")
    private Integer examYear;

    /**
     * 试卷季度（对于真题：上半年/下半年）
     */
    @Column(name = "exam_season", length = 20)
    private String examSeason;

    /**
     * 难度分布配置
     */
    @Column(name = "difficulty_distribution", columnDefinition = "TEXT")
    private String difficultyDistribution;

    /**
     * 知识点分布配置
     */
    @Column(name = "knowledge_distribution", columnDefinition = "TEXT")
    private String knowledgeDistribution;

    /**
     * 是否允许查看答案
     */
    @Column(name = "allow_view_answer", nullable = false)
    private Boolean allowViewAnswer = true;

    /**
     * 是否允许查看解析
     */
    @Column(name = "allow_view_explanation", nullable = false)
    private Boolean allowViewExplanation = true;

    /**
     * 是否随机题目顺序
     */
    @Column(name = "random_question_order", nullable = false)
    private Boolean randomQuestionOrder = false;

    /**
     * 是否随机选项顺序
     */
    @Column(name = "random_option_order", nullable = false)
    private Boolean randomOptionOrder = false;

    /**
     * 试卷使用次数
     */
    @Column(name = "usage_count", nullable = false)
    private Integer usageCount = 0;

    /**
     * 平均分数
     */
    @Column(name = "average_score")
    private Double averageScore;

    /**
     * 通过率
     */
    @Column(name = "pass_rate")
    private Double passRate;

    /**
     * 试卷包含的题目
     */
    @OneToMany(mappedBy = "examPaper", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<PaperQuestion> paperQuestions = new ArrayList<>();

    /**
     * 增加使用次数
     */
    public void incrementUsageCount() {
        this.usageCount++;
    }

    /**
     * 更新统计信息
     */
    public void updateStatistics(Double newAverageScore, Double newPassRate) {
        this.averageScore = newAverageScore;
        this.passRate = newPassRate;
    }

    /**
     * 检查是否为真题
     */
    public boolean isRealExam() {
        return PaperType.REAL_EXAM.equals(this.paperType);
    }

    /**
     * 检查是否可用
     */
    public boolean isActive() {
        return PaperStatus.ACTIVE.equals(this.status);
    }

    /**
     * 获取题目总数
     */
    public int getQuestionCount() {
        return paperQuestions != null ? paperQuestions.size() : 0;
    }

}
