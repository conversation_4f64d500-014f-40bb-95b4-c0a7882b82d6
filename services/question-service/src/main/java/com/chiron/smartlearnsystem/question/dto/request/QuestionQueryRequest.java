package com.chiron.smartlearnsystem.question.dto.request;

import com.chiron.smartlearnsystem.common.core.enums.Difficulty;
import com.chiron.smartlearnsystem.question.enums.QuestionSource;
import com.chiron.smartlearnsystem.question.enums.QuestionType;
import com.chiron.smartlearnsystem.question.enums.ReviewStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.util.Set;

/**
 * 题目查询请求
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuestionQueryRequest {

    /**
     * 页码
     */
    @Min(value = 1, message = "页码不能小于1")
    private Integer page = 1;

    /**
     * 页大小
     */
    @Min(value = 1, message = "页大小不能小于1")
    @Max(value = 100, message = "页大小不能大于100")
    private Integer size = 20;

    /**
     * 科目代码
     */
    private String subjectCode;

    /**
     * 题目类型
     */
    private QuestionType type;

    /**
     * 难度等级
     */
    private Difficulty difficulty;

    /**
     * 题目来源
     */
    private QuestionSource source;

    /**
     * 审核状态
     */
    private ReviewStatus reviewStatus;

    /**
     * 所属年份
     */
    private Integer examYear;

    /**
     * 关键字搜索
     */
    private String keyword;

    /**
     * 知识点
     */
    private Set<String> knowledgePoints;

    /**
     * 标签
     */
    private Set<String> tags;

    /**
     * 最小正确率
     */
    private Double minCorrectRate;

    /**
     * 最大正确率
     */
    private Double maxCorrectRate;

    /**
     * 最小答题次数
     */
    private Integer minAttempts;

    /**
     * 是否只查询已审核通过的题目
     */
    private Boolean onlyApproved = true;

    /**
     * 排序字段
     */
    private String sortBy = "createdAt";

    /**
     * 排序方向
     */
    private String sortDirection = "DESC";

}
