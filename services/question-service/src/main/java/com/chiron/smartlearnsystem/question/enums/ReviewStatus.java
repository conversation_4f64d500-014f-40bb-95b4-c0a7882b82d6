package com.chiron.smartlearnsystem.exercise.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 审核状态枚举
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum ReviewStatus {

    /**
     * 待审核
     */
    PENDING("PENDING", "待审核", "题目已提交，等待审核"),

    /**
     * 审核中
     */
    REVIEWING("REVIEWING", "审核中", "题目正在审核过程中"),

    /**
     * 审核通过
     */
    APPROVED("APPROVED", "审核通过", "题目审核通过，可以使用"),

    /**
     * 审核拒绝
     */
    REJECTED("REJECTED", "审核拒绝", "题目审核未通过，需要修改"),

    /**
     * 需要修改
     */
    NEED_REVISION("NEED_REVISION", "需要修改", "题目需要根据审核意见进行修改"),

    /**
     * 已下架
     */
    ARCHIVED("ARCHIVED", "已下架", "题目已下架，不再使用");

    private final String code;
    private final String name;
    private final String description;

    /**
     * 根据代码获取审核状态
     */
    public static ReviewStatus getByCode(String code) {
        for (ReviewStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return PENDING; // 默认返回待审核
    }

    /**
     * 是否可以使用
     */
    public boolean isUsable() {
        return this == APPROVED;
    }

    /**
     * 是否需要处理
     */
    public boolean needsAction() {
        return this == PENDING || this == NEED_REVISION;
    }

    /**
     * 是否为最终状态
     */
    public boolean isFinalStatus() {
        return this == APPROVED || this == REJECTED || this == ARCHIVED;
    }

}
