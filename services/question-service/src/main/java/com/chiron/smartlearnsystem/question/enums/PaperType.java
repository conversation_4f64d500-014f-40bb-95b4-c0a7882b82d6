package com.chiron.smartlearnsystem.question.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 试卷类型枚举
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Getter
public enum PaperType {

    /**
     * 历年真题
     */
    REAL_EXAM("REAL_EXAM", "历年真题", "软考历年真题试卷"),

    /**
     * 模拟考试
     */
    SIMULATION("SIMULATION", "模拟考试", "模拟真实考试环境的试卷"),

    /**
     * 专项练习
     */
    PRACTICE("PRACTICE", "专项练习", "针对特定知识点的练习试卷"),

    /**
     * 智能组卷
     */
    INTELLIGENT("INTELLIGENT", "智能组卷", "AI根据用户水平智能生成的试卷"),

    /**
     * 错题重练
     */
    WRONG_QUESTION("WRONG_QUESTION", "错题重练", "基于用户错题生成的试卷"),

    /**
     * 冲刺试卷
     */
    SPRINT("SPRINT", "冲刺试卷", "考前冲刺专用试卷"),

    /**
     * 自定义试卷
     */
    CUSTOM("CUSTOM", "自定义试卷", "用户或教师自定义的试卷");

    private final String code;
    private final String name;
    private final String description;

    PaperType(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    /**
     * 根据代码获取试卷类型
     */
    public static PaperType getByCode(String code) {
        for (PaperType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return PRACTICE; // 默认返回专项练习
    }

    /**
     * 是否为正式考试类型
     */
    public boolean isFormalExam() {
        return this == REAL_EXAM || this == SIMULATION;
    }

    /**
     * 是否为练习类型
     */
    public boolean isPracticeType() {
        return this == PRACTICE || this == WRONG_QUESTION || this == CUSTOM;
    }

    /**
     * 是否为AI生成类型
     */
    public boolean isAIGenerated() {
        return this == INTELLIGENT;
    }

    /**
     * 获取推荐权重
     */
    public double getRecommendationWeight() {
        switch (this) {
            case REAL_EXAM:
                return 1.0;
            case SIMULATION:
                return 0.9;
            case SPRINT:
                return 0.8;
            case INTELLIGENT:
                return 0.7;
            case PRACTICE:
                return 0.6;
            case WRONG_QUESTION:
                return 0.8; // 错题重练权重较高
            case CUSTOM:
                return 0.5;
            default:
                return 0.5;
        }
    }

}
