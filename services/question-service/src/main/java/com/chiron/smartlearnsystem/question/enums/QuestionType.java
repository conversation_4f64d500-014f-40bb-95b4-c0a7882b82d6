package com.chiron.smartlearnsystem.question.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 题目类型枚举
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Getter
public enum QuestionType {

    /**
     * 单选题
     */
    SINGLE_CHOICE("SINGLE_CHOICE", "单选题", "从多个选项中选择一个正确答案"),

    /**
     * 多选题
     */
    MULTIPLE_CHOICE("MULTIPLE_CHOICE", "多选题", "从多个选项中选择多个正确答案"),

    /**
     * 判断题
     */
    TRUE_FALSE("TRUE_FALSE", "判断题", "判断题目描述是否正确"),

    /**
     * 填空题
     */
    FILL_BLANK("FILL_BLANK", "填空题", "在空白处填入正确答案"),

    /**
     * 简答题
     */
    SHORT_ANSWER("SHORT_ANSWER", "简答题", "用简短文字回答问题"),

    /**
     * 案例分析题
     */
    CASE_ANALYSIS("CASE_ANALYSIS", "案例分析题", "分析给定案例并回答相关问题"),

    /**
     * 综合应用题
     */
    COMPREHENSIVE("COMPREHENSIVE", "综合应用题", "综合运用多个知识点解决问题");

    private final String code;
    private final String name;
    private final String description;

    QuestionType(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    /**
     * 根据代码获取题目类型
     */
    public static QuestionType getByCode(String code) {
        for (QuestionType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return SINGLE_CHOICE; // 默认返回单选题
    }

    /**
     * 是否为选择题
     */
    public boolean isChoiceType() {
        return this == SINGLE_CHOICE || this == MULTIPLE_CHOICE;
    }

    /**
     * 是否为主观题
     */
    public boolean isSubjectiveType() {
        return this == SHORT_ANSWER || this == CASE_ANALYSIS || this == COMPREHENSIVE;
    }

    /**
     * 是否为客观题
     */
    public boolean isObjectiveType() {
        return !isSubjectiveType();
    }

}
