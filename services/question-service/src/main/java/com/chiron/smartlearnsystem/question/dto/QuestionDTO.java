package com.chiron.smartlearnsystem.exercise.dto;

import com.chiron.smartlearnsystem.common.core.enums.Difficulty;
import com.chiron.smartlearnsystem.exercise.entity.Question;
import com.chiron.smartlearnsystem.exercise.enums.QuestionSource;
import com.chiron.smartlearnsystem.exercise.enums.QuestionType;
import com.chiron.smartlearnsystem.exercise.enums.ReviewStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Set;

/**
 * 题目DTO
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuestionDTO {

    /**
     * 题目ID
     */
    private Long id;

    /**
     * 题目内容
     */
    private String content;

    /**
     * 题目类型
     */
    private QuestionType type;

    /**
     * 难度等级
     */
    private Difficulty difficulty;

    /**
     * 科目代码
     */
    private String subjectCode;

    /**
     * 题目选项
     */
    private String options;

    /**
     * 正确答案
     */
    private String correctAnswer;

    /**
     * 详细解析
     */
    private String explanation;

    /**
     * 题目来源
     */
    private QuestionSource source;

    /**
     * 所属年份
     */
    private Integer examYear;

    /**
     * 题目序号
     */
    private Integer questionNumber;

    /**
     * 总答题次数
     */
    private Integer totalAttempts;

    /**
     * 正确答题次数
     */
    private Integer correctAttempts;

    /**
     * 正确率
     */
    private BigDecimal correctRate;

    /**
     * 审核状态
     */
    private ReviewStatus reviewStatus;

    /**
     * 审核人ID
     */
    private Long reviewerId;

    /**
     * 审核意见
     */
    private String reviewComment;

    /**
     * 题目分值
     */
    private Integer score;

    /**
     * 预估答题时间（秒）
     */
    private Integer estimatedTime;

    /**
     * 题目标签
     */
    private Set<String> tags;

    /**
     * 关联知识点
     */
    private Set<String> knowledgePoints;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 从Question实体转换为DTO
     */
    public static QuestionDTO from(Question question) {
        if (question == null) {
            return null;
        }

        return QuestionDTO.builder()
                .id(question.getId())
                .content(question.getContent())
                .type(question.getType())
                .difficulty(question.getDifficulty())
                .subjectCode(question.getSubjectCode())
                .options(question.getOptions())
                .correctAnswer(question.getCorrectAnswer())
                .explanation(question.getExplanation())
                .source(question.getSource())
                .examYear(question.getExamYear())
                .questionNumber(question.getQuestionNumber())
                .totalAttempts(question.getTotalAttempts())
                .correctAttempts(question.getCorrectAttempts())
                .correctRate(question.getCorrectRate())
                .reviewStatus(question.getReviewStatus())
                .reviewerId(question.getReviewerId())
                .reviewComment(question.getReviewComment())
                .score(question.getScore())
                .estimatedTime(question.getEstimatedTime())
                .tags(question.getTags())
                .knowledgePoints(question.getKnowledgePoints())
                .createdAt(question.getCreatedAt())
                .updatedAt(question.getUpdatedAt())
                .build();
    }

    /**
     * 从Question实体转换为简化DTO（不包含答案和解析）
     */
    public static QuestionDTO fromWithoutAnswer(Question question) {
        if (question == null) {
            return null;
        }

        return QuestionDTO.builder()
                .id(question.getId())
                .content(question.getContent())
                .type(question.getType())
                .difficulty(question.getDifficulty())
                .subjectCode(question.getSubjectCode())
                .options(question.getOptions())
                // 不包含正确答案和解析
                .source(question.getSource())
                .examYear(question.getExamYear())
                .questionNumber(question.getQuestionNumber())
                .score(question.getScore())
                .estimatedTime(question.getEstimatedTime())
                .tags(question.getTags())
                .knowledgePoints(question.getKnowledgePoints())
                .createdAt(question.getCreatedAt())
                .build();
    }

    /**
     * 检查是否为真题
     */
    public boolean isRealExam() {
        return source != null && source.isOfficial();
    }

    /**
     * 检查是否已审核通过
     */
    public boolean isApproved() {
        return ReviewStatus.APPROVED.equals(reviewStatus);
    }

    /**
     * 获取难度描述
     */
    public String getDifficultyDescription() {
        return difficulty != null ? difficulty.getDescription() : "";
    }

    /**
     * 获取类型描述
     */
    public String getTypeDescription() {
        return type != null ? type.getName() : "";
    }

}
