package com.chiron.smartlearnsystem.question.entity;

import com.chiron.smartlearnsystem.common.database.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import java.time.LocalDateTime;

/**
 * 答题记录实体
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Entity
@Table(name = "answer_records", indexes = {
    @Index(name = "idx_user_question", columnList = "user_id, question_id"),
    @Index(name = "idx_exam_record", columnList = "exam_record_id"),
    @Index(name = "idx_question_id", columnList = "question_id"),
    @Index(name = "idx_answer_time", columnList = "answer_time"),
    @Index(name = "idx_is_correct", columnList = "is_correct")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AnswerRecord extends BaseEntity {

    /**
     * 用户ID
     */
    @Column(name = "user_id", nullable = false)
    private Long userId;

    /**
     * 题目ID
     */
    @Column(name = "question_id", nullable = false)
    private Long questionId;

    /**
     * 考试记录ID（可为空，表示单独练习）
     */
    @Column(name = "exam_record_id")
    private Long examRecordId;

    /**
     * 用户答案
     */
    @Column(name = "user_answer", columnDefinition = "TEXT")
    private String userAnswer;

    /**
     * 正确答案
     */
    @Column(name = "correct_answer", columnDefinition = "TEXT")
    private String correctAnswer;

    /**
     * 是否正确
     */
    @Column(name = "is_correct", nullable = false)
    private Boolean isCorrect = false;

    /**
     * 答题时间
     */
    @Column(name = "answer_time", nullable = false)
    private LocalDateTime answerTime;

    /**
     * 用时（秒）
     */
    @Column(name = "time_spent_seconds")
    private Integer timeSpentSeconds;

    /**
     * 得分
     */
    @Column(name = "score")
    private Integer score = 0;

    /**
     * 题目分值
     */
    @Column(name = "question_score")
    private Integer questionScore;

    /**
     * 答题来源（练习/考试/复习）
     */
    @Column(name = "answer_source", length = 20)
    private String answerSource = "PRACTICE";

    /**
     * 科目代码
     */
    @Column(name = "subject_code", length = 20)
    private String subjectCode;

    /**
     * 知识点（JSON格式）
     */
    @Column(name = "knowledge_points", columnDefinition = "TEXT")
    private String knowledgePoints;

    /**
     * 题目难度
     */
    @Column(name = "difficulty", length = 20)
    private String difficulty;

    /**
     * 是否标记
     */
    @Column(name = "is_marked", nullable = false)
    private Boolean isMarked = false;

    /**
     * 用户备注
     */
    @Column(name = "user_note", columnDefinition = "TEXT")
    private String userNote;

    /**
     * IP地址
     */
    @Column(name = "ip_address", length = 50)
    private String ipAddress;

    /**
     * 设备信息
     */
    @Column(name = "device_info", columnDefinition = "TEXT")
    private String deviceInfo;

    /**
     * 关联的题目
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "question_id", insertable = false, updatable = false)
    private Question question;

    /**
     * 关联的考试记录
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "exam_record_id", insertable = false, updatable = false)
    private ExamRecord examRecord;

    /**
     * 计算得分
     */
    public void calculateScore() {
        if (questionScore == null) {
            this.score = 0;
            return;
        }
        
        if (Boolean.TRUE.equals(isCorrect)) {
            this.score = questionScore;
        } else {
            this.score = 0;
        }
    }

    /**
     * 检查答案是否正确
     */
    public void checkAnswer() {
        if (userAnswer == null || correctAnswer == null) {
            this.isCorrect = false;
            return;
        }
        
        // 简单的字符串比较，实际可能需要更复杂的逻辑
        this.isCorrect = userAnswer.trim().equalsIgnoreCase(correctAnswer.trim());
        calculateScore();
    }

    /**
     * 设置答题时间
     */
    public void setAnswerTimeNow() {
        this.answerTime = LocalDateTime.now();
    }

    /**
     * 是否为错题
     */
    public boolean isWrongAnswer() {
        return Boolean.FALSE.equals(isCorrect);
    }

    /**
     * 是否为练习答题
     */
    public boolean isPractice() {
        return "PRACTICE".equals(answerSource);
    }

    /**
     * 是否为考试答题
     */
    public boolean isExam() {
        return "EXAM".equals(answerSource);
    }

}
