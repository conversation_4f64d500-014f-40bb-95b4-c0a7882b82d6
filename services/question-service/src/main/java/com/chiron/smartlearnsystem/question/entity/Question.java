package com.chiron.smartlearnsystem.exercise.entity;

import com.chiron.smartlearnsystem.common.core.enums.Difficulty;
import com.chiron.smartlearnsystem.common.database.entity.BaseEntity;
import com.chiron.smartlearnsystem.exercise.enums.QuestionSource;
import com.chiron.smartlearnsystem.exercise.enums.QuestionType;
import com.chiron.smartlearnsystem.exercise.enums.ReviewStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.HashSet;
import java.util.Set;

/**
 * 题目实体
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Entity
@Table(name = "questions", indexes = {
    @Index(name = "idx_subject_difficulty", columnList = "subject_code, difficulty"),
    @Index(name = "idx_source_year", columnList = "source, exam_year"),
    @Index(name = "idx_review_status", columnList = "review_status"),
    @Index(name = "idx_question_type", columnList = "type"),
    @Index(name = "idx_created_at", columnList = "created_at")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Question extends BaseEntity {

    /**
     * 题目内容
     */
    @Column(columnDefinition = "TEXT", nullable = false)
    private String content;

    /**
     * 题目类型
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private QuestionType type;

    /**
     * 难度等级
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private Difficulty difficulty;

    /**
     * 科目代码
     */
    @Column(name = "subject_code", nullable = false, length = 20)
    private String subjectCode;

    /**
     * 题目选项（JSON格式存储）
     */
    @Type(type = "json")
    @Column(columnDefinition = "JSON")
    private String options;

    /**
     * 正确答案
     */
    @Column(name = "correct_answer", nullable = false, length = 500)
    private String correctAnswer;

    /**
     * 详细解析
     */
    @Column(columnDefinition = "TEXT")
    private String explanation;

    /**
     * 题目来源
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private QuestionSource source = QuestionSource.PRACTICE;

    /**
     * 所属年份（对于真题）
     */
    @Column(name = "exam_year")
    private Integer examYear;

    /**
     * 题目序号（在试卷中的位置）
     */
    @Column(name = "question_number")
    private Integer questionNumber;

    /**
     * 总答题次数
     */
    @Column(name = "total_attempts", nullable = false)
    private Integer totalAttempts = 0;

    /**
     * 正确答题次数
     */
    @Column(name = "correct_attempts", nullable = false)
    private Integer correctAttempts = 0;

    /**
     * 审核状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "review_status", nullable = false, length = 20)
    private ReviewStatus reviewStatus = ReviewStatus.PENDING;

    /**
     * 审核人ID
     */
    @Column(name = "reviewer_id")
    private Long reviewerId;

    /**
     * 审核意见
     */
    @Column(name = "review_comment", columnDefinition = "TEXT")
    private String reviewComment;

    /**
     * 题目分值
     */
    @Column(name = "score", nullable = false)
    private Integer score = 1;

    /**
     * 预估答题时间（秒）
     */
    @Column(name = "estimated_time")
    private Integer estimatedTime;

    /**
     * 题目标签
     */
    @ElementCollection
    @CollectionTable(
        name = "question_tags",
        joinColumns = @JoinColumn(name = "question_id")
    )
    @Column(name = "tag")
    private Set<String> tags = new HashSet<>();

    /**
     * 关联知识点
     */
    @ElementCollection
    @CollectionTable(
        name = "question_knowledge_points",
        joinColumns = @JoinColumn(name = "question_id")
    )
    @Column(name = "knowledge_point")
    private Set<String> knowledgePoints = new HashSet<>();

    /**
     * 计算正确率
     */
    public BigDecimal getCorrectRate() {
        if (totalAttempts == 0) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(correctAttempts)
                .divide(BigDecimal.valueOf(totalAttempts), 4, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 增加答题统计
     */
    public void addAttempt(boolean isCorrect) {
        this.totalAttempts++;
        if (isCorrect) {
            this.correctAttempts++;
        }
    }

    /**
     * 检查是否为真题
     */
    public boolean isRealExam() {
        return QuestionSource.REAL_EXAM.equals(this.source);
    }

    /**
     * 检查是否已审核通过
     */
    public boolean isApproved() {
        return ReviewStatus.APPROVED.equals(this.reviewStatus);
    }

    /**
     * 添加知识点
     */
    public void addKnowledgePoint(String knowledgePoint) {
        this.knowledgePoints.add(knowledgePoint);
    }

    /**
     * 添加标签
     */
    public void addTag(String tag) {
        this.tags.add(tag);
    }

}
