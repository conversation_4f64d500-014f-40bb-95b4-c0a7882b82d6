package com.chiron.smartlearnsystem.exercise.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 题目来源枚举
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum QuestionSource {

    /**
     * 历年真题
     */
    REAL_EXAM("REAL_EXAM", "历年真题", "来自历年软考真题"),

    /**
     * 模拟题
     */
    SIMULATION("SIMULATION", "模拟题", "专业团队编写的模拟题"),

    /**
     * 练习题
     */
    PRACTICE("PRACTICE", "练习题", "日常练习使用的题目"),

    /**
     * 用户贡献
     */
    USER_CONTRIBUTED("USER_CONTRIBUTED", "用户贡献", "用户上传贡献的题目"),

    /**
     * AI生成
     */
    AI_GENERATED("AI_GENERATED", "AI生成", "人工智能生成的题目"),

    /**
     * 第三方导入
     */
    THIRD_PARTY("THIRD_PARTY", "第三方导入", "从第三方平台导入的题目");

    private final String code;
    private final String name;
    private final String description;

    /**
     * 根据代码获取题目来源
     */
    public static QuestionSource getByCode(String code) {
        for (QuestionSource source : values()) {
            if (source.getCode().equals(code)) {
                return source;
            }
        }
        return PRACTICE; // 默认返回练习题
    }

    /**
     * 是否为官方题目
     */
    public boolean isOfficial() {
        return this == REAL_EXAM || this == SIMULATION;
    }

    /**
     * 是否需要审核
     */
    public boolean needsReview() {
        return this == USER_CONTRIBUTED || this == AI_GENERATED || this == THIRD_PARTY;
    }

    /**
     * 获取权重（用于推荐算法）
     */
    public double getWeight() {
        switch (this) {
            case REAL_EXAM:
                return 1.0;
            case SIMULATION:
                return 0.9;
            case PRACTICE:
                return 0.8;
            case USER_CONTRIBUTED:
                return 0.7;
            case AI_GENERATED:
                return 0.6;
            case THIRD_PARTY:
                return 0.5;
            default:
                return 0.5;
        }
    }

}
