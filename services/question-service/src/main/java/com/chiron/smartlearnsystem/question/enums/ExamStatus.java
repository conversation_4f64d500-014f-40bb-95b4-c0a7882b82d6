package com.chiron.smartlearnsystem.exercise.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 考试状态枚举
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum ExamStatus {

    /**
     * 进行中
     */
    IN_PROGRESS("IN_PROGRESS", "进行中", "考试正在进行"),

    /**
     * 已完成
     */
    COMPLETED("COMPLETED", "已完成", "考试已正常完成"),

    /**
     * 已提交
     */
    SUBMITTED("SUBMITTED", "已提交", "考试已提交等待评分"),

    /**
     * 已评分
     */
    GRADED("GRADED", "已评分", "考试已完成评分"),

    /**
     * 超时
     */
    TIMEOUT("TIMEOUT", "超时", "考试时间超时"),

    /**
     * 中途放弃
     */
    ABANDONED("ABANDONED", "中途放弃", "考试中途放弃"),

    /**
     * 异常中断
     */
    INTERRUPTED("INTERRUPTED", "异常中断", "考试异常中断"),

    /**
     * 作弊
     */
    CHEATING("CHEATING", "作弊", "检测到作弊行为"),

    /**
     * 已取消
     */
    CANCELLED("CANCELLED", "已取消", "考试已取消");

    private final String code;
    private final String name;
    private final String description;

    /**
     * 根据代码获取考试状态
     */
    public static ExamStatus getByCode(String code) {
        for (ExamStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return IN_PROGRESS; // 默认返回进行中
    }

    /**
     * 是否为进行中状态
     */
    public boolean isInProgress() {
        return this == IN_PROGRESS;
    }

    /**
     * 是否为完成状态
     */
    public boolean isCompleted() {
        return this == COMPLETED || this == SUBMITTED || this == GRADED;
    }

    /**
     * 是否为异常状态
     */
    public boolean isAbnormal() {
        return this == TIMEOUT || this == ABANDONED || this == INTERRUPTED || 
               this == CHEATING || this == CANCELLED;
    }

    /**
     * 是否可以继续考试
     */
    public boolean canContinue() {
        return this == IN_PROGRESS;
    }

    /**
     * 是否需要评分
     */
    public boolean needsGrading() {
        return this == SUBMITTED;
    }

    /**
     * 是否为最终状态
     */
    public boolean isFinalStatus() {
        return this == COMPLETED || this == GRADED || this == TIMEOUT || 
               this == ABANDONED || this == INTERRUPTED || this == CHEATING || 
               this == CANCELLED;
    }

}
