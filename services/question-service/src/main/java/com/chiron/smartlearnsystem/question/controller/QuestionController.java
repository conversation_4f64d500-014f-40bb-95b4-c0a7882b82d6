package com.chiron.smartlearnsystem.exercise.controller;

import com.chiron.smartlearnsystem.common.core.domain.ApiResponse;
import com.chiron.smartlearnsystem.common.core.domain.PageResult;
import com.chiron.smartlearnsystem.common.core.enums.Difficulty;
import com.chiron.smartlearnsystem.exercise.dto.QuestionDTO;
import com.chiron.smartlearnsystem.exercise.dto.request.QuestionCreateRequest;
import com.chiron.smartlearnsystem.exercise.dto.request.QuestionQueryRequest;
import com.chiron.smartlearnsystem.exercise.dto.request.QuestionUpdateRequest;
import com.chiron.smartlearnsystem.exercise.enums.QuestionType;
import com.chiron.smartlearnsystem.exercise.enums.ReviewStatus;
import com.chiron.smartlearnsystem.exercise.service.QuestionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.List;

/**
 * 题目控制器
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/questions")
@RequiredArgsConstructor
@Validated
public class QuestionController {

    private final QuestionService questionService;

    /**
     * 创建题目
     */
    @PostMapping
    @PreAuthorize("hasRole('TEACHER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<QuestionDTO>> createQuestion(
            @Valid @RequestBody QuestionCreateRequest request) {
        
        log.info("创建题目请求: {}", request);
        
        // 验证选择题选项
        if (!request.isValidChoiceQuestion()) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.fail("选择题必须提供选项"));
        }
        
        // 验证真题年份
        if (!request.isValidRealExam()) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.fail("真题必须提供年份"));
        }
        
        QuestionDTO questionDTO = questionService.createQuestion(request);
        
        return ResponseEntity.ok(ApiResponse.success("题目创建成功", questionDTO));
    }

    /**
     * 更新题目
     */
    @PutMapping("/{questionId}")
    @PreAuthorize("hasRole('TEACHER') or hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<QuestionDTO>> updateQuestion(
            @PathVariable Long questionId,
            @Valid @RequestBody QuestionUpdateRequest request) {
        
        log.info("更新题目请求: questionId={}", questionId);
        
        QuestionDTO questionDTO = questionService.updateQuestion(questionId, request);
        
        return ResponseEntity.ok(ApiResponse.success("题目更新成功", questionDTO));
    }

    /**
     * 获取题目详情
     */
    @GetMapping("/{questionId}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<QuestionDTO>> getQuestion(@PathVariable Long questionId) {
        
        log.info("获取题目详情: questionId={}", questionId);
        
        QuestionDTO questionDTO = questionService.getQuestionById(questionId);
        
        return ResponseEntity.ok(ApiResponse.success("获取题目成功", questionDTO));
    }

    /**
     * 分页查询题目
     */
    @GetMapping
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<PageResult<QuestionDTO>>> getQuestions(
            @Valid QuestionQueryRequest request) {
        
        log.info("分页查询题目: {}", request);
        
        PageResult<QuestionDTO> result = questionService.getQuestions(request);
        
        return ResponseEntity.ok(ApiResponse.success("查询题目成功", result));
    }

    /**
     * 随机获取题目
     */
    @GetMapping("/random")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<QuestionDTO>>> getRandomQuestions(
            @RequestParam String subjectCode,
            @RequestParam(required = false) Difficulty difficulty,
            @RequestParam(required = false) QuestionType type,
            @RequestParam @Min(1) @Max(50) Integer count) {
        
        log.info("随机获取题目: subject={}, difficulty={}, type={}, count={}", 
                subjectCode, difficulty, type, count);
        
        List<QuestionDTO> questions = questionService.getRandomQuestions(
                subjectCode, difficulty, type, count);
        
        return ResponseEntity.ok(ApiResponse.success("获取随机题目成功", questions));
    }

    /**
     * 搜索题目
     */
    @GetMapping("/search")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<PageResult<QuestionDTO>>> searchQuestions(
            @RequestParam String keyword,
            @RequestParam(required = false) String subjectCode,
            @RequestParam(defaultValue = "1") @Min(1) Integer page,
            @RequestParam(defaultValue = "20") @Min(1) @Max(100) Integer size) {
        
        log.info("搜索题目: keyword={}, subject={}", keyword, subjectCode);
        
        PageResult<QuestionDTO> result = questionService.searchQuestions(
                keyword, subjectCode, page, size);
        
        return ResponseEntity.ok(ApiResponse.success("搜索题目成功", result));
    }

    /**
     * 获取热门题目
     */
    @GetMapping("/popular")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<QuestionDTO>>> getPopularQuestions(
            @RequestParam(required = false) String subjectCode,
            @RequestParam(defaultValue = "10") @Min(1) @Max(50) Integer limit) {
        
        log.info("获取热门题目: subject={}, limit={}", subjectCode, limit);
        
        List<QuestionDTO> questions = questionService.getPopularQuestions(subjectCode, limit);
        
        return ResponseEntity.ok(ApiResponse.success("获取热门题目成功", questions));
    }

    /**
     * 获取困难题目
     */
    @GetMapping("/difficult")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<QuestionDTO>>> getDifficultQuestions(
            @RequestParam(required = false) String subjectCode,
            @RequestParam(defaultValue = "10") @Min(1) @Max(50) Integer limit) {
        
        log.info("获取困难题目: subject={}, limit={}", subjectCode, limit);
        
        List<QuestionDTO> questions = questionService.getDifficultQuestions(subjectCode, limit);
        
        return ResponseEntity.ok(ApiResponse.success("获取困难题目成功", questions));
    }

    /**
     * 审核题目
     */
    @PostMapping("/{questionId}/review")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TEACHER')")
    public ResponseEntity<ApiResponse<Void>> reviewQuestion(
            @PathVariable Long questionId,
            @RequestParam ReviewStatus status,
            @RequestParam(required = false) String comment) {
        
        log.info("审核题目: questionId={}, status={}", questionId, status);
        
        // TODO: 从认证信息中获取审核人ID
        Long reviewerId = 1L;
        
        questionService.reviewQuestion(questionId, status, reviewerId, comment);
        
        return ResponseEntity.ok(ApiResponse.success("题目审核成功"));
    }

    /**
     * 删除题目
     */
    @DeleteMapping("/{questionId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> deleteQuestion(@PathVariable Long questionId) {
        
        log.info("删除题目: questionId={}", questionId);
        
        // TODO: 实现软删除逻辑
        
        return ResponseEntity.ok(ApiResponse.success("题目删除成功"));
    }

    /**
     * 记录答题统计
     */
    @PostMapping("/{questionId}/answer-statistics")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Void>> recordAnswerStatistics(
            @PathVariable Long questionId,
            @RequestParam Boolean isCorrect) {
        
        log.debug("记录答题统计: questionId={}, isCorrect={}", questionId, isCorrect);
        
        questionService.recordAnswerStatistics(questionId, isCorrect);
        
        return ResponseEntity.ok(ApiResponse.success("统计记录成功"));
    }

}
