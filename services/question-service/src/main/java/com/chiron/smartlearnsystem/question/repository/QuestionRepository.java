package com.chiron.smartlearnsystem.exercise.repository;

import com.chiron.smartlearnsystem.common.core.enums.Difficulty;
import com.chiron.smartlearnsystem.exercise.entity.Question;
import com.chiron.smartlearnsystem.exercise.enums.QuestionSource;
import com.chiron.smartlearnsystem.exercise.enums.QuestionType;
import com.chiron.smartlearnsystem.exercise.enums.ReviewStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * 题目数据访问层
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Repository
public interface QuestionRepository extends JpaRepository<Question, Long>, JpaSpecificationExecutor<Question> {

    /**
     * 根据科目和难度查找题目
     */
    List<Question> findBySubjectCodeAndDifficulty(String subjectCode, Difficulty difficulty);

    /**
     * 根据科目和题目类型查找题目
     */
    List<Question> findBySubjectCodeAndType(String subjectCode, QuestionType type);

    /**
     * 根据科目、难度和类型查找题目
     */
    List<Question> findBySubjectCodeAndDifficultyAndType(String subjectCode, Difficulty difficulty, QuestionType type);

    /**
     * 根据审核状态查找题目
     */
    List<Question> findByReviewStatus(ReviewStatus reviewStatus);

    /**
     * 根据科目和审核状态查找题目
     */
    Page<Question> findBySubjectCodeAndReviewStatus(String subjectCode, ReviewStatus reviewStatus, Pageable pageable);

    /**
     * 根据题目来源查找题目
     */
    List<Question> findBySource(QuestionSource source);

    /**
     * 根据科目和来源查找题目
     */
    List<Question> findBySubjectCodeAndSource(String subjectCode, QuestionSource source);

    /**
     * 根据年份查找真题
     */
    List<Question> findBySourceAndExamYear(QuestionSource source, Integer examYear);

    /**
     * 根据科目和年份查找真题
     */
    List<Question> findBySubjectCodeAndSourceAndExamYear(String subjectCode, QuestionSource source, Integer examYear);

    /**
     * 查找已审核通过的题目
     */
    @Query("SELECT q FROM Question q WHERE q.reviewStatus = 'APPROVED' AND q.deleted = 0")
    List<Question> findApprovedQuestions();

    /**
     * 根据科目查找已审核通过的题目
     */
    @Query("SELECT q FROM Question q WHERE q.subjectCode = :subjectCode AND q.reviewStatus = 'APPROVED' AND q.deleted = 0")
    List<Question> findApprovedQuestionsBySubject(@Param("subjectCode") String subjectCode);

    /**
     * 根据知识点查找题目
     */
    @Query("SELECT q FROM Question q JOIN q.knowledgePoints kp WHERE kp = :knowledgePoint AND q.reviewStatus = 'APPROVED'")
    List<Question> findByKnowledgePoint(@Param("knowledgePoint") String knowledgePoint);

    /**
     * 根据标签查找题目
     */
    @Query("SELECT q FROM Question q JOIN q.tags t WHERE t = :tag AND q.reviewStatus = 'APPROVED'")
    List<Question> findByTag(@Param("tag") String tag);

    /**
     * 根据正确率范围查找题目
     */
    @Query("SELECT q FROM Question q WHERE q.totalAttempts > 0 AND " +
           "(q.correctAttempts * 1.0 / q.totalAttempts) BETWEEN :minRate AND :maxRate " +
           "AND q.reviewStatus = 'APPROVED'")
    List<Question> findByCorrectRateRange(@Param("minRate") BigDecimal minRate, @Param("maxRate") BigDecimal maxRate);

    /**
     * 查找热门题目（答题次数多）
     */
    @Query("SELECT q FROM Question q WHERE q.reviewStatus = 'APPROVED' AND q.totalAttempts > :minAttempts " +
           "ORDER BY q.totalAttempts DESC")
    List<Question> findPopularQuestions(@Param("minAttempts") Integer minAttempts, Pageable pageable);

    /**
     * 查找困难题目（正确率低）
     */
    @Query("SELECT q FROM Question q WHERE q.totalAttempts >= :minAttempts AND " +
           "(q.correctAttempts * 1.0 / q.totalAttempts) <= :maxCorrectRate " +
           "AND q.reviewStatus = 'APPROVED' ORDER BY (q.correctAttempts * 1.0 / q.totalAttempts) ASC")
    List<Question> findDifficultQuestions(@Param("minAttempts") Integer minAttempts, 
                                        @Param("maxCorrectRate") BigDecimal maxCorrectRate, 
                                        Pageable pageable);

    /**
     * 随机获取题目
     */
    @Query(value = "SELECT * FROM questions q WHERE q.subject_code = :subjectCode " +
                   "AND q.review_status = 'APPROVED' AND q.deleted = 0 " +
                   "ORDER BY RAND() LIMIT :limit", nativeQuery = true)
    List<Question> findRandomQuestions(@Param("subjectCode") String subjectCode, @Param("limit") Integer limit);

    /**
     * 根据条件随机获取题目
     */
    @Query(value = "SELECT * FROM questions q WHERE q.subject_code = :subjectCode " +
                   "AND q.difficulty = :difficulty AND q.type = :type " +
                   "AND q.review_status = 'APPROVED' AND q.deleted = 0 " +
                   "ORDER BY RAND() LIMIT :limit", nativeQuery = true)
    List<Question> findRandomQuestionsByCondition(@Param("subjectCode") String subjectCode,
                                                 @Param("difficulty") String difficulty,
                                                 @Param("type") String type,
                                                 @Param("limit") Integer limit);

    /**
     * 更新题目统计信息
     */
    @Modifying
    @Query("UPDATE Question q SET q.totalAttempts = q.totalAttempts + 1, " +
           "q.correctAttempts = q.correctAttempts + :correctIncrement WHERE q.id = :questionId")
    int updateQuestionStatistics(@Param("questionId") Long questionId, @Param("correctIncrement") Integer correctIncrement);

    /**
     * 批量更新审核状态
     */
    @Modifying
    @Query("UPDATE Question q SET q.reviewStatus = :status, q.reviewerId = :reviewerId, " +
           "q.reviewComment = :comment WHERE q.id IN :questionIds")
    int batchUpdateReviewStatus(@Param("questionIds") List<Long> questionIds,
                               @Param("status") ReviewStatus status,
                               @Param("reviewerId") Long reviewerId,
                               @Param("comment") String comment);

    /**
     * 统计各科目题目数量
     */
    @Query("SELECT q.subjectCode, COUNT(q) FROM Question q WHERE q.reviewStatus = 'APPROVED' GROUP BY q.subjectCode")
    List<Object[]> countQuestionsBySubject();

    /**
     * 统计各难度题目数量
     */
    @Query("SELECT q.difficulty, COUNT(q) FROM Question q WHERE q.reviewStatus = 'APPROVED' GROUP BY q.difficulty")
    List<Object[]> countQuestionsByDifficulty();

    /**
     * 统计各类型题目数量
     */
    @Query("SELECT q.type, COUNT(q) FROM Question q WHERE q.reviewStatus = 'APPROVED' GROUP BY q.type")
    List<Object[]> countQuestionsByType();

    /**
     * 全文搜索题目
     */
    @Query("SELECT q FROM Question q WHERE q.reviewStatus = 'APPROVED' AND " +
           "(q.content LIKE %:keyword% OR q.explanation LIKE %:keyword%)")
    Page<Question> searchQuestions(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 根据科目搜索题目
     */
    @Query("SELECT q FROM Question q WHERE q.subjectCode = :subjectCode AND q.reviewStatus = 'APPROVED' AND " +
           "(q.content LIKE %:keyword% OR q.explanation LIKE %:keyword%)")
    Page<Question> searchQuestionsBySubject(@Param("subjectCode") String subjectCode, 
                                          @Param("keyword") String keyword, 
                                          Pageable pageable);

    /**
     * 检查题目内容是否重复
     */
    @Query("SELECT COUNT(q) > 0 FROM Question q WHERE q.content = :content AND q.subjectCode = :subjectCode")
    boolean existsByContentAndSubjectCode(@Param("content") String content, @Param("subjectCode") String subjectCode);

}
