package com.chiron.smartlearnsystem.exercise.dto.request;

import com.chiron.smartlearnsystem.common.core.enums.Difficulty;
import com.chiron.smartlearnsystem.exercise.enums.QuestionType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Size;
import java.util.Set;

/**
 * 题目更新请求
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuestionUpdateRequest {

    /**
     * 题目内容
     */
    @Size(max = 5000, message = "题目内容长度不能超过5000个字符")
    private String content;

    /**
     * 题目类型
     */
    private QuestionType type;

    /**
     * 难度等级
     */
    private Difficulty difficulty;

    /**
     * 题目选项（JSON格式）
     */
    @Size(max = 2000, message = "题目选项长度不能超过2000个字符")
    private String options;

    /**
     * 正确答案
     */
    @Size(max = 500, message = "正确答案长度不能超过500个字符")
    private String correctAnswer;

    /**
     * 详细解析
     */
    @Size(max = 5000, message = "详细解析长度不能超过5000个字符")
    private String explanation;

    /**
     * 题目分值
     */
    @Min(value = 1, message = "题目分值不能小于1")
    @Max(value = 100, message = "题目分值不能大于100")
    private Integer score;

    /**
     * 预估答题时间（秒）
     */
    @Min(value = 10, message = "预估答题时间不能小于10秒")
    @Max(value = 3600, message = "预估答题时间不能大于3600秒")
    private Integer estimatedTime;

    /**
     * 题目标签
     */
    @Size(max = 10, message = "标签数量不能超过10个")
    private Set<String> tags;

    /**
     * 关联知识点
     */
    @Size(max = 20, message = "知识点数量不能超过20个")
    private Set<String> knowledgePoints;

}
