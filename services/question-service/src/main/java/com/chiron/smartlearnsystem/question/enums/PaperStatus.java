package com.chiron.smartlearnsystem.question.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 试卷状态枚举
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Getter
public enum PaperStatus {

    /**
     * 激活状态
     */
    ACTIVE("ACTIVE", "激活", "试卷可正常使用"),

    /**
     * 草稿状态
     */
    DRAFT("DRAFT", "草稿", "试卷正在编辑中"),

    /**
     * 待审核
     */
    PENDING_REVIEW("PENDING_REVIEW", "待审核", "试卷等待审核"),

    /**
     * 审核中
     */
    REVIEWING("REVIEWING", "审核中", "试卷正在审核"),

    /**
     * 已发布
     */
    PUBLISHED("PUBLISHED", "已发布", "试卷已正式发布"),

    /**
     * 已暂停
     */
    PAUSED("PAUSED", "已暂停", "试卷暂时停用"),

    /**
     * 已归档
     */
    ARCHIVED("ARCHIVED", "已归档", "试卷已归档"),

    /**
     * 已删除
     */
    DELETED("DELETED", "已删除", "试卷已删除");

    private final String code;
    private final String name;
    private final String description;

    PaperStatus(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    /**
     * 根据代码获取试卷状态
     */
    public static PaperStatus getByCode(String code) {
        for (PaperStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return DRAFT; // 默认返回草稿状态
    }

    /**
     * 是否可以使用
     */
    public boolean isUsable() {
        return this == ACTIVE || this == PUBLISHED;
    }

    /**
     * 是否可以编辑
     */
    public boolean isEditable() {
        return this == DRAFT || this == PENDING_REVIEW;
    }

    /**
     * 是否需要审核
     */
    public boolean needsReview() {
        return this == PENDING_REVIEW;
    }

    /**
     * 是否为最终状态
     */
    public boolean isFinalStatus() {
        return this == ARCHIVED || this == DELETED;
    }

    /**
     * 是否可以参与考试
     */
    public boolean canTakeExam() {
        return this == ACTIVE || this == PUBLISHED;
    }

}
