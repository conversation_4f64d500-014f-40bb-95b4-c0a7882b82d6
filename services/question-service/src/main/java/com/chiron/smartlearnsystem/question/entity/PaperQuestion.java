package com.chiron.smartlearnsystem.exercise.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 试卷题目关联实体
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Entity
@Table(name = "paper_questions", indexes = {
    @Index(name = "idx_paper_order", columnList = "paper_id, question_order"),
    @Index(name = "idx_question_id", columnList = "question_id")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PaperQuestion implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 试卷ID
     */
    @Column(name = "paper_id", nullable = false)
    private Long paperId;

    /**
     * 题目ID
     */
    @Column(name = "question_id", nullable = false)
    private Long questionId;

    /**
     * 题目在试卷中的顺序
     */
    @Column(name = "question_order", nullable = false)
    private Integer questionOrder;

    /**
     * 题目分值（可以覆盖题目默认分值）
     */
    @Column(name = "score")
    private Integer score;

    /**
     * 是否必答题
     */
    @Column(name = "is_required", nullable = false)
    private Boolean isRequired = true;

    /**
     * 题目分组（用于分组显示）
     */
    @Column(name = "question_group", length = 50)
    private String questionGroup;

    /**
     * 关联的试卷
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "paper_id", insertable = false, updatable = false)
    private ExamPaper examPaper;

    /**
     * 关联的题目
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "question_id", insertable = false, updatable = false)
    private Question question;

}
