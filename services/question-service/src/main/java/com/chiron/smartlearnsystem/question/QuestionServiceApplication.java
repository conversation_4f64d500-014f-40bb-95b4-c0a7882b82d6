package com.chiron.smartlearnsystem.question;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * 题库服务启动类
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@SpringBootApplication(scanBasePackages = {
    "com.chiron.smartlearnsystem.question",
    "com.chiron.smartlearnsystem.common"
})
@EnableFeignClients
public class QuestionServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(QuestionServiceApplication.class, args);
    }

}
