package com.chiron.smartlearnsystem.exercise.service;

import com.chiron.smartlearnsystem.common.core.domain.PageResult;
import com.chiron.smartlearnsystem.common.core.enums.Difficulty;
import com.chiron.smartlearnsystem.common.core.exception.BusinessException;
import com.chiron.smartlearnsystem.exercise.dto.QuestionDTO;
import com.chiron.smartlearnsystem.exercise.dto.request.QuestionCreateRequest;
import com.chiron.smartlearnsystem.exercise.dto.request.QuestionQueryRequest;
import com.chiron.smartlearnsystem.exercise.dto.request.QuestionUpdateRequest;
import com.chiron.smartlearnsystem.exercise.entity.Question;
import com.chiron.smartlearnsystem.exercise.enums.QuestionSource;
import com.chiron.smartlearnsystem.exercise.enums.QuestionType;
import com.chiron.smartlearnsystem.exercise.enums.ReviewStatus;
import com.chiron.smartlearnsystem.exercise.repository.QuestionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 题目服务
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class QuestionService {

    private final QuestionRepository questionRepository;
    private final RedisTemplate<String, Object> redisTemplate;

    private static final String QUESTION_CACHE_KEY = "question:";
    private static final String QUESTION_LIST_CACHE_KEY = "question:list:";
    private static final int CACHE_EXPIRE_HOURS = 2;

    /**
     * 创建题目
     */
    public QuestionDTO createQuestion(QuestionCreateRequest request) {
        log.info("创建题目: subject={}, type={}", request.getSubjectCode(), request.getType());

        // 检查题目内容是否重复
        if (questionRepository.existsByContentAndSubjectCode(request.getContent(), request.getSubjectCode())) {
            throw new BusinessException("题目内容已存在");
        }

        // 构建题目实体
        Question question = Question.builder()
                .content(request.getContent())
                .type(request.getType())
                .difficulty(request.getDifficulty())
                .subjectCode(request.getSubjectCode())
                .options(request.getOptions())
                .correctAnswer(request.getCorrectAnswer())
                .explanation(request.getExplanation())
                .source(request.getSource() != null ? request.getSource() : QuestionSource.PRACTICE)
                .examYear(request.getExamYear())
                .score(request.getScore() != null ? request.getScore() : 1)
                .estimatedTime(request.getEstimatedTime())
                .reviewStatus(determineInitialReviewStatus(request.getSource()))
                .build();

        // 设置知识点和标签
        if (request.getKnowledgePoints() != null) {
            question.getKnowledgePoints().addAll(request.getKnowledgePoints());
        }
        if (request.getTags() != null) {
            question.getTags().addAll(request.getTags());
        }

        // 保存题目
        Question savedQuestion = questionRepository.save(question);

        log.info("题目创建成功: questionId={}", savedQuestion.getId());

        return QuestionDTO.from(savedQuestion);
    }

    /**
     * 更新题目
     */
    public QuestionDTO updateQuestion(Long questionId, QuestionUpdateRequest request) {
        log.info("更新题目: questionId={}", questionId);

        Question question = questionRepository.findById(questionId)
                .orElseThrow(() -> new BusinessException("题目不存在"));

        // 检查是否可以编辑
        if (question.getReviewStatus() == ReviewStatus.APPROVED) {
            throw new BusinessException("已审核通过的题目不能直接修改");
        }

        // 更新题目信息
        if (request.getContent() != null) {
            question.setContent(request.getContent());
        }
        if (request.getType() != null) {
            question.setType(request.getType());
        }
        if (request.getDifficulty() != null) {
            question.setDifficulty(request.getDifficulty());
        }
        if (request.getOptions() != null) {
            question.setOptions(request.getOptions());
        }
        if (request.getCorrectAnswer() != null) {
            question.setCorrectAnswer(request.getCorrectAnswer());
        }
        if (request.getExplanation() != null) {
            question.setExplanation(request.getExplanation());
        }
        if (request.getScore() != null) {
            question.setScore(request.getScore());
        }
        if (request.getEstimatedTime() != null) {
            question.setEstimatedTime(request.getEstimatedTime());
        }

        // 更新知识点和标签
        if (request.getKnowledgePoints() != null) {
            question.getKnowledgePoints().clear();
            question.getKnowledgePoints().addAll(request.getKnowledgePoints());
        }
        if (request.getTags() != null) {
            question.getTags().clear();
            question.getTags().addAll(request.getTags());
        }

        // 重新设置为待审核状态
        question.setReviewStatus(ReviewStatus.PENDING);

        Question updatedQuestion = questionRepository.save(question);

        // 清除缓存
        clearQuestionCache(questionId);

        log.info("题目更新成功: questionId={}", questionId);

        return QuestionDTO.from(updatedQuestion);
    }

    /**
     * 获取题目详情
     */
    @Transactional(readOnly = true)
    public QuestionDTO getQuestionById(Long questionId) {
        // 先从缓存获取
        String cacheKey = QUESTION_CACHE_KEY + questionId;
        QuestionDTO cachedQuestion = (QuestionDTO) redisTemplate.opsForValue().get(cacheKey);
        if (cachedQuestion != null) {
            return cachedQuestion;
        }

        // 从数据库获取
        Question question = questionRepository.findById(questionId)
                .orElseThrow(() -> new BusinessException("题目不存在"));

        QuestionDTO questionDTO = QuestionDTO.from(question);

        // 缓存结果
        redisTemplate.opsForValue().set(cacheKey, questionDTO, CACHE_EXPIRE_HOURS, TimeUnit.HOURS);

        return questionDTO;
    }

    /**
     * 分页查询题目
     */
    @Transactional(readOnly = true)
    public PageResult<QuestionDTO> getQuestions(QuestionQueryRequest request) {
        log.info("分页查询题目: {}", request);

        // 构建分页参数
        Sort sort = Sort.by(Sort.Direction.DESC, "createdAt");
        Pageable pageable = PageRequest.of(request.getPage() - 1, request.getSize(), sort);

        // 查询题目
        Page<Question> questionPage = questionRepository.findAll(pageable);

        // 转换为DTO
        List<QuestionDTO> questionDTOs = questionPage.getContent().stream()
                .map(QuestionDTO::from)
                .collect(Collectors.toList());

        return PageResult.of(questionDTOs, questionPage.getTotalElements(), 
                           (long) request.getPage(), (long) request.getSize());
    }

    /**
     * 根据条件随机获取题目
     */
    @Transactional(readOnly = true)
    public List<QuestionDTO> getRandomQuestions(String subjectCode, Difficulty difficulty, 
                                              QuestionType type, Integer count) {
        log.info("随机获取题目: subject={}, difficulty={}, type={}, count={}", 
                subjectCode, difficulty, type, count);

        List<Question> questions;
        if (difficulty != null && type != null) {
            questions = questionRepository.findRandomQuestionsByCondition(
                    subjectCode, difficulty.name(), type.name(), count);
        } else {
            questions = questionRepository.findRandomQuestions(subjectCode, count);
        }

        return questions.stream()
                .map(QuestionDTO::from)
                .collect(Collectors.toList());
    }

    /**
     * 审核题目
     */
    public void reviewQuestion(Long questionId, ReviewStatus status, Long reviewerId, String comment) {
        log.info("审核题目: questionId={}, status={}, reviewerId={}", questionId, status, reviewerId);

        Question question = questionRepository.findById(questionId)
                .orElseThrow(() -> new BusinessException("题目不存在"));

        question.setReviewStatus(status);
        question.setReviewerId(reviewerId);
        question.setReviewComment(comment);

        questionRepository.save(question);

        // 清除缓存
        clearQuestionCache(questionId);

        log.info("题目审核完成: questionId={}, status={}", questionId, status);
    }

    /**
     * 记录答题统计
     */
    public void recordAnswerStatistics(Long questionId, boolean isCorrect) {
        log.debug("记录答题统计: questionId={}, isCorrect={}", questionId, isCorrect);

        int correctIncrement = isCorrect ? 1 : 0;
        questionRepository.updateQuestionStatistics(questionId, correctIncrement);

        // 清除缓存
        clearQuestionCache(questionId);
    }

    /**
     * 获取热门题目
     */
    @Transactional(readOnly = true)
    public List<QuestionDTO> getPopularQuestions(String subjectCode, Integer limit) {
        Pageable pageable = PageRequest.of(0, limit);
        List<Question> questions = questionRepository.findPopularQuestions(10, pageable);

        return questions.stream()
                .filter(q -> subjectCode == null || subjectCode.equals(q.getSubjectCode()))
                .map(QuestionDTO::from)
                .collect(Collectors.toList());
    }

    /**
     * 获取困难题目
     */
    @Transactional(readOnly = true)
    public List<QuestionDTO> getDifficultQuestions(String subjectCode, Integer limit) {
        Pageable pageable = PageRequest.of(0, limit);
        List<Question> questions = questionRepository.findDifficultQuestions(
                10, BigDecimal.valueOf(0.5), pageable);

        return questions.stream()
                .filter(q -> subjectCode == null || subjectCode.equals(q.getSubjectCode()))
                .map(QuestionDTO::from)
                .collect(Collectors.toList());
    }

    /**
     * 搜索题目
     */
    @Transactional(readOnly = true)
    public PageResult<QuestionDTO> searchQuestions(String keyword, String subjectCode, 
                                                 Integer page, Integer size) {
        log.info("搜索题目: keyword={}, subject={}", keyword, subjectCode);

        Pageable pageable = PageRequest.of(page - 1, size);
        Page<Question> questionPage;

        if (subjectCode != null) {
            questionPage = questionRepository.searchQuestionsBySubject(subjectCode, keyword, pageable);
        } else {
            questionPage = questionRepository.searchQuestions(keyword, pageable);
        }

        List<QuestionDTO> questionDTOs = questionPage.getContent().stream()
                .map(QuestionDTO::from)
                .collect(Collectors.toList());

        return PageResult.of(questionDTOs, questionPage.getTotalElements(), 
                           (long) page, (long) size);
    }

    /**
     * 确定初始审核状态
     */
    private ReviewStatus determineInitialReviewStatus(QuestionSource source) {
        if (source != null && source.needsReview()) {
            return ReviewStatus.PENDING;
        }
        return ReviewStatus.APPROVED;
    }

    /**
     * 清除题目缓存
     */
    private void clearQuestionCache(Long questionId) {
        String cacheKey = QUESTION_CACHE_KEY + questionId;
        redisTemplate.delete(cacheKey);
        
        // 清除相关列表缓存
        redisTemplate.delete(QUESTION_LIST_CACHE_KEY + "*");
    }

}
