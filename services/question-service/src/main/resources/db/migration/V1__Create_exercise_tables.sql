-- 题目表
CREATE TABLE questions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    content TEXT NOT NULL COMMENT '题目内容',
    type VARCHAR(20) NOT NULL COMMENT '题目类型',
    difficulty VARCHAR(20) NOT NULL COMMENT '难度等级',
    subject_code VARCHAR(20) NOT NULL COMMENT '科目代码',
    options JSON COMMENT '题目选项',
    correct_answer VARCHAR(500) NOT NULL COMMENT '正确答案',
    explanation TEXT COMMENT '详细解析',
    source VARCHAR(20) NOT NULL DEFAULT 'PRACTICE' COMMENT '题目来源',
    exam_year INT COMMENT '所属年份',
    question_number INT COMMENT '题目序号',
    total_attempts INT NOT NULL DEFAULT 0 COMMENT '总答题次数',
    correct_attempts INT NOT NULL DEFAULT 0 COMMENT '正确答题次数',
    review_status VARCHAR(20) NOT NULL DEFAULT 'PENDING' COMMENT '审核状态',
    reviewer_id BIGINT COMMENT '审核人ID',
    review_comment TEXT COMMENT '审核意见',
    score INT NOT NULL DEFAULT 1 COMMENT '题目分值',
    estimated_time INT COMMENT '预估答题时间（秒）',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT NOT NULL DEFAULT 0,
    version BIGINT NOT NULL DEFAULT 0,
    
    INDEX idx_subject_difficulty (subject_code, difficulty),
    INDEX idx_source_year (source, exam_year),
    INDEX idx_review_status (review_status),
    INDEX idx_question_type (type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='题目表';

-- 题目标签表
CREATE TABLE question_tags (
    question_id BIGINT NOT NULL,
    tag VARCHAR(50) NOT NULL,
    PRIMARY KEY (question_id, tag),
    FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='题目标签表';

-- 题目知识点表
CREATE TABLE question_knowledge_points (
    question_id BIGINT NOT NULL,
    knowledge_point VARCHAR(100) NOT NULL,
    PRIMARY KEY (question_id, knowledge_point),
    FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='题目知识点表';

-- 试卷表
CREATE TABLE exam_papers (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL COMMENT '试卷标题',
    description TEXT COMMENT '试卷描述',
    subject_code VARCHAR(20) NOT NULL COMMENT '科目代码',
    paper_type VARCHAR(20) NOT NULL COMMENT '试卷类型',
    duration_minutes INT NOT NULL COMMENT '考试时长（分钟）',
    total_score INT NOT NULL COMMENT '总分',
    passing_score INT NOT NULL COMMENT '及格分数',
    question_config JSON COMMENT '题目配置信息',
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '试卷状态',
    created_by BIGINT COMMENT '创建人ID',
    exam_year INT COMMENT '试卷年份',
    exam_season VARCHAR(20) COMMENT '试卷季度',
    difficulty_distribution TEXT COMMENT '难度分布配置',
    knowledge_distribution TEXT COMMENT '知识点分布配置',
    allow_view_answer BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否允许查看答案',
    allow_view_explanation BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否允许查看解析',
    random_question_order BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否随机题目顺序',
    random_option_order BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否随机选项顺序',
    usage_count INT NOT NULL DEFAULT 0 COMMENT '试卷使用次数',
    average_score DOUBLE COMMENT '平均分数',
    pass_rate DOUBLE COMMENT '通过率',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT NOT NULL DEFAULT 0,
    version BIGINT NOT NULL DEFAULT 0,
    
    INDEX idx_subject_type (subject_code, paper_type),
    INDEX idx_status (status),
    INDEX idx_created_by (created_by),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='试卷表';

-- 试卷题目关联表
CREATE TABLE paper_questions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    paper_id BIGINT NOT NULL,
    question_id BIGINT NOT NULL,
    question_order INT NOT NULL COMMENT '题目在试卷中的顺序',
    score INT COMMENT '题目分值',
    is_required BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否必答题',
    question_group VARCHAR(50) COMMENT '题目分组',
    
    INDEX idx_paper_order (paper_id, question_order),
    INDEX idx_question_id (question_id),
    FOREIGN KEY (paper_id) REFERENCES exam_papers(id) ON DELETE CASCADE,
    FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='试卷题目关联表';

-- 考试记录表
CREATE TABLE exam_records (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    paper_id BIGINT NOT NULL COMMENT '试卷ID',
    session_id VARCHAR(50) NOT NULL UNIQUE COMMENT '考试会话ID',
    start_time DATETIME NOT NULL COMMENT '开始时间',
    end_time DATETIME COMMENT '结束时间',
    submit_time DATETIME COMMENT '提交时间',
    total_score INT COMMENT '试卷总分',
    user_score INT COMMENT '用户得分',
    correct_count INT NOT NULL DEFAULT 0 COMMENT '正确题目数量',
    total_questions INT NOT NULL DEFAULT 0 COMMENT '总题目数量',
    is_passed BOOLEAN COMMENT '是否通过考试',
    time_spent_minutes INT COMMENT '用时（分钟）',
    exam_data JSON COMMENT '考试详细数据',
    status VARCHAR(20) NOT NULL DEFAULT 'IN_PROGRESS' COMMENT '考试状态',
    accuracy_rate DECIMAL(5,4) COMMENT '正确率',
    ranking INT COMMENT '排名',
    beat_percentage DECIMAL(5,2) COMMENT '超越百分比',
    exam_environment TEXT COMMENT '考试环境信息',
    ip_address VARCHAR(50) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT NOT NULL DEFAULT 0,
    version BIGINT NOT NULL DEFAULT 0,
    
    INDEX idx_user_paper (user_id, paper_id),
    INDEX idx_session (session_id),
    INDEX idx_status_time (status, start_time),
    INDEX idx_score (user_score),
    INDEX idx_start_time (start_time),
    FOREIGN KEY (paper_id) REFERENCES exam_papers(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='考试记录表';

-- 答题记录表
CREATE TABLE answer_records (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    question_id BIGINT NOT NULL COMMENT '题目ID',
    exam_record_id BIGINT COMMENT '考试记录ID',
    user_answer TEXT COMMENT '用户答案',
    correct_answer TEXT COMMENT '正确答案',
    is_correct BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否正确',
    answer_time DATETIME NOT NULL COMMENT '答题时间',
    time_spent_seconds INT COMMENT '用时（秒）',
    score INT DEFAULT 0 COMMENT '得分',
    question_score INT COMMENT '题目分值',
    answer_source VARCHAR(20) DEFAULT 'PRACTICE' COMMENT '答题来源',
    subject_code VARCHAR(20) COMMENT '科目代码',
    knowledge_points TEXT COMMENT '知识点',
    difficulty VARCHAR(20) COMMENT '题目难度',
    is_marked BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否标记',
    user_note TEXT COMMENT '用户备注',
    ip_address VARCHAR(50) COMMENT 'IP地址',
    device_info TEXT COMMENT '设备信息',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT NOT NULL DEFAULT 0,
    version BIGINT NOT NULL DEFAULT 0,
    
    INDEX idx_user_question (user_id, question_id),
    INDEX idx_exam_record (exam_record_id),
    INDEX idx_question_id (question_id),
    INDEX idx_answer_time (answer_time),
    INDEX idx_is_correct (is_correct),
    FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE,
    FOREIGN KEY (exam_record_id) REFERENCES exam_records(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='答题记录表';
