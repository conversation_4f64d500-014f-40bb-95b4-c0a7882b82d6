server:
  port: 8082

spring:
  application:
    name: question-service
  
  profiles:
    active: dev
  
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *************************************************************************************************************************************************************
    username: root
    password: 123456
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: QuestionServiceHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
  
  redis:
    host: localhost
    port: 6379
    password: 
    database: 2
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0
  
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
    validate-on-migrate: true

eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
    register-with-eureka: true
    fetch-registry: true
  instance:
    prefer-ip-address: true
    lease-renewal-interval-in-seconds: 30
    lease-expiration-duration-in-seconds: 90

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true

logging:
  level:
    com.chiron.smartlearnsystem.question: INFO
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/question-service.log

# 自定义配置
smartlearn:
  question:
    # 题目配置
    question:
      max-options: 10          # 最大选项数
      max-content-length: 5000 # 最大内容长度
      default-difficulty: "MEDIUM" # 默认难度
      auto-tag: true           # 自动标签
    
    # 考试配置
    exam:
      max-duration: 180        # 最大考试时长（分钟）
      auto-submit: true        # 自动提交
      shuffle-questions: true  # 随机题目顺序
      shuffle-options: true    # 随机选项顺序
      max-attempts: 3          # 最大尝试次数
    
    # 缓存配置
    cache:
      question-expire-minutes: 30
      exam-expire-minutes: 180
      result-expire-hours: 24
    
    # 文件上传配置
    upload:
      max-file-size: 10MB
      allowed-types: "jpg,jpeg,png,gif,pdf,doc,docx"
      upload-path: "/data/question-files"
