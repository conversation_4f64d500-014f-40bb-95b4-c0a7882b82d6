server:
  port: 8081

spring:
  application:
    name: user-service
  profiles:
    active: dev
  
  # 数据库配置
  datasource:
    url: ****************************************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
  
  # Redis配置
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0
    timeout: 5000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms
  
  # Kafka配置
  kafka:
    bootstrap-servers: localhost:9092
    consumer:
      group-id: user-service-group
      auto-offset-reset: earliest
    producer:
      retries: 3
      batch-size: 16384

# Eureka配置
eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
  instance:
    prefer-ip-address: true
    lease-renewal-interval-in-seconds: 10
    lease-expiration-duration-in-seconds: 30

# JWT配置
jwt:
  secret: smartlearn-user-service-jwt-secret-key-2024
  expiration: 86400
  refresh-expiration: 604800

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# 日志配置
logging:
  level:
    com.chiron.smartlearnsystem: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE

---
spring:
  config:
    activate:
      on-profile: test

  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true

---
spring:
  config:
    activate:
      on-profile: prod

  datasource:
    url: *******************************************************************************************************************************
    username: ${DB_USERNAME:smartlearn}
    password: ${DB_PASSWORD:smartlearn123}
  
  redis:
    host: redis-server
    port: 6379
    password: ${REDIS_PASSWORD:}
  
  kafka:
    bootstrap-servers: kafka-server:9092

eureka:
  client:
    service-url:
      defaultZone: http://eureka-server:8761/eureka/

logging:
  level:
    root: WARN
    com.chiron.smartlearnsystem: INFO
