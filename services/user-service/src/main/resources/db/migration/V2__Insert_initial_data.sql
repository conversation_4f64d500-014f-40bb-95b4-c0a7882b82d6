-- 插入默认角色
INSERT INTO roles (name, code, description, enabled, sort_order) VALUES
('超级管理员', 'ROLE_SUPER_ADMIN', '系统超级管理员，拥有所有权限', TRUE, 1),
('管理员', 'ROLE_ADMIN', '系统管理员，拥有大部分管理权限', TRUE, 2),
('教师', 'ROLE_TEACHER', '教师角色，可以管理课程和题目', TRUE, 3),
('学生', 'ROLE_USER', '普通学生用户', TRUE, 4);

-- 插入权限数据
INSERT INTO permissions (name, code, type, description, parent_id, path, icon, sort_order, enabled) VALUES
-- 系统管理
('系统管理', 'SYSTEM_MANAGE', 'MENU', '系统管理菜单', NULL, '/system', 'system', 1, TRUE),
('用户管理', 'USER_MANAGE', 'MENU', '用户管理菜单', 1, '/system/users', 'user', 1, TRUE),
('角色管理', 'ROLE_MANAGE', 'MENU', '角色管理菜单', 1, '/system/roles', 'role', 2, TRUE),
('权限管理', 'PERMISSION_MANAGE', 'MENU', '权限管理菜单', 1, '/system/permissions', 'permission', 3, TRUE),

-- 用户管理权限
('查看用户', 'USER_VIEW', 'API', '查看用户信息', 2, NULL, NULL, 1, TRUE),
('创建用户', 'USER_CREATE', 'API', '创建新用户', 2, NULL, NULL, 2, TRUE),
('编辑用户', 'USER_EDIT', 'API', '编辑用户信息', 2, NULL, NULL, 3, TRUE),
('删除用户', 'USER_DELETE', 'API', '删除用户', 2, NULL, NULL, 4, TRUE),
('重置密码', 'USER_RESET_PASSWORD', 'API', '重置用户密码', 2, NULL, NULL, 5, TRUE),

-- 角色管理权限
('查看角色', 'ROLE_VIEW', 'API', '查看角色信息', 3, NULL, NULL, 1, TRUE),
('创建角色', 'ROLE_CREATE', 'API', '创建新角色', 3, NULL, NULL, 2, TRUE),
('编辑角色', 'ROLE_EDIT', 'API', '编辑角色信息', 3, NULL, NULL, 3, TRUE),
('删除角色', 'ROLE_DELETE', 'API', '删除角色', 3, NULL, NULL, 4, TRUE),
('分配权限', 'ROLE_ASSIGN_PERMISSION', 'API', '为角色分配权限', 3, NULL, NULL, 5, TRUE),

-- 权限管理权限
('查看权限', 'PERMISSION_VIEW', 'API', '查看权限信息', 4, NULL, NULL, 1, TRUE),
('创建权限', 'PERMISSION_CREATE', 'API', '创建新权限', 4, NULL, NULL, 2, TRUE),
('编辑权限', 'PERMISSION_EDIT', 'API', '编辑权限信息', 4, NULL, NULL, 3, TRUE),
('删除权限', 'PERMISSION_DELETE', 'API', '删除权限', 4, NULL, NULL, 4, TRUE),

-- 学习管理
('学习管理', 'STUDY_MANAGE', 'MENU', '学习管理菜单', NULL, '/study', 'study', 2, TRUE),
('我的学习', 'MY_STUDY', 'MENU', '我的学习', 18, '/study/my', 'my-study', 1, TRUE),
('学习计划', 'STUDY_PLAN', 'MENU', '学习计划', 18, '/study/plan', 'plan', 2, TRUE),
('学习记录', 'STUDY_RECORD', 'MENU', '学习记录', 18, '/study/record', 'record', 3, TRUE),

-- 题库管理
('题库管理', 'EXERCISE_MANAGE', 'MENU', '题库管理菜单', NULL, '/exercise', 'exercise', 3, TRUE),
('题目练习', 'EXERCISE_PRACTICE', 'MENU', '题目练习', 22, '/exercise/practice', 'practice', 1, TRUE),
('模拟考试', 'EXERCISE_EXAM', 'MENU', '模拟考试', 22, '/exercise/exam', 'exam', 2, TRUE),
('错题本', 'EXERCISE_WRONG', 'MENU', '错题本', 22, '/exercise/wrong', 'wrong', 3, TRUE),

-- 基础权限
('个人中心', 'PROFILE_VIEW', 'MENU', '个人中心', NULL, '/profile', 'profile', 10, TRUE),
('修改密码', 'PROFILE_CHANGE_PASSWORD', 'API', '修改个人密码', 26, NULL, NULL, 1, TRUE),
('更新资料', 'PROFILE_UPDATE', 'API', '更新个人资料', 26, NULL, NULL, 2, TRUE);

-- 为超级管理员分配所有权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT 1, id FROM permissions WHERE enabled = TRUE;

-- 为管理员分配部分权限（除了超级管理员专有权限）
INSERT INTO role_permissions (role_id, permission_id)
SELECT 2, id FROM permissions 
WHERE enabled = TRUE 
AND code NOT IN ('PERMISSION_CREATE', 'PERMISSION_EDIT', 'PERMISSION_DELETE');

-- 为教师分配教学相关权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT 3, id FROM permissions 
WHERE enabled = TRUE 
AND code IN ('STUDY_MANAGE', 'MY_STUDY', 'STUDY_PLAN', 'STUDY_RECORD', 
             'EXERCISE_MANAGE', 'EXERCISE_PRACTICE', 'EXERCISE_EXAM', 'EXERCISE_WRONG',
             'PROFILE_VIEW', 'PROFILE_CHANGE_PASSWORD', 'PROFILE_UPDATE');

-- 为普通用户分配基础权限
INSERT INTO role_permissions (role_id, permission_id)
SELECT 4, id FROM permissions 
WHERE enabled = TRUE 
AND code IN ('MY_STUDY', 'STUDY_PLAN', 'STUDY_RECORD',
             'EXERCISE_PRACTICE', 'EXERCISE_EXAM', 'EXERCISE_WRONG',
             'PROFILE_VIEW', 'PROFILE_CHANGE_PASSWORD', 'PROFILE_UPDATE');

-- 创建默认管理员用户
-- 密码: admin123 (BCrypt加密后的值)
INSERT INTO users (username, email, password, real_name, status) VALUES
('admin', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBaLIU8pjrsZSu', '系统管理员', 'ACTIVE');

-- 为管理员用户分配超级管理员角色
INSERT INTO user_roles (user_id, role_id) VALUES (1, 1);

-- 为管理员用户创建默认偏好设置
INSERT INTO user_preferences (user_id, daily_study_goal, study_reminder_enabled, reminder_time, 
                              preferred_difficulty, learning_mode, ai_recommendation_enabled, 
                              auto_review_enabled, auto_adjust_plan) 
VALUES (1, 120, TRUE, '09:00:00', 'HARD', 'SYSTEMATIC', TRUE, TRUE, TRUE);
