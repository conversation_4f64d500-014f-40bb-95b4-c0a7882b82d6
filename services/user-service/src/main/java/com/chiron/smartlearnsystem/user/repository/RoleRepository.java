package com.chiron.smartlearnsystem.user.repository;

import com.chiron.smartlearnsystem.user.entity.Role;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * 角色数据访问层
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Repository
public interface RoleRepository extends JpaRepository<Role, Long> {

    /**
     * 根据角色名称查找角色
     */
    Optional<Role> findByName(String name);

    /**
     * 根据角色编码查找角色
     */
    Optional<Role> findByCode(String code);

    /**
     * 根据角色名称或编码查找角色
     */
    Optional<Role> findByNameOrCode(String name, String code);

    /**
     * 检查角色名称是否存在
     */
    boolean existsByName(String name);

    /**
     * 检查角色编码是否存在
     */
    boolean existsByCode(String code);

    /**
     * 查找启用的角色
     */
    List<Role> findByEnabledTrue();

    /**
     * 查找禁用的角色
     */
    List<Role> findByEnabledFalse();

    /**
     * 根据启用状态查找角色
     */
    List<Role> findByEnabled(Boolean enabled);

    /**
     * 根据角色编码列表查找角色
     */
    List<Role> findByCodeIn(Set<String> codes);

    /**
     * 根据角色名称列表查找角色
     */
    List<Role> findByNameIn(Set<String> names);

    /**
     * 按排序字段排序查找所有角色
     */
    List<Role> findAllByOrderBySortOrderAsc();

    /**
     * 按排序字段排序查找启用的角色
     */
    List<Role> findByEnabledTrueOrderBySortOrderAsc();

    /**
     * 根据用户ID查找角色
     */
    @Query("SELECT r FROM Role r JOIN r.users u WHERE u.id = :userId")
    List<Role> findByUserId(@Param("userId") Long userId);

    /**
     * 根据权限编码查找角色
     */
    @Query("SELECT DISTINCT r FROM Role r JOIN r.permissions p WHERE p.code = :permissionCode")
    List<Role> findByPermissionCode(@Param("permissionCode") String permissionCode);

    /**
     * 查找拥有特定权限的启用角色
     */
    @Query("SELECT DISTINCT r FROM Role r JOIN r.permissions p WHERE p.code = :permissionCode AND r.enabled = true")
    List<Role> findEnabledRolesByPermissionCode(@Param("permissionCode") String permissionCode);

    /**
     * 统计角色总数
     */
    @Query("SELECT COUNT(r) FROM Role r WHERE r.deleted = 0")
    long countActiveRoles();

    /**
     * 统计启用的角色数量
     */
    long countByEnabledTrue();

    /**
     * 统计禁用的角色数量
     */
    long countByEnabledFalse();

    /**
     * 查找角色名称包含关键字的角色
     */
    List<Role> findByNameContainingIgnoreCase(String keyword);

    /**
     * 查找角色描述包含关键字的角色
     */
    List<Role> findByDescriptionContainingIgnoreCase(String keyword);

    /**
     * 搜索角色（名称、编码、描述）
     */
    @Query("SELECT r FROM Role r WHERE " +
           "r.name LIKE %:keyword% OR " +
           "r.code LIKE %:keyword% OR " +
           "r.description LIKE %:keyword%")
    List<Role> searchRoles(@Param("keyword") String keyword);

    /**
     * 查找没有用户的角色
     */
    @Query("SELECT r FROM Role r WHERE r.users IS EMPTY")
    List<Role> findRolesWithoutUsers();

    /**
     * 查找没有权限的角色
     */
    @Query("SELECT r FROM Role r WHERE r.permissions IS EMPTY")
    List<Role> findRolesWithoutPermissions();

}
