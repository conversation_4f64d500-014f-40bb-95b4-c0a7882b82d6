package com.chiron.smartlearnsystem.user.dto;

import com.chiron.smartlearnsystem.common.core.enums.Difficulty;
import com.chiron.smartlearnsystem.user.entity.UserPreference;
import com.chiron.smartlearnsystem.user.enums.LearningMode;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Set;

/**
 * 用户偏好设置DTO
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserPreferenceDTO {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 学习目标科目
     */
    private Set<String> targetSubjects;

    /**
     * 每日学习时长目标（分钟）
     */
    private Integer dailyStudyGoal;

    /**
     * 学习提醒设置
     */
    private Boolean studyReminderEnabled;

    /**
     * 提醒时间
     */
    @JsonFormat(pattern = "HH:mm:ss")
    private LocalTime reminderTime;

    /**
     * 难度偏好
     */
    private Difficulty preferredDifficulty;

    /**
     * 学习模式偏好
     */
    private LearningMode learningMode;

    /**
     * 是否启用AI推荐
     */
    private Boolean aiRecommendationEnabled;

    /**
     * 是否启用错题自动复习
     */
    private Boolean autoReviewEnabled;

    /**
     * 学习计划自动调整
     */
    private Boolean autoAdjustPlan;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 从UserPreference实体转换为DTO
     */
    public static UserPreferenceDTO from(UserPreference preference) {
        if (preference == null) {
            return null;
        }

        return UserPreferenceDTO.builder()
                .userId(preference.getUserId())
                .targetSubjects(preference.getTargetSubjects())
                .dailyStudyGoal(preference.getDailyStudyGoal())
                .studyReminderEnabled(preference.getStudyReminderEnabled())
                .reminderTime(preference.getReminderTime())
                .preferredDifficulty(preference.getPreferredDifficulty())
                .learningMode(preference.getLearningMode())
                .aiRecommendationEnabled(preference.getAiRecommendationEnabled())
                .autoReviewEnabled(preference.getAutoReviewEnabled())
                .autoAdjustPlan(preference.getAutoAdjustPlan())
                .createdAt(preference.getCreatedAt())
                .updatedAt(preference.getUpdatedAt())
                .build();
    }

}
