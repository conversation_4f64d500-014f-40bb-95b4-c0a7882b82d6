package com.chiron.smartlearnsystem.user.entity;

import com.chiron.smartlearnsystem.common.core.enums.Difficulty;
import com.chiron.smartlearnsystem.user.enums.LearningMode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.HashSet;
import java.util.Set;

/**
 * 用户学习偏好设置实体
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Entity
@Table(name = "user_preferences")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserPreference {

    /**
     * 用户ID（主键）
     */
    @Id
    private Long userId;

    /**
     * 关联用户
     */
    @OneToOne
    @MapsId
    @JoinColumn(name = "user_id")
    private User user;

    /**
     * 学习目标科目
     */
    @ElementCollection
    @CollectionTable(
        name = "user_target_subjects",
        joinColumns = @JoinColumn(name = "user_id")
    )
    @Column(name = "subject_code")
    private Set<String> targetSubjects = new HashSet<>();

    /**
     * 每日学习时长目标（分钟）
     */
    @Column(name = "daily_study_goal", nullable = false)
    private Integer dailyStudyGoal = 60;

    /**
     * 学习提醒设置
     */
    @Column(name = "study_reminder_enabled", nullable = false)
    private Boolean studyReminderEnabled = true;

    /**
     * 提醒时间
     */
    @Column(name = "reminder_time")
    private LocalTime reminderTime = LocalTime.of(20, 0);

    /**
     * 难度偏好
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "preferred_difficulty", nullable = false)
    private Difficulty preferredDifficulty = Difficulty.MEDIUM;

    /**
     * 学习模式偏好
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "learning_mode", nullable = false)
    private LearningMode learningMode = LearningMode.SYSTEMATIC;

    /**
     * 是否启用AI推荐
     */
    @Column(name = "ai_recommendation_enabled", nullable = false)
    private Boolean aiRecommendationEnabled = true;

    /**
     * 是否启用错题自动复习
     */
    @Column(name = "auto_review_enabled", nullable = false)
    private Boolean autoReviewEnabled = true;

    /**
     * 学习计划自动调整
     */
    @Column(name = "auto_adjust_plan", nullable = false)
    private Boolean autoAdjustPlan = true;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    /**
     * 添加目标科目
     */
    public void addTargetSubject(String subjectCode) {
        this.targetSubjects.add(subjectCode);
    }

    /**
     * 移除目标科目
     */
    public void removeTargetSubject(String subjectCode) {
        this.targetSubjects.remove(subjectCode);
    }

    /**
     * 检查是否包含目标科目
     */
    public boolean hasTargetSubject(String subjectCode) {
        return this.targetSubjects.contains(subjectCode);
    }

}
