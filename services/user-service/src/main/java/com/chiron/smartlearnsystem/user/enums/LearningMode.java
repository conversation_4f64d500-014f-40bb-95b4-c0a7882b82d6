package com.chiron.smartlearnsystem.user.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 学习模式枚举
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum LearningMode {

    /**
     * 系统化学习 - 按照知识体系顺序学习
     */
    SYSTEMATIC("SYSTEMATIC", "系统化学习", "按照知识体系和章节顺序进行学习"),

    /**
     * 随机学习 - 随机选择题目和知识点
     */
    RANDOM("RANDOM", "随机学习", "随机选择题目和知识点进行学习"),

    /**
     * 薄弱点重点学习 - 重点攻克薄弱知识点
     */
    WEAK_POINT_FOCUSED("WEAK_POINT_FOCUSED", "薄弱点重点学习", "重点攻克个人薄弱知识点"),

    /**
     * 考前冲刺 - 针对考试的强化训练
     */
    EXAM_SPRINT("EXAM_SPRINT", "考前冲刺", "针对即将到来的考试进行强化训练"),

    /**
     * 自适应学习 - AI智能调整学习内容
     */
    ADAPTIVE("ADAPTIVE", "自适应学习", "基于AI分析自动调整学习内容和难度");

    private final String code;
    private final String name;
    private final String description;

    /**
     * 根据代码获取学习模式
     */
    public static LearningMode getByCode(String code) {
        for (LearningMode mode : values()) {
            if (mode.getCode().equals(code)) {
                return mode;
            }
        }
        return SYSTEMATIC; // 默认返回系统化学习
    }

}
