package com.chiron.smartlearnsystem.user.entity;

import com.chiron.smartlearnsystem.common.database.entity.BaseEntity;
import com.chiron.smartlearnsystem.user.enums.PermissionType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import java.util.HashSet;
import java.util.Set;

/**
 * 权限实体
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Entity
@Table(name = "permissions", indexes = {
    @Index(name = "idx_permission_code", columnList = "code"),
    @Index(name = "idx_permission_type", columnList = "type"),
    @Index(name = "idx_permission_parent", columnList = "parent_id")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Permission extends BaseEntity {

    /**
     * 权限名称
     */
    @Column(nullable = false, length = 100)
    private String name;

    /**
     * 权限编码
     */
    @Column(unique = true, nullable = false, length = 100)
    private String code;

    /**
     * 权限类型
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private PermissionType type;

    /**
     * 权限描述
     */
    @Column(length = 200)
    private String description;

    /**
     * 父权限ID
     */
    @Column(name = "parent_id")
    private Long parentId;

    /**
     * 权限路径
     */
    @Column(length = 500)
    private String path;

    /**
     * 图标
     */
    @Column(length = 100)
    private String icon;

    /**
     * 排序
     */
    @Column(name = "sort_order")
    private Integer sortOrder = 0;

    /**
     * 是否启用
     */
    @Column(nullable = false)
    private Boolean enabled = true;

    /**
     * 关联角色
     */
    @ManyToMany(mappedBy = "permissions", fetch = FetchType.LAZY)
    private Set<Role> roles = new HashSet<>();

    /**
     * 子权限
     */
    @OneToMany(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_id")
    private Set<Permission> children = new HashSet<>();

    /**
     * 是否为菜单权限
     */
    public boolean isMenu() {
        return PermissionType.MENU.equals(this.type);
    }

    /**
     * 是否为按钮权限
     */
    public boolean isButton() {
        return PermissionType.BUTTON.equals(this.type);
    }

    /**
     * 是否为API权限
     */
    public boolean isApi() {
        return PermissionType.API.equals(this.type);
    }

}
