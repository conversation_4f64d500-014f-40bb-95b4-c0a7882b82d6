package com.chiron.smartlearnsystem.user.dto;

import com.chiron.smartlearnsystem.user.entity.Permission;
import com.chiron.smartlearnsystem.user.enums.PermissionType;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 权限DTO
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PermissionDTO {

    /**
     * 权限ID
     */
    private Long id;

    /**
     * 权限名称
     */
    private String name;

    /**
     * 权限编码
     */
    private String code;

    /**
     * 权限类型
     */
    private PermissionType type;

    /**
     * 权限描述
     */
    private String description;

    /**
     * 父权限ID
     */
    private Long parentId;

    /**
     * 权限路径
     */
    private String path;

    /**
     * 图标
     */
    private String icon;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 子权限
     */
    private Set<PermissionDTO> children;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 从Permission实体转换为DTO
     */
    public static PermissionDTO from(Permission permission) {
        if (permission == null) {
            return null;
        }

        return PermissionDTO.builder()
                .id(permission.getId())
                .name(permission.getName())
                .code(permission.getCode())
                .type(permission.getType())
                .description(permission.getDescription())
                .parentId(permission.getParentId())
                .path(permission.getPath())
                .icon(permission.getIcon())
                .sortOrder(permission.getSortOrder())
                .enabled(permission.getEnabled())
                .children(permission.getChildren() != null ?
                         permission.getChildren().stream()
                             .map(PermissionDTO::from)
                             .collect(Collectors.toSet()) : null)
                .createdAt(permission.getCreatedAt())
                .updatedAt(permission.getUpdatedAt())
                .build();
    }

    /**
     * 从Permission实体转换为简化DTO（不包含子权限）
     */
    public static PermissionDTO fromSimple(Permission permission) {
        if (permission == null) {
            return null;
        }

        return PermissionDTO.builder()
                .id(permission.getId())
                .name(permission.getName())
                .code(permission.getCode())
                .type(permission.getType())
                .description(permission.getDescription())
                .parentId(permission.getParentId())
                .path(permission.getPath())
                .icon(permission.getIcon())
                .sortOrder(permission.getSortOrder())
                .enabled(permission.getEnabled())
                .createdAt(permission.getCreatedAt())
                .updatedAt(permission.getUpdatedAt())
                .build();
    }

}
