package com.chiron.smartlearnsystem.user.repository;

import com.chiron.smartlearnsystem.common.core.enums.Difficulty;
import com.chiron.smartlearnsystem.user.entity.UserPreference;
import com.chiron.smartlearnsystem.user.enums.LearningMode;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalTime;
import java.util.List;
import java.util.Optional;

/**
 * 用户偏好设置数据访问层
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Repository
public interface UserPreferenceRepository extends JpaRepository<UserPreference, Long> {

    /**
     * 根据用户ID查找偏好设置
     */
    Optional<UserPreference> findByUserId(Long userId);

    /**
     * 根据学习模式查找用户偏好
     */
    List<UserPreference> findByLearningMode(LearningMode learningMode);

    /**
     * 根据难度偏好查找用户偏好
     */
    List<UserPreference> findByPreferredDifficulty(Difficulty difficulty);

    /**
     * 查找启用学习提醒的用户偏好
     */
    List<UserPreference> findByStudyReminderEnabledTrue();

    /**
     * 查找启用AI推荐的用户偏好
     */
    List<UserPreference> findByAiRecommendationEnabledTrue();

    /**
     * 根据提醒时间查找用户偏好
     */
    List<UserPreference> findByReminderTime(LocalTime reminderTime);

    /**
     * 查找提醒时间在指定范围内的用户偏好
     */
    List<UserPreference> findByReminderTimeBetween(LocalTime startTime, LocalTime endTime);

    /**
     * 根据每日学习目标查找用户偏好
     */
    List<UserPreference> findByDailyStudyGoal(Integer dailyStudyGoal);

    /**
     * 查找每日学习目标大于指定值的用户偏好
     */
    List<UserPreference> findByDailyStudyGoalGreaterThan(Integer dailyStudyGoal);

    /**
     * 查找包含特定目标科目的用户偏好
     */
    @Query("SELECT up FROM UserPreference up JOIN up.targetSubjects ts WHERE ts = :subjectCode")
    List<UserPreference> findByTargetSubjectsContaining(@Param("subjectCode") String subjectCode);

    /**
     * 统计各学习模式的用户数量
     */
    @Query("SELECT up.learningMode, COUNT(up) FROM UserPreference up GROUP BY up.learningMode")
    List<Object[]> countByLearningMode();

    /**
     * 统计各难度偏好的用户数量
     */
    @Query("SELECT up.preferredDifficulty, COUNT(up) FROM UserPreference up GROUP BY up.preferredDifficulty")
    List<Object[]> countByPreferredDifficulty();

    /**
     * 统计启用各功能的用户数量
     */
    @Query("SELECT " +
           "SUM(CASE WHEN up.studyReminderEnabled = true THEN 1 ELSE 0 END) as reminderEnabled, " +
           "SUM(CASE WHEN up.aiRecommendationEnabled = true THEN 1 ELSE 0 END) as aiEnabled, " +
           "SUM(CASE WHEN up.autoReviewEnabled = true THEN 1 ELSE 0 END) as autoReviewEnabled, " +
           "SUM(CASE WHEN up.autoAdjustPlan = true THEN 1 ELSE 0 END) as autoAdjustEnabled " +
           "FROM UserPreference up")
    Object[] getFeatureUsageStatistics();

    /**
     * 查找平均每日学习目标
     */
    @Query("SELECT AVG(up.dailyStudyGoal) FROM UserPreference up")
    Double getAverageDailyStudyGoal();

    /**
     * 检查用户偏好是否存在
     */
    boolean existsByUserId(Long userId);

}
