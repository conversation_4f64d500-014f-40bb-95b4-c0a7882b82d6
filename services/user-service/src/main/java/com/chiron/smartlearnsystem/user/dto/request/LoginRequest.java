package com.chiron.smartlearnsystem.user.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 用户登录请求
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LoginRequest {

    /**
     * 用户名或邮箱
     */
    @NotBlank(message = "用户名或邮箱不能为空")
    @Size(max = 100, message = "用户名或邮箱长度不能超过100个字符")
    private String username;

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    @Size(max = 100, message = "密码长度不能超过100个字符")
    private String password;

    /**
     * 验证码
     */
    private String captcha;

    /**
     * 验证码键
     */
    private String captchaKey;

    /**
     * 是否记住我
     */
    private Boolean rememberMe = false;

    /**
     * 登录IP（由系统自动设置）
     */
    private String loginIp;

    /**
     * 登录设备信息
     */
    private String deviceInfo;

}
