package com.chiron.smartlearnsystem.user.controller;

import com.chiron.smartlearnsystem.common.core.domain.ApiResponse;
import com.chiron.smartlearnsystem.user.dto.UserDTO;
import com.chiron.smartlearnsystem.user.dto.UserPreferenceDTO;
import com.chiron.smartlearnsystem.user.dto.request.LoginRequest;
import com.chiron.smartlearnsystem.user.dto.request.UserRegistrationRequest;
import com.chiron.smartlearnsystem.user.dto.response.LoginResponse;
import com.chiron.smartlearnsystem.user.dto.response.UserRegistrationResponse;
import com.chiron.smartlearnsystem.user.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 用户控制器
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/users")
@RequiredArgsConstructor
@Validated
public class UserController {

    private final UserService userService;

    /**
     * 用户注册
     */
    @PostMapping("/register")
    public ResponseEntity<ApiResponse<UserRegistrationResponse>> register(
            @Valid @RequestBody UserRegistrationRequest request) {
        
        log.info("用户注册请求: username={}", request.getUsername());
        
        UserRegistrationResponse response = userService.register(request);
        
        return ResponseEntity.ok(ApiResponse.success("注册成功", response));
    }

    /**
     * 用户登录
     */
    @PostMapping("/login")
    public ResponseEntity<ApiResponse<LoginResponse>> login(
            @Valid @RequestBody LoginRequest request,
            HttpServletRequest httpRequest) {
        
        // 设置登录IP
        request.setLoginIp(getClientIpAddress(httpRequest));
        
        log.info("用户登录请求: username={}, ip={}", request.getUsername(), request.getLoginIp());
        
        LoginResponse response = userService.login(request);
        
        return ResponseEntity.ok(ApiResponse.success("登录成功", response));
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/profile")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<UserDTO>> getCurrentUserProfile(Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        
        log.info("获取用户信息请求: userId={}", userId);
        
        UserDTO userDTO = userService.getUserById(userId);
        
        return ResponseEntity.ok(ApiResponse.success("获取用户信息成功", userDTO));
    }

    /**
     * 获取当前用户学习偏好
     */
    @GetMapping("/preference")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<UserPreferenceDTO>> getCurrentUserPreference(Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        
        log.info("获取用户偏好请求: userId={}", userId);
        
        UserPreferenceDTO preferenceDTO = userService.getUserPreference(userId);
        
        return ResponseEntity.ok(ApiResponse.success("获取用户偏好成功", preferenceDTO));
    }

    /**
     * 用户退出登录
     */
    @PostMapping("/logout")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Void>> logout(Authentication authentication) {
        
        Long userId = getUserIdFromAuthentication(authentication);
        
        log.info("用户退出登录请求: userId={}", userId);
        
        userService.logout(userId);
        
        return ResponseEntity.ok(ApiResponse.success("退出登录成功"));
    }

    /**
     * 刷新访问令牌
     */
    @PostMapping("/refresh-token")
    public ResponseEntity<ApiResponse<LoginResponse>> refreshToken(
            @RequestParam("refreshToken") String refreshToken) {
        
        log.info("刷新令牌请求");
        
        LoginResponse response = userService.refreshToken(refreshToken);
        
        return ResponseEntity.ok(ApiResponse.success("刷新令牌成功", response));
    }

    /**
     * 检查用户名是否可用
     */
    @GetMapping("/check-username")
    public ResponseEntity<ApiResponse<Boolean>> checkUsername(@RequestParam String username) {
        
        log.info("检查用户名可用性: username={}", username);
        
        boolean available = userService.isUsernameAvailable(username);
        
        return ResponseEntity.ok(ApiResponse.success("检查完成", available));
    }

    /**
     * 检查邮箱是否可用
     */
    @GetMapping("/check-email")
    public ResponseEntity<ApiResponse<Boolean>> checkEmail(@RequestParam String email) {
        
        log.info("检查邮箱可用性: email={}", email);
        
        boolean available = userService.isEmailAvailable(email);
        
        return ResponseEntity.ok(ApiResponse.success("检查完成", available));
    }

    /**
     * 发送邮箱验证码
     */
    @PostMapping("/send-email-code")
    public ResponseEntity<ApiResponse<Void>> sendEmailVerificationCode(@RequestParam String email) {
        
        log.info("发送邮箱验证码请求: email={}", email);
        
        userService.sendEmailVerificationCode(email);
        
        return ResponseEntity.ok(ApiResponse.success("验证码发送成功"));
    }

    /**
     * 发送手机验证码
     */
    @PostMapping("/send-sms-code")
    public ResponseEntity<ApiResponse<Void>> sendSmsVerificationCode(@RequestParam String phoneNumber) {
        
        log.info("发送手机验证码请求: phoneNumber={}", phoneNumber);
        
        userService.sendSmsVerificationCode(phoneNumber);
        
        return ResponseEntity.ok(ApiResponse.success("验证码发送成功"));
    }

    /**
     * 获取图形验证码
     */
    @GetMapping("/captcha")
    public ResponseEntity<ApiResponse<Object>> getCaptcha() {
        
        log.info("获取图形验证码请求");
        
        Object captcha = userService.generateCaptcha();
        
        return ResponseEntity.ok(ApiResponse.success("获取验证码成功", captcha));
    }

    /**
     * 从认证信息中获取用户ID
     */
    private Long getUserIdFromAuthentication(Authentication authentication) {
        if (authentication == null || authentication.getPrincipal() == null) {
            throw new RuntimeException("用户未登录");
        }
        
        // 这里需要根据实际的认证实现来获取用户ID
        // 假设在JWT中存储了用户ID
        return 1L; // TODO: 实现实际的用户ID获取逻辑
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }

}
