package com.chiron.smartlearnsystem.user.service;

import com.chiron.smartlearnsystem.common.core.enums.Difficulty;
import com.chiron.smartlearnsystem.common.core.enums.UserStatus;
import com.chiron.smartlearnsystem.common.core.exception.BusinessException;
import com.chiron.smartlearnsystem.common.security.jwt.JwtTokenProvider;
import com.chiron.smartlearnsystem.user.dto.UserDTO;
import com.chiron.smartlearnsystem.user.dto.UserPreferenceDTO;
import com.chiron.smartlearnsystem.user.dto.request.LoginRequest;
import com.chiron.smartlearnsystem.user.dto.request.UserRegistrationRequest;
import com.chiron.smartlearnsystem.user.dto.response.LoginResponse;
import com.chiron.smartlearnsystem.user.dto.response.UserRegistrationResponse;
import com.chiron.smartlearnsystem.user.entity.Role;
import com.chiron.smartlearnsystem.user.entity.User;
import com.chiron.smartlearnsystem.user.entity.UserPreference;
import com.chiron.smartlearnsystem.user.enums.LearningMode;
import com.chiron.smartlearnsystem.user.repository.RoleRepository;
import com.chiron.smartlearnsystem.user.repository.UserPreferenceRepository;
import com.chiron.smartlearnsystem.user.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.HashSet;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 用户服务
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class UserService {

    private final UserRepository userRepository;
    private final UserPreferenceRepository userPreferenceRepository;
    private final RoleRepository roleRepository;
    private final PasswordEncoder passwordEncoder;
    private final JwtTokenProvider jwtTokenProvider;
    private final RedisTemplate<String, Object> redisTemplate;

    private static final String USER_SESSION_KEY = "user:session:";
    private static final String LOGIN_ATTEMPT_KEY = "login:attempt:";
    private static final int MAX_LOGIN_ATTEMPTS = 5;
    private static final int LOGIN_ATTEMPT_TIMEOUT = 30; // 分钟

    /**
     * 用户注册
     */
    public UserRegistrationResponse register(UserRegistrationRequest request) {
        log.info("用户注册请求: username={}, email={}", request.getUsername(), request.getEmail());

        // 验证密码一致性
        if (!request.isPasswordMatch()) {
            throw new BusinessException("两次输入的密码不一致");
        }

        // 验证用户协议
        if (!Boolean.TRUE.equals(request.getAgreeTerms())) {
            throw new BusinessException("请先同意用户协议");
        }

        // 验证用户名和邮箱唯一性
        validateUserUniqueness(request.getUsername(), request.getEmail(), request.getPhoneNumber());

        // 验证验证码
        validateCaptcha(request.getCaptchaKey(), request.getCaptcha());

        // 创建用户
        User user = User.builder()
                .username(request.getUsername())
                .email(request.getEmail())
                .password(passwordEncoder.encode(request.getPassword()))
                .realName(request.getRealName())
                .phoneNumber(request.getPhoneNumber())
                .status(UserStatus.ACTIVE)
                .build();

        // 设置默认角色
        Role defaultRole = roleRepository.findByCode("ROLE_USER")
                .orElseThrow(() -> new BusinessException("默认角色不存在"));
        user.addRole(defaultRole);

        // 保存用户
        User savedUser = userRepository.save(user);

        // 创建默认学习偏好
        createDefaultPreference(savedUser);

        // 发送欢迎邮件（异步）
        sendWelcomeEmailAsync(savedUser);

        log.info("用户注册成功: userId={}, username={}", savedUser.getId(), savedUser.getUsername());

        return UserRegistrationResponse.from(savedUser);
    }

    /**
     * 用户登录
     */
    public LoginResponse login(LoginRequest request) {
        log.info("用户登录请求: username={}", request.getUsername());

        // 检查登录尝试次数
        checkLoginAttempts(request.getUsername());

        // 验证验证码（如果需要）
        if (needCaptchaForLogin(request.getUsername())) {
            validateCaptcha(request.getCaptchaKey(), request.getCaptcha());
        }

        // 查找用户
        User user = userRepository.findByUsernameOrEmail(request.getUsername(), request.getUsername())
                .orElseThrow(() -> {
                    recordFailedLoginAttempt(request.getUsername());
                    return new BadCredentialsException("用户名或密码错误");
                });

        // 验证密码
        if (!passwordEncoder.matches(request.getPassword(), user.getPassword())) {
            recordFailedLoginAttempt(request.getUsername());
            throw new BadCredentialsException("用户名或密码错误");
        }

        // 检查用户状态
        if (user.getStatus() != UserStatus.ACTIVE) {
            throw new BusinessException("用户账户已被禁用");
        }

        // 更新登录信息
        user.updateLoginInfo(request.getLoginIp());
        userRepository.save(user);

        // 生成JWT令牌
        String accessToken = jwtTokenProvider.generateToken(user.getId(), user.getUsername());
        String refreshToken = jwtTokenProvider.generateRefreshToken(user.getId(), user.getUsername());

        // 缓存用户会话信息
        cacheUserSession(user.getId(), accessToken, refreshToken);

        // 清除登录失败记录
        clearLoginAttempts(request.getUsername());

        // 获取用户权限
        Set<String> permissions = getUserPermissions(user);

        log.info("用户登录成功: userId={}, username={}", user.getId(), user.getUsername());

        return LoginResponse.builder()
                .accessToken(accessToken)
                .refreshToken(refreshToken)
                .tokenType("Bearer")
                .expiresIn(86400L) // 24小时
                .refreshExpiresIn(604800L) // 7天
                .user(UserDTO.from(user))
                .loginTime(LocalDateTime.now())
                .loginIp(request.getLoginIp())
                .firstLogin(user.getLastLoginTime() == null)
                .permissions(permissions)
                .build();
    }

    /**
     * 获取用户详细信息
     */
    @Transactional(readOnly = true)
    public UserDTO getUserById(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new BusinessException("用户不存在"));
        return UserDTO.from(user);
    }

    /**
     * 获取用户学习偏好
     */
    @Transactional(readOnly = true)
    public UserPreferenceDTO getUserPreference(Long userId) {
        UserPreference preference = userPreferenceRepository.findByUserId(userId)
                .orElseThrow(() -> new BusinessException("用户偏好设置不存在"));
        return UserPreferenceDTO.from(preference);
    }

    /**
     * 验证用户唯一性
     */
    private void validateUserUniqueness(String username, String email, String phoneNumber) {
        if (userRepository.existsByUsername(username)) {
            throw new BusinessException("用户名已存在");
        }
        if (userRepository.existsByEmail(email)) {
            throw new BusinessException("邮箱已被注册");
        }
        if (phoneNumber != null && userRepository.existsByPhoneNumber(phoneNumber)) {
            throw new BusinessException("手机号已被注册");
        }
    }

    /**
     * 验证验证码
     */
    private void validateCaptcha(String captchaKey, String captcha) {
        if (captchaKey == null || captcha == null) {
            throw new BusinessException("验证码不能为空");
        }

        String cachedCaptcha = (String) redisTemplate.opsForValue().get("captcha:" + captchaKey);
        if (cachedCaptcha == null) {
            throw new BusinessException("验证码已过期");
        }

        if (!cachedCaptcha.equalsIgnoreCase(captcha)) {
            throw new BusinessException("验证码错误");
        }

        // 删除已使用的验证码
        redisTemplate.delete("captcha:" + captchaKey);
    }

    /**
     * 创建默认学习偏好
     */
    private void createDefaultPreference(User user) {
        UserPreference preference = UserPreference.builder()
                .user(user)
                .dailyStudyGoal(60)
                .studyReminderEnabled(true)
                .reminderTime(LocalTime.of(20, 0))
                .preferredDifficulty(Difficulty.MEDIUM)
                .learningMode(LearningMode.SYSTEMATIC)
                .aiRecommendationEnabled(true)
                .autoReviewEnabled(true)
                .autoAdjustPlan(true)
                .targetSubjects(new HashSet<>())
                .build();

        userPreferenceRepository.save(preference);
        log.info("为用户 {} 创建了默认学习偏好", user.getId());
    }

    /**
     * 异步发送欢迎邮件
     */
    private void sendWelcomeEmailAsync(User user) {
        // TODO: 实现异步邮件发送
        log.info("发送欢迎邮件给用户: {}", user.getEmail());
    }

    /**
     * 检查登录尝试次数
     */
    private void checkLoginAttempts(String username) {
        String key = LOGIN_ATTEMPT_KEY + username;
        Integer attempts = (Integer) redisTemplate.opsForValue().get(key);
        if (attempts != null && attempts >= MAX_LOGIN_ATTEMPTS) {
            throw new BusinessException("登录失败次数过多，请" + LOGIN_ATTEMPT_TIMEOUT + "分钟后再试");
        }
    }

    /**
     * 记录登录失败尝试
     */
    private void recordFailedLoginAttempt(String username) {
        String key = LOGIN_ATTEMPT_KEY + username;
        Integer attempts = (Integer) redisTemplate.opsForValue().get(key);
        attempts = attempts == null ? 1 : attempts + 1;
        redisTemplate.opsForValue().set(key, attempts, LOGIN_ATTEMPT_TIMEOUT, TimeUnit.MINUTES);
    }

    /**
     * 清除登录尝试记录
     */
    private void clearLoginAttempts(String username) {
        redisTemplate.delete(LOGIN_ATTEMPT_KEY + username);
    }

    /**
     * 是否需要验证码
     */
    private boolean needCaptchaForLogin(String username) {
        String key = LOGIN_ATTEMPT_KEY + username;
        Integer attempts = (Integer) redisTemplate.opsForValue().get(key);
        return attempts != null && attempts >= 3;
    }

    /**
     * 缓存用户会话信息
     */
    private void cacheUserSession(Long userId, String accessToken, String refreshToken) {
        String key = USER_SESSION_KEY + userId;
        redisTemplate.opsForHash().put(key, "accessToken", accessToken);
        redisTemplate.opsForHash().put(key, "refreshToken", refreshToken);
        redisTemplate.opsForHash().put(key, "loginTime", LocalDateTime.now().toString());
        redisTemplate.expire(key, 24, TimeUnit.HOURS);
    }

    /**
     * 获取用户权限
     */
    private Set<String> getUserPermissions(User user) {
        return user.getRoles().stream()
                .flatMap(role -> role.getPermissions().stream())
                .map(permission -> permission.getCode())
                .collect(Collectors.toSet());
    }

    /**
     * 用户退出登录
     */
    public void logout(Long userId) {
        log.info("用户退出登录: userId={}", userId);

        // 清除Redis中的会话信息
        String key = USER_SESSION_KEY + userId;
        redisTemplate.delete(key);

        // 可以在这里添加其他退出登录的逻辑，比如记录日志等
    }

    /**
     * 刷新访问令牌
     */
    public LoginResponse refreshToken(String refreshToken) {
        log.info("刷新访问令牌请求");

        // 验证刷新令牌
        if (!jwtTokenProvider.validateToken(refreshToken)) {
            throw new BusinessException("刷新令牌无效");
        }

        if (!jwtTokenProvider.isRefreshToken(refreshToken)) {
            throw new BusinessException("令牌类型错误");
        }

        // 从令牌中获取用户信息
        Long userId = jwtTokenProvider.getUserIdFromToken(refreshToken);
        String username = jwtTokenProvider.getUsernameFromToken(refreshToken);

        // 查找用户
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new BusinessException("用户不存在"));

        // 检查用户状态
        if (user.getStatus() != UserStatus.ACTIVE) {
            throw new BusinessException("用户账户已被禁用");
        }

        // 生成新的访问令牌
        String newAccessToken = jwtTokenProvider.generateToken(userId, username);
        String newRefreshToken = jwtTokenProvider.generateRefreshToken(userId, username);

        // 更新缓存
        cacheUserSession(userId, newAccessToken, newRefreshToken);

        return LoginResponse.builder()
                .accessToken(newAccessToken)
                .refreshToken(newRefreshToken)
                .tokenType("Bearer")
                .expiresIn(86400L)
                .refreshExpiresIn(604800L)
                .user(UserDTO.fromSimple(user))
                .loginTime(LocalDateTime.now())
                .build();
    }

    /**
     * 检查用户名是否可用
     */
    @Transactional(readOnly = true)
    public boolean isUsernameAvailable(String username) {
        return !userRepository.existsByUsername(username);
    }

    /**
     * 检查邮箱是否可用
     */
    @Transactional(readOnly = true)
    public boolean isEmailAvailable(String email) {
        return !userRepository.existsByEmail(email);
    }

    /**
     * 发送邮箱验证码
     */
    public void sendEmailVerificationCode(String email) {
        // 生成6位数字验证码
        String code = generateVerificationCode();

        // 缓存验证码，5分钟过期
        String key = "email:code:" + email;
        redisTemplate.opsForValue().set(key, code, 5, TimeUnit.MINUTES);

        // TODO: 发送邮件
        log.info("发送邮箱验证码: email={}, code={}", email, code);
    }

    /**
     * 发送手机验证码
     */
    public void sendSmsVerificationCode(String phoneNumber) {
        // 生成6位数字验证码
        String code = generateVerificationCode();

        // 缓存验证码，5分钟过期
        String key = "sms:code:" + phoneNumber;
        redisTemplate.opsForValue().set(key, code, 5, TimeUnit.MINUTES);

        // TODO: 发送短信
        log.info("发送手机验证码: phoneNumber={}, code={}", phoneNumber, code);
    }

    /**
     * 生成图形验证码
     */
    public Object generateCaptcha() {
        // TODO: 实现图形验证码生成
        String captchaKey = "captcha_" + System.currentTimeMillis();
        String captchaCode = generateVerificationCode();

        // 缓存验证码，5分钟过期
        redisTemplate.opsForValue().set("captcha:" + captchaKey, captchaCode, 5, TimeUnit.MINUTES);

        return new Object() {
            public String getKey() { return captchaKey; }
            public String getImage() { return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="; }
        };
    }

    /**
     * 生成验证码
     */
    private String generateVerificationCode() {
        return String.valueOf((int) ((Math.random() * 9 + 1) * 100000));
    }

}
