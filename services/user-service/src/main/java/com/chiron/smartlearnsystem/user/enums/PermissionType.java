package com.chiron.smartlearnsystem.user.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 权限类型枚举
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum PermissionType {

    /**
     * 菜单权限
     */
    MENU("MENU", "菜单权限", "控制菜单的显示和访问"),

    /**
     * 按钮权限
     */
    BUTTON("BUTTON", "按钮权限", "控制页面按钮的显示和操作"),

    /**
     * API权限
     */
    API("API", "API权限", "控制API接口的访问权限"),

    /**
     * 数据权限
     */
    DATA("DATA", "数据权限", "控制数据的访问范围");

    private final String code;
    private final String name;
    private final String description;

    /**
     * 根据代码获取权限类型
     */
    public static PermissionType getByCode(String code) {
        for (PermissionType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return API; // 默认返回API权限
    }

}
