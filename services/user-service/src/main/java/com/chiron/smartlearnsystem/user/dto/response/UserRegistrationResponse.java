package com.chiron.smartlearnsystem.user.dto.response;

import com.chiron.smartlearnsystem.user.dto.UserDTO;
import com.chiron.smartlearnsystem.user.entity.User;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 用户注册响应
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserRegistrationResponse {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 注册时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime registrationTime;

    /**
     * 是否需要邮箱验证
     */
    private Boolean needEmailVerification = false;

    /**
     * 是否需要手机验证
     */
    private Boolean needPhoneVerification = false;

    /**
     * 欢迎消息
     */
    private String welcomeMessage;

    /**
     * 下一步操作提示
     */
    private String nextStepTip;

    /**
     * 从User实体创建注册响应
     */
    public static UserRegistrationResponse from(User user) {
        if (user == null) {
            return null;
        }

        return UserRegistrationResponse.builder()
                .userId(user.getId())
                .username(user.getUsername())
                .email(user.getEmail())
                .realName(user.getRealName())
                .registrationTime(user.getCreatedAt())
                .needEmailVerification(false) // 根据系统配置决定
                .needPhoneVerification(false) // 根据系统配置决定
                .welcomeMessage("欢迎加入SmartLearn智能学习系统！")
                .nextStepTip("请完善您的学习偏好设置，开始您的学习之旅。")
                .build();
    }

}
