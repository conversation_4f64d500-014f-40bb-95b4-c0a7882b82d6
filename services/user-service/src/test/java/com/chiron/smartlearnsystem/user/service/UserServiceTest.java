package com.chiron.smartlearnsystem.user.service;

import com.chiron.smartlearnsystem.common.core.enums.UserStatus;
import com.chiron.smartlearnsystem.user.dto.request.UserRegistrationRequest;
import com.chiron.smartlearnsystem.user.dto.response.UserRegistrationResponse;
import com.chiron.smartlearnsystem.user.entity.Role;
import com.chiron.smartlearnsystem.user.entity.User;
import com.chiron.smartlearnsystem.user.repository.RoleRepository;
import com.chiron.smartlearnsystem.user.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 用户服务测试
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@ExtendWith(MockitoExtension.class)
class UserServiceTest {

    @Mock
    private UserRepository userRepository;

    @Mock
    private RoleRepository roleRepository;

    @Mock
    private PasswordEncoder passwordEncoder;

    @Mock
    private RedisTemplate<String, Object> redisTemplate;

    @Mock
    private ValueOperations<String, Object> valueOperations;

    @InjectMocks
    private UserService userService;

    private UserRegistrationRequest registrationRequest;
    private Role defaultRole;

    @BeforeEach
    void setUp() {
        // 设置注册请求
        registrationRequest = UserRegistrationRequest.builder()
                .username("testuser")
                .email("<EMAIL>")
                .password("Test123456")
                .confirmPassword("Test123456")
                .realName("测试用户")
                .captcha("1234")
                .captchaKey("test_key")
                .agreeTerms(true)
                .build();

        // 设置默认角色
        defaultRole = Role.builder()
                .id(1L)
                .name("普通用户")
                .code("ROLE_USER")
                .enabled(true)
                .build();

        // 设置Redis模拟
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
    }

    @Test
    void testRegisterSuccess() {
        // 准备测试数据
        when(userRepository.existsByUsername(anyString())).thenReturn(false);
        when(userRepository.existsByEmail(anyString())).thenReturn(false);
        when(roleRepository.findByCode("ROLE_USER")).thenReturn(Optional.of(defaultRole));
        when(passwordEncoder.encode(anyString())).thenReturn("encoded_password");
        when(valueOperations.get("captcha:test_key")).thenReturn("1234");
        
        User savedUser = User.builder()
                .id(1L)
                .username("testuser")
                .email("<EMAIL>")
                .password("encoded_password")
                .realName("测试用户")
                .status(UserStatus.ACTIVE)
                .build();
        
        when(userRepository.save(any(User.class))).thenReturn(savedUser);

        // 执行测试
        UserRegistrationResponse response = userService.register(registrationRequest);

        // 验证结果
        assertNotNull(response);
        assertEquals("testuser", response.getUsername());
        assertEquals("<EMAIL>", response.getEmail());
        assertEquals("测试用户", response.getRealName());
        assertEquals(1L, response.getUserId());

        // 验证方法调用
        verify(userRepository).existsByUsername("testuser");
        verify(userRepository).existsByEmail("<EMAIL>");
        verify(roleRepository).findByCode("ROLE_USER");
        verify(passwordEncoder).encode("Test123456");
        verify(userRepository).save(any(User.class));
    }

    @Test
    void testRegisterWithExistingUsername() {
        // 准备测试数据
        when(userRepository.existsByUsername("testuser")).thenReturn(true);

        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> {
            userService.register(registrationRequest);
        });

        // 验证方法调用
        verify(userRepository).existsByUsername("testuser");
        verify(userRepository, never()).save(any(User.class));
    }

    @Test
    void testRegisterWithPasswordMismatch() {
        // 修改注册请求
        registrationRequest.setConfirmPassword("DifferentPassword");

        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> {
            userService.register(registrationRequest);
        });

        // 验证没有调用保存方法
        verify(userRepository, never()).save(any(User.class));
    }

    @Test
    void testRegisterWithoutAgreement() {
        // 修改注册请求
        registrationRequest.setAgreeTerms(false);

        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> {
            userService.register(registrationRequest);
        });

        // 验证没有调用保存方法
        verify(userRepository, never()).save(any(User.class));
    }

    @Test
    void testIsUsernameAvailable() {
        // 准备测试数据
        when(userRepository.existsByUsername("available")).thenReturn(false);
        when(userRepository.existsByUsername("taken")).thenReturn(true);

        // 执行测试
        assertTrue(userService.isUsernameAvailable("available"));
        assertFalse(userService.isUsernameAvailable("taken"));

        // 验证方法调用
        verify(userRepository).existsByUsername("available");
        verify(userRepository).existsByUsername("taken");
    }

    @Test
    void testIsEmailAvailable() {
        // 准备测试数据
        when(userRepository.existsByEmail("<EMAIL>")).thenReturn(false);
        when(userRepository.existsByEmail("<EMAIL>")).thenReturn(true);

        // 执行测试
        assertTrue(userService.isEmailAvailable("<EMAIL>"));
        assertFalse(userService.isEmailAvailable("<EMAIL>"));

        // 验证方法调用
        verify(userRepository).existsByEmail("<EMAIL>");
        verify(userRepository).existsByEmail("<EMAIL>");
    }

}
