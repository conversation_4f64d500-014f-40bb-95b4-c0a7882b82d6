package com.chiron.smartlearnsystem.learning.dto;

import com.chiron.smartlearnsystem.learning.entity.StudyStage;
import com.chiron.smartlearnsystem.learning.enums.StageStatus;
import com.chiron.smartlearnsystem.learning.enums.StageType;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Set;

/**
 * 学习阶段DTO
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StudyStageDTO {

    /**
     * 阶段ID
     */
    private Long id;

    /**
     * 学习计划ID
     */
    private Long planId;

    /**
     * 阶段名称
     */
    private String name;

    /**
     * 阶段描述
     */
    private String description;

    /**
     * 阶段类型
     */
    private StageType stageType;

    /**
     * 阶段状态
     */
    private StageStatus status;

    /**
     * 阶段顺序
     */
    private Integer stageOrder;

    /**
     * 开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    /**
     * 结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    /**
     * 预计学习时长（分钟）
     */
    private Integer estimatedMinutes;

    /**
     * 实际学习时长（分钟）
     */
    private Integer actualMinutes;

    /**
     * 目标题目数量
     */
    private Integer targetQuestions;

    /**
     * 已完成题目数量
     */
    private Integer completedQuestions;

    /**
     * 目标正确率
     */
    private BigDecimal targetAccuracy;

    /**
     * 实际正确率
     */
    private BigDecimal actualAccuracy;

    /**
     * 进度百分比
     */
    private BigDecimal progressPercentage;

    /**
     * 学习重点
     */
    private Set<String> focusPoints;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 从StudyStage实体转换为DTO
     */
    public static StudyStageDTO from(StudyStage studyStage) {
        if (studyStage == null) {
            return null;
        }

        return StudyStageDTO.builder()
                .id(studyStage.getId())
                .planId(studyStage.getPlanId())
                .name(studyStage.getName())
                .description(studyStage.getDescription())
                .stageType(studyStage.getStageType())
                .status(studyStage.getStatus())
                .stageOrder(studyStage.getStageOrder())
                .startDate(studyStage.getStartDate())
                .endDate(studyStage.getEndDate())
                .estimatedMinutes(studyStage.getEstimatedMinutes())
                .actualMinutes(studyStage.getActualMinutes())
                .targetQuestions(studyStage.getTargetQuestions())
                .completedQuestions(studyStage.getCompletedQuestions())
                .targetAccuracy(studyStage.getTargetAccuracy())
                .actualAccuracy(studyStage.getActualAccuracy())
                .progressPercentage(studyStage.getProgressPercentage())
                .focusPoints(studyStage.getFocusPoints())
                .createdAt(studyStage.getCreatedAt())
                .updatedAt(studyStage.getUpdatedAt())
                .build();
    }

    /**
     * 获取阶段类型描述
     */
    public String getStageTypeDescription() {
        return stageType != null ? stageType.getDescription() : "";
    }

    /**
     * 获取状态描述
     */
    public String getStatusDescription() {
        return status != null ? status.getDescription() : "";
    }

    /**
     * 获取进度描述
     */
    public String getProgressDescription() {
        if (progressPercentage == null) {
            return "0%";
        }
        return progressPercentage.setScale(1, BigDecimal.ROUND_HALF_UP) + "%";
    }

    /**
     * 检查是否可以开始
     */
    public boolean canStart() {
        return status != null && status.canStart();
    }

    /**
     * 检查是否进行中
     */
    public boolean isInProgress() {
        return status != null && status.isActive();
    }

    /**
     * 检查是否已完成
     */
    public boolean isCompleted() {
        return status != null && status.isCompleted();
    }

}
