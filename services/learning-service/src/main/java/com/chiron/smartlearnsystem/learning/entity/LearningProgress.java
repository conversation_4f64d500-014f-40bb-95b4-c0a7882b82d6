package com.chiron.smartlearnsystem.study.entity;

import com.chiron.smartlearnsystem.common.database.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 学习进度实体
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Entity
@Table(name = "learning_progress", indexes = {
    @Index(name = "idx_user_subject", columnList = "user_id, subject_code"),
    @Index(name = "idx_user_knowledge", columnList = "user_id, knowledge_point"),
    @Index(name = "idx_progress_date", columnList = "progress_date"),
    @Index(name = "idx_mastery_level", columnList = "mastery_level")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LearningProgress extends BaseEntity {

    /**
     * 用户ID
     */
    @Column(name = "user_id", nullable = false)
    private Long userId;

    /**
     * 科目代码
     */
    @Column(name = "subject_code", nullable = false, length = 20)
    private String subjectCode;

    /**
     * 知识点
     */
    @Column(name = "knowledge_point", nullable = false, length = 200)
    private String knowledgePoint;

    /**
     * 掌握程度（0-1）
     */
    @Column(name = "mastery_level", nullable = false, precision = 5, scale = 4)
    private BigDecimal masteryLevel = BigDecimal.ZERO;

    /**
     * 学习次数
     */
    @Column(name = "study_count", nullable = false)
    private Integer studyCount = 0;

    /**
     * 总学习时长（分钟）
     */
    @Column(name = "total_study_minutes", nullable = false)
    private Integer totalStudyMinutes = 0;

    /**
     * 答题总数
     */
    @Column(name = "total_questions", nullable = false)
    private Integer totalQuestions = 0;

    /**
     * 正确答题数
     */
    @Column(name = "correct_questions", nullable = false)
    private Integer correctQuestions = 0;

    /**
     * 当前正确率
     */
    @Column(name = "current_accuracy", precision = 5, scale = 4)
    private BigDecimal currentAccuracy = BigDecimal.ZERO;

    /**
     * 历史最高正确率
     */
    @Column(name = "best_accuracy", precision = 5, scale = 4)
    private BigDecimal bestAccuracy = BigDecimal.ZERO;

    /**
     * 连续正确次数
     */
    @Column(name = "consecutive_correct", nullable = false)
    private Integer consecutiveCorrect = 0;

    /**
     * 最大连续正确次数
     */
    @Column(name = "max_consecutive_correct", nullable = false)
    private Integer maxConsecutiveCorrect = 0;

    /**
     * 错误次数
     */
    @Column(name = "error_count", nullable = false)
    private Integer errorCount = 0;

    /**
     * 最近错误时间
     */
    @Column(name = "last_error_time")
    private LocalDateTime lastErrorTime;

    /**
     * 首次学习时间
     */
    @Column(name = "first_study_time")
    private LocalDateTime firstStudyTime;

    /**
     * 最后学习时间
     */
    @Column(name = "last_study_time")
    private LocalDateTime lastStudyTime;

    /**
     * 进度日期
     */
    @Column(name = "progress_date", nullable = false)
    private LocalDate progressDate;

    /**
     * 学习强度（1-10）
     */
    @Column(name = "study_intensity", precision = 3, scale = 1)
    private BigDecimal studyIntensity = BigDecimal.valueOf(5.0);

    /**
     * 遗忘曲线参数（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "forgetting_curve", columnDefinition = "JSON")
    private String forgettingCurve;

    /**
     * 下次复习时间
     */
    @Column(name = "next_review_time")
    private LocalDateTime nextReviewTime;

    /**
     * 复习间隔（天）
     */
    @Column(name = "review_interval", nullable = false)
    private Integer reviewInterval = 1;

    /**
     * 复习次数
     */
    @Column(name = "review_count", nullable = false)
    private Integer reviewCount = 0;

    /**
     * 学习效率评分（1-10）
     */
    @Column(name = "efficiency_score", precision = 3, scale = 1)
    private BigDecimal efficiencyScore;

    /**
     * 进度变化趋势（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "progress_trend", columnDefinition = "JSON")
    private String progressTrend;

    /**
     * 更新学习进度
     */
    public void updateProgress(boolean isCorrect, Integer studyMinutes) {
        this.studyCount++;
        this.totalStudyMinutes += studyMinutes;
        this.totalQuestions++;
        this.lastStudyTime = LocalDateTime.now();
        
        if (this.firstStudyTime == null) {
            this.firstStudyTime = this.lastStudyTime;
        }
        
        if (isCorrect) {
            this.correctQuestions++;
            this.consecutiveCorrect++;
            this.maxConsecutiveCorrect = Math.max(this.maxConsecutiveCorrect, this.consecutiveCorrect);
        } else {
            this.errorCount++;
            this.consecutiveCorrect = 0;
            this.lastErrorTime = this.lastStudyTime;
        }
        
        // 更新正确率
        updateAccuracy();
        
        // 更新掌握程度
        updateMasteryLevel();
        
        // 计算下次复习时间
        calculateNextReviewTime();
    }

    /**
     * 更新正确率
     */
    private void updateAccuracy() {
        if (totalQuestions > 0) {
            this.currentAccuracy = BigDecimal.valueOf(correctQuestions)
                    .divide(BigDecimal.valueOf(totalQuestions), 4, BigDecimal.ROUND_HALF_UP);
            
            if (this.currentAccuracy.compareTo(this.bestAccuracy) > 0) {
                this.bestAccuracy = this.currentAccuracy;
            }
        }
    }

    /**
     * 更新掌握程度
     */
    private void updateMasteryLevel() {
        // 基于正确率、连续正确次数、学习次数等因素计算掌握程度
        BigDecimal accuracyWeight = this.currentAccuracy.multiply(BigDecimal.valueOf(0.6));
        BigDecimal consistencyWeight = BigDecimal.valueOf(Math.min(1.0, consecutiveCorrect / 5.0))
                .multiply(BigDecimal.valueOf(0.3));
        BigDecimal experienceWeight = BigDecimal.valueOf(Math.min(1.0, studyCount / 10.0))
                .multiply(BigDecimal.valueOf(0.1));
        
        this.masteryLevel = accuracyWeight.add(consistencyWeight).add(experienceWeight);
        
        // 确保掌握程度在0-1之间
        if (this.masteryLevel.compareTo(BigDecimal.ONE) > 0) {
            this.masteryLevel = BigDecimal.ONE;
        }
    }

    /**
     * 计算下次复习时间
     */
    private void calculateNextReviewTime() {
        // 基于遗忘曲线和掌握程度计算复习间隔
        double masteryFactor = this.masteryLevel.doubleValue();
        int baseInterval = this.reviewInterval;
        
        if (this.consecutiveCorrect >= 3) {
            // 连续正确，延长复习间隔
            this.reviewInterval = (int) (baseInterval * (1 + masteryFactor));
        } else if (this.consecutiveCorrect == 0) {
            // 刚答错，缩短复习间隔
            this.reviewInterval = Math.max(1, baseInterval / 2);
        }
        
        // 限制复习间隔范围
        this.reviewInterval = Math.max(1, Math.min(30, this.reviewInterval));
        
        this.nextReviewTime = LocalDateTime.now().plusDays(this.reviewInterval);
    }

    /**
     * 检查是否需要复习
     */
    public boolean needsReview() {
        return nextReviewTime != null && LocalDateTime.now().isAfter(nextReviewTime);
    }

    /**
     * 检查是否已掌握
     */
    public boolean isMastered() {
        return masteryLevel.compareTo(BigDecimal.valueOf(0.8)) >= 0;
    }

    /**
     * 检查是否为薄弱点
     */
    public boolean isWeakPoint() {
        return masteryLevel.compareTo(BigDecimal.valueOf(0.3)) < 0 || 
               currentAccuracy.compareTo(BigDecimal.valueOf(0.5)) < 0;
    }

    /**
     * 获取学习天数
     */
    public long getStudyDays() {
        if (firstStudyTime == null) {
            return 0;
        }
        return java.time.temporal.ChronoUnit.DAYS.between(
                firstStudyTime.toLocalDate(), 
                LocalDate.now()
        ) + 1;
    }

}
