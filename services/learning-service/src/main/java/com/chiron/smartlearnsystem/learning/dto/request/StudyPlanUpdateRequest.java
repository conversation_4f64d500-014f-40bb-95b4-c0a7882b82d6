package com.chiron.smartlearnsystem.study.dto.request;

import com.chiron.smartlearnsystem.common.core.enums.Difficulty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.Future;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Size;
import java.time.LocalDate;
import java.util.Set;

/**
 * 学习计划更新请求
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StudyPlanUpdateRequest {

    /**
     * 计划名称
     */
    @Size(max = 200, message = "计划名称长度不能超过200个字符")
    private String name;

    /**
     * 计划描述
     */
    @Size(max = 1000, message = "计划描述长度不能超过1000个字符")
    private String description;

    /**
     * 目标考试日期
     */
    @Future(message = "目标考试日期必须是未来日期")
    private LocalDate targetExamDate;

    /**
     * 计划结束日期
     */
    private LocalDate endDate;

    /**
     * 每日学习时长目标（分钟）
     */
    @Min(value = 15, message = "每日学习时长不能少于15分钟")
    @Max(value = 480, message = "每日学习时长不能超过8小时")
    private Integer dailyStudyMinutes;

    /**
     * 每周学习天数目标
     */
    @Min(value = 1, message = "每周学习天数不能少于1天")
    @Max(value = 7, message = "每周学习天数不能超过7天")
    private Integer weeklyStudyDays;

    /**
     * 目标难度
     */
    private Difficulty targetDifficulty;

    /**
     * 是否启用智能调整
     */
    private Boolean autoAdjustEnabled;

    /**
     * 目标知识点
     */
    @Size(max = 50, message = "目标知识点数量不能超过50个")
    private Set<String> targetKnowledgePoints;

}
