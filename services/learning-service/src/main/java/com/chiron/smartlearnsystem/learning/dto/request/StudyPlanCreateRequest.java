package com.chiron.smartlearnsystem.study.dto.request;

import com.chiron.smartlearnsystem.common.core.enums.Difficulty;
import com.chiron.smartlearnsystem.study.enums.PlanType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.*;
import java.time.LocalDate;
import java.util.Set;

/**
 * 学习计划创建请求
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StudyPlanCreateRequest {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 计划名称
     */
    @NotBlank(message = "计划名称不能为空")
    @Size(max = 200, message = "计划名称长度不能超过200个字符")
    private String name;

    /**
     * 计划描述
     */
    @Size(max = 1000, message = "计划描述长度不能超过1000个字符")
    private String description;

    /**
     * 科目代码
     */
    @NotBlank(message = "科目代码不能为空")
    @Size(max = 20, message = "科目代码长度不能超过20个字符")
    private String subjectCode;

    /**
     * 计划类型
     */
    @NotNull(message = "计划类型不能为空")
    private PlanType planType;

    /**
     * 目标考试日期
     */
    @Future(message = "目标考试日期必须是未来日期")
    private LocalDate targetExamDate;

    /**
     * 计划开始日期
     */
    private LocalDate startDate;

    /**
     * 计划结束日期
     */
    private LocalDate endDate;

    /**
     * 每日学习时长目标（分钟）
     */
    @NotNull(message = "每日学习时长不能为空")
    @Min(value = 15, message = "每日学习时长不能少于15分钟")
    @Max(value = 480, message = "每日学习时长不能超过8小时")
    private Integer dailyStudyMinutes;

    /**
     * 每周学习天数目标
     */
    @NotNull(message = "每周学习天数不能为空")
    @Min(value = 1, message = "每周学习天数不能少于1天")
    @Max(value = 7, message = "每周学习天数不能超过7天")
    private Integer weeklyStudyDays;

    /**
     * 目标难度
     */
    @NotNull(message = "目标难度不能为空")
    private Difficulty targetDifficulty;

    /**
     * 目标学习时长（分钟）
     */
    @Min(value = 60, message = "目标学习时长不能少于1小时")
    private Integer targetMinutes;

    /**
     * 目标题目数量
     */
    @Min(value = 10, message = "目标题目数量不能少于10题")
    private Integer targetQuestions;

    /**
     * 是否启用智能调整
     */
    private Boolean autoAdjustEnabled = true;

    /**
     * 目标知识点
     */
    @Size(max = 50, message = "目标知识点数量不能超过50个")
    private Set<String> targetKnowledgePoints;

    /**
     * 验证日期逻辑
     */
    public boolean isValidDateRange() {
        if (startDate != null && endDate != null) {
            return !endDate.isBefore(startDate);
        }
        return true;
    }

    /**
     * 验证考试日期
     */
    public boolean isValidExamDate() {
        if (targetExamDate != null && endDate != null) {
            return !targetExamDate.isBefore(endDate);
        }
        return true;
    }

    /**
     * 计算计划总时长（天）
     */
    public long getPlanDuration() {
        if (startDate != null && endDate != null) {
            return java.time.temporal.ChronoUnit.DAYS.between(startDate, endDate) + 1;
        }
        return 0;
    }

}
