package com.chiron.smartlearnsystem.study.entity;

import com.chiron.smartlearnsystem.common.database.entity.BaseEntity;
import com.chiron.smartlearnsystem.study.enums.StageStatus;
import com.chiron.smartlearnsystem.study.enums.StageType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashSet;
import java.util.Set;

/**
 * 学习阶段实体
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Entity
@Table(name = "study_stages", indexes = {
    @Index(name = "idx_plan_order", columnList = "plan_id, stage_order"),
    @Index(name = "idx_status", columnList = "status"),
    @Index(name = "idx_dates", columnList = "start_date, end_date")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StudyStage extends BaseEntity {

    /**
     * 学习计划ID
     */
    @Column(name = "plan_id", nullable = false)
    private Long planId;

    /**
     * 阶段名称
     */
    @Column(nullable = false, length = 200)
    private String name;

    /**
     * 阶段描述
     */
    @Column(columnDefinition = "TEXT")
    private String description;

    /**
     * 阶段类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "stage_type", nullable = false, length = 20)
    private StageType stageType;

    /**
     * 阶段状态
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private StageStatus status = StageStatus.PENDING;

    /**
     * 阶段顺序
     */
    @Column(name = "stage_order", nullable = false)
    private Integer stageOrder;

    /**
     * 开始日期
     */
    @Column(name = "start_date")
    private LocalDate startDate;

    /**
     * 结束日期
     */
    @Column(name = "end_date")
    private LocalDate endDate;

    /**
     * 预计学习时长（分钟）
     */
    @Column(name = "estimated_minutes")
    private Integer estimatedMinutes;

    /**
     * 实际学习时长（分钟）
     */
    @Column(name = "actual_minutes", nullable = false)
    private Integer actualMinutes = 0;

    /**
     * 目标题目数量
     */
    @Column(name = "target_questions")
    private Integer targetQuestions;

    /**
     * 已完成题目数量
     */
    @Column(name = "completed_questions", nullable = false)
    private Integer completedQuestions = 0;

    /**
     * 目标正确率
     */
    @Column(name = "target_accuracy", precision = 5, scale = 4)
    private BigDecimal targetAccuracy;

    /**
     * 实际正确率
     */
    @Column(name = "actual_accuracy", precision = 5, scale = 4)
    private BigDecimal actualAccuracy;

    /**
     * 进度百分比
     */
    @Column(name = "progress_percentage", precision = 5, scale = 2)
    private BigDecimal progressPercentage = BigDecimal.ZERO;

    /**
     * 阶段配置（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "stage_config", columnDefinition = "JSON")
    private String stageConfig;

    /**
     * 前置条件（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "prerequisites", columnDefinition = "JSON")
    private String prerequisites;

    /**
     * 学习重点
     */
    @ElementCollection
    @CollectionTable(
        name = "study_stage_focus_points",
        joinColumns = @JoinColumn(name = "stage_id")
    )
    @Column(name = "focus_point")
    private Set<String> focusPoints = new HashSet<>();

    /**
     * 关联的学习计划
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "plan_id", insertable = false, updatable = false)
    private StudyPlan studyPlan;

    /**
     * 更新进度
     */
    public void updateProgress() {
        if (targetQuestions != null && targetQuestions > 0) {
            this.progressPercentage = BigDecimal.valueOf(completedQuestions)
                    .divide(BigDecimal.valueOf(targetQuestions), 4, BigDecimal.ROUND_HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
        } else if (estimatedMinutes != null && estimatedMinutes > 0) {
            this.progressPercentage = BigDecimal.valueOf(actualMinutes)
                    .divide(BigDecimal.valueOf(estimatedMinutes), 4, BigDecimal.ROUND_HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
        }
        
        // 检查是否完成
        if (this.progressPercentage.compareTo(BigDecimal.valueOf(100)) >= 0) {
            this.status = StageStatus.COMPLETED;
        }
    }

    /**
     * 开始阶段
     */
    public void startStage() {
        this.status = StageStatus.IN_PROGRESS;
        if (this.startDate == null) {
            this.startDate = LocalDate.now();
        }
    }

    /**
     * 完成阶段
     */
    public void completeStage() {
        this.status = StageStatus.COMPLETED;
        this.progressPercentage = BigDecimal.valueOf(100);
        if (this.endDate == null) {
            this.endDate = LocalDate.now();
        }
    }

    /**
     * 增加学习时长
     */
    public void addStudyTime(Integer minutes) {
        this.actualMinutes += minutes;
        updateProgress();
    }

    /**
     * 增加完成题目数
     */
    public void addCompletedQuestions(Integer count) {
        this.completedQuestions += count;
        updateProgress();
    }

    /**
     * 检查前置条件是否满足
     */
    public boolean checkPrerequisites() {
        // TODO: 实现前置条件检查逻辑
        return true;
    }

    /**
     * 检查是否可以开始
     */
    public boolean canStart() {
        return status == StageStatus.PENDING && checkPrerequisites();
    }

    /**
     * 检查是否已完成
     */
    public boolean isCompleted() {
        return status == StageStatus.COMPLETED;
    }

    /**
     * 检查是否进行中
     */
    public boolean isInProgress() {
        return status == StageStatus.IN_PROGRESS;
    }

}
