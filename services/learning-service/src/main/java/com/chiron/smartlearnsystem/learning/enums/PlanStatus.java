package com.chiron.smartlearnsystem.learning.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 学习计划状态枚举
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Getter
public enum PlanStatus {

    /**
     * 活跃状态
     */
    ACTIVE("ACTIVE", "进行中", "学习计划正在执行中"),

    /**
     * 暂停状态
     */
    PAUSED("PAUSED", "已暂停", "学习计划暂时停止"),

    /**
     * 已完成
     */
    COMPLETED("COMPLETED", "已完成", "学习计划已完成"),

    /**
     * 已过期
     */
    EXPIRED("EXPIRED", "已过期", "学习计划已过期"),

    /**
     * 已取消
     */
    CANCELLED("CANCELLED", "已取消", "学习计划已取消"),

    /**
     * 草稿状态
     */
    DRAFT("DRAFT", "草稿", "学习计划正在制定中"),

    /**
     * 待开始
     */
    PENDING("PENDING", "待开始", "学习计划等待开始");

    private final String code;
    private final String name;
    private final String description;

    PlanStatus(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 判断是否可以开始
     */
    public boolean canStart() {
        return this == DRAFT || this == PENDING;
    }

    /**
     * 根据代码获取计划状态
     */
    public static PlanStatus getByCode(String code) {
        for (PlanStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return DRAFT; // 默认返回草稿状态
    }

    /**
     * 是否为活跃状态
     */
    public boolean isActive() {
        return this == ACTIVE;
    }

    /**
     * 是否为完成状态
     */
    public boolean isCompleted() {
        return this == COMPLETED;
    }

    /**
     * 是否为最终状态
     */
    public boolean isFinalStatus() {
        return this == COMPLETED || this == EXPIRED || this == CANCELLED;
    }

    /**
     * 是否可以执行学习
     */
    public boolean canStudy() {
        return this == ACTIVE;
    }

    /**
     * 是否可以编辑
     */
    public boolean canEdit() {
        return this == DRAFT || this == PENDING || this == PAUSED;
    }

    /**
     * 是否可以暂停
     */
    public boolean canPause() {
        return this == ACTIVE;
    }

    /**
     * 是否可以恢复
     */
    public boolean canResume() {
        return this == PAUSED;
    }

    /**
     * 是否可以取消
     */
    public boolean canCancel() {
        return this == ACTIVE || this == PAUSED || this == PENDING || this == DRAFT;
    }

}
