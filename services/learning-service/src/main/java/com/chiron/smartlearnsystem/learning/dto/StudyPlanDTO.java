package com.chiron.smartlearnsystem.learning.dto;

import com.chiron.smartlearnsystem.common.core.enums.Difficulty;
import com.chiron.smartlearnsystem.learning.entity.StudyPlan;
import com.chiron.smartlearnsystem.learning.enums.PlanStatus;
import com.chiron.smartlearnsystem.learning.enums.PlanType;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 学习计划DTO
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StudyPlanDTO {

    /**
     * 计划ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 计划名称
     */
    private String name;

    /**
     * 计划描述
     */
    private String description;

    /**
     * 科目代码
     */
    private String subjectCode;

    /**
     * 计划类型
     */
    private PlanType planType;

    /**
     * 计划状态
     */
    private PlanStatus status;

    /**
     * 目标考试日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate targetExamDate;

    /**
     * 计划开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    /**
     * 计划结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    /**
     * 每日学习时长目标（分钟）
     */
    private Integer dailyStudyMinutes;

    /**
     * 每周学习天数目标
     */
    private Integer weeklyStudyDays;

    /**
     * 目标难度
     */
    private Difficulty targetDifficulty;

    /**
     * 总进度百分比
     */
    private BigDecimal progressPercentage;

    /**
     * 已完成学习时长（分钟）
     */
    private Integer completedMinutes;

    /**
     * 目标学习时长（分钟）
     */
    private Integer targetMinutes;

    /**
     * 已完成题目数量
     */
    private Integer completedQuestions;

    /**
     * 目标题目数量
     */
    private Integer targetQuestions;

    /**
     * 平均正确率
     */
    private BigDecimal averageAccuracy;

    /**
     * 最后学习时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastStudyTime;

    /**
     * 连续学习天数
     */
    private Integer consecutiveDays;

    /**
     * 是否启用智能调整
     */
    private Boolean autoAdjustEnabled;

    /**
     * 目标知识点
     */
    private Set<String> targetKnowledgePoints;

    /**
     * 学习阶段
     */
    private List<StudyStageDTO> studyStages;

    /**
     * 剩余天数
     */
    private Long remainingDays;

    /**
     * 是否过期
     */
    private Boolean expired;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 从StudyPlan实体转换为DTO
     */
    public static StudyPlanDTO from(StudyPlan studyPlan) {
        if (studyPlan == null) {
            return null;
        }

        StudyPlanDTOBuilder builder = StudyPlanDTO.builder()
                .id(studyPlan.getId())
                .userId(studyPlan.getUserId())
                .name(studyPlan.getName())
                .description(studyPlan.getDescription())
                .subjectCode(studyPlan.getSubjectCode())
                .planType(studyPlan.getPlanType())
                .status(studyPlan.getStatus())
                .targetExamDate(studyPlan.getTargetExamDate())
                .startDate(studyPlan.getStartDate())
                .endDate(studyPlan.getEndDate())
                .dailyStudyMinutes(studyPlan.getDailyStudyMinutes())
                .weeklyStudyDays(studyPlan.getWeeklyStudyDays())
                .targetDifficulty(studyPlan.getTargetDifficulty())
                .progressPercentage(studyPlan.getProgressPercentage())
                .completedMinutes(studyPlan.getCompletedMinutes())
                .targetMinutes(studyPlan.getTargetMinutes())
                .completedQuestions(studyPlan.getCompletedQuestions())
                .targetQuestions(studyPlan.getTargetQuestions())
                .averageAccuracy(studyPlan.getAverageAccuracy())
                .lastStudyTime(studyPlan.getLastStudyTime())
                .consecutiveDays(studyPlan.getConsecutiveDays())
                .autoAdjustEnabled(studyPlan.getAutoAdjustEnabled())
                .targetKnowledgePoints(studyPlan.getTargetKnowledgePoints())
                .remainingDays(studyPlan.getRemainingDays())
                .expired(studyPlan.isExpired())
                .createdAt(studyPlan.getCreatedAt())
                .updatedAt(studyPlan.getUpdatedAt());

        // 转换学习阶段
        if (studyPlan.getStudyStages() != null && !studyPlan.getStudyStages().isEmpty()) {
            List<StudyStageDTO> stageDTOs = studyPlan.getStudyStages().stream()
                    .map(StudyStageDTO::from)
                    .collect(Collectors.toList());
            builder.studyStages(stageDTOs);
        }

        return builder.build();
    }

    /**
     * 从StudyPlan实体转换为简化DTO（不包含阶段信息）
     */
    public static StudyPlanDTO fromSimple(StudyPlan studyPlan) {
        if (studyPlan == null) {
            return null;
        }

        return StudyPlanDTO.builder()
                .id(studyPlan.getId())
                .userId(studyPlan.getUserId())
                .name(studyPlan.getName())
                .description(studyPlan.getDescription())
                .subjectCode(studyPlan.getSubjectCode())
                .planType(studyPlan.getPlanType())
                .status(studyPlan.getStatus())
                .targetExamDate(studyPlan.getTargetExamDate())
                .startDate(studyPlan.getStartDate())
                .endDate(studyPlan.getEndDate())
                .progressPercentage(studyPlan.getProgressPercentage())
                .completedMinutes(studyPlan.getCompletedMinutes())
                .targetMinutes(studyPlan.getTargetMinutes())
                .averageAccuracy(studyPlan.getAverageAccuracy())
                .lastStudyTime(studyPlan.getLastStudyTime())
                .consecutiveDays(studyPlan.getConsecutiveDays())
                .remainingDays(studyPlan.getRemainingDays())
                .expired(studyPlan.isExpired())
                .createdAt(studyPlan.getCreatedAt())
                .updatedAt(studyPlan.getUpdatedAt())
                .build();
    }

    /**
     * 获取计划类型描述
     */
    public String getPlanTypeDescription() {
        return planType != null ? planType.getDescription() : "";
    }

    /**
     * 获取状态描述
     */
    public String getStatusDescription() {
        return status != null ? status.getDescription() : "";
    }

    /**
     * 获取进度描述
     */
    public String getProgressDescription() {
        if (progressPercentage == null) {
            return "0%";
        }
        return progressPercentage.setScale(1, BigDecimal.ROUND_HALF_UP) + "%";
    }

    /**
     * 检查是否活跃
     */
    public boolean isActive() {
        return status != null && status.isActive();
    }

    /**
     * 检查是否完成
     */
    public boolean isCompleted() {
        return status != null && status.isCompleted();
    }

}
