package com.chiron.smartlearnsystem.learning.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 学习阶段类型枚举
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum StageType {

    /**
     * 基础学习阶段
     */
    FOUNDATION("FOUNDATION", "基础学习", "学习基础知识点和概念"),

    /**
     * 强化练习阶段
     */
    PRACTICE("PRACTICE", "强化练习", "通过大量练习巩固知识"),

    /**
     * 专项突破阶段
     */
    BREAKTHROUGH("BREAKTHROUGH", "专项突破", "针对薄弱点进行专项训练"),

    /**
     * 综合提升阶段
     */
    COMPREHENSIVE("COMPREHENSIVE", "综合提升", "综合运用所学知识"),

    /**
     * 模拟考试阶段
     */
    SIMULATION("SIMULATION", "模拟考试", "通过模拟考试检验学习效果"),

    /**
     * 冲刺复习阶段
     */
    SPRINT_REVIEW("SPRINT_REVIEW", "冲刺复习", "考前最后冲刺复习"),

    /**
     * 错题重练阶段
     */
    WRONG_REVIEW("WRONG_REVIEW", "错题重练", "重点复习历史错题"),

    /**
     * 知识巩固阶段
     */
    CONSOLIDATION("CONSOLIDATION", "知识巩固", "巩固已学知识点");

    private final String code;
    private final String name;
    private final String description;

    /**
     * 根据代码获取阶段类型
     */
    public static StageType getByCode(String code) {
        for (StageType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return FOUNDATION; // 默认返回基础学习
    }

    /**
     * 是否为基础阶段
     */
    public boolean isFoundationStage() {
        return this == FOUNDATION;
    }

    /**
     * 是否为练习阶段
     */
    public boolean isPracticeStage() {
        return this == PRACTICE || this == BREAKTHROUGH || this == WRONG_REVIEW;
    }

    /**
     * 是否为考试阶段
     */
    public boolean isExamStage() {
        return this == SIMULATION;
    }

    /**
     * 是否为复习阶段
     */
    public boolean isReviewStage() {
        return this == SPRINT_REVIEW || this == CONSOLIDATION || this == WRONG_REVIEW;
    }

    /**
     * 获取推荐学习强度
     */
    public double getRecommendedIntensity() {
        switch (this) {
            case FOUNDATION:
                return 0.7; // 基础阶段强度较低
            case PRACTICE:
                return 1.0; // 标准强度
            case BREAKTHROUGH:
                return 1.3; // 专项突破强度较高
            case COMPREHENSIVE:
                return 1.1; // 综合提升中等强度
            case SIMULATION:
                return 1.2; // 模拟考试强度较高
            case SPRINT_REVIEW:
                return 1.5; // 冲刺阶段最高强度
            case WRONG_REVIEW:
                return 1.1; // 错题复习中等强度
            case CONSOLIDATION:
                return 0.8; // 巩固阶段强度较低
            default:
                return 1.0;
        }
    }

    /**
     * 获取推荐持续时间（天）
     */
    public int getRecommendedDuration() {
        switch (this) {
            case FOUNDATION:
                return 21; // 基础学习3周
            case PRACTICE:
                return 14; // 强化练习2周
            case BREAKTHROUGH:
                return 10; // 专项突破10天
            case COMPREHENSIVE:
                return 14; // 综合提升2周
            case SIMULATION:
                return 7; // 模拟考试1周
            case SPRINT_REVIEW:
                return 7; // 冲刺复习1周
            case WRONG_REVIEW:
                return 5; // 错题重练5天
            case CONSOLIDATION:
                return 7; // 知识巩固1周
            default:
                return 14;
        }
    }

}
