package com.chiron.smartlearnsystem.study.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 学习计划类型枚举
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum PlanType {

    /**
     * 系统推荐计划
     */
    SYSTEM_RECOMMENDED("SYSTEM_RECOMMENDED", "系统推荐", "基于用户水平和目标自动生成的学习计划"),

    /**
     * 自定义计划
     */
    CUSTOM("CUSTOM", "自定义计划", "用户自己创建的个性化学习计划"),

    /**
     * 考前冲刺计划
     */
    EXAM_SPRINT("EXAM_SPRINT", "考前冲刺", "针对即将到来的考试制定的强化学习计划"),

    /**
     * 薄弱点强化计划
     */
    WEAKNESS_IMPROVEMENT("WEAKNESS_IMPROVEMENT", "薄弱点强化", "针对用户薄弱知识点的专项提升计划"),

    /**
     * 基础巩固计划
     */
    FOUNDATION_BUILDING("FOUNDATION_BUILDING", "基础巩固", "从基础开始系统性学习的计划"),

    /**
     * 进阶提升计划
     */
    ADVANCED_IMPROVEMENT("ADVANCED_IMPROVEMENT", "进阶提升", "在已有基础上进一步提升的计划"),

    /**
     * 错题重练计划
     */
    WRONG_QUESTION_REVIEW("WRONG_QUESTION_REVIEW", "错题重练", "基于历史错题的复习计划"),

    /**
     * AI智能计划
     */
    AI_INTELLIGENT("AI_INTELLIGENT", "AI智能计划", "基于AI分析生成的个性化学习计划");

    private final String code;
    private final String name;
    private final String description;

    /**
     * 根据代码获取计划类型
     */
    public static PlanType getByCode(String code) {
        for (PlanType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return CUSTOM; // 默认返回自定义计划
    }

    /**
     * 是否为系统生成的计划
     */
    public boolean isSystemGenerated() {
        return this == SYSTEM_RECOMMENDED || this == AI_INTELLIGENT;
    }

    /**
     * 是否为专项计划
     */
    public boolean isSpecializedPlan() {
        return this == EXAM_SPRINT || this == WEAKNESS_IMPROVEMENT || 
               this == WRONG_QUESTION_REVIEW;
    }

    /**
     * 是否需要AI支持
     */
    public boolean needsAISupport() {
        return this == AI_INTELLIGENT || this == SYSTEM_RECOMMENDED || 
               this == WEAKNESS_IMPROVEMENT;
    }

    /**
     * 获取推荐学习强度
     */
    public double getRecommendedIntensity() {
        switch (this) {
            case EXAM_SPRINT:
                return 1.5; // 高强度
            case WEAKNESS_IMPROVEMENT:
                return 1.2; // 中高强度
            case AI_INTELLIGENT:
            case SYSTEM_RECOMMENDED:
                return 1.0; // 标准强度
            case FOUNDATION_BUILDING:
                return 0.8; // 中等强度
            case CUSTOM:
            case ADVANCED_IMPROVEMENT:
            case WRONG_QUESTION_REVIEW:
                return 1.0; // 标准强度
            default:
                return 1.0;
        }
    }

}
