package com.chiron.smartlearnsystem.learning.entity;

import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import com.chiron.smartlearnsystem.common.database.entity.BaseEntity;
import com.chiron.smartlearnsystem.learning.enums.SessionStatus;
import com.chiron.smartlearnsystem.learning.enums.SessionType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

/**
 * 学习会话实体
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Entity
@Table(name = "study_sessions", indexes = {
    @Index(name = "idx_user_date", columnList = "user_id, start_time"),
    @Index(name = "idx_plan_stage", columnList = "plan_id, stage_id"),
    @Index(name = "idx_status", columnList = "status"),
    @Index(name = "idx_session_type", columnList = "session_type")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StudySession extends BaseEntity {

    /**
     * 用户ID
     */
    @Column(name = "user_id", nullable = false)
    private Long userId;

    /**
     * 学习计划ID
     */
    @Column(name = "plan_id")
    private Long planId;

    /**
     * 学习阶段ID
     */
    @Column(name = "stage_id")
    private Long stageId;

    /**
     * 会话类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "session_type", nullable = false, length = 20)
    private SessionType sessionType;

    /**
     * 会话状态
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private SessionStatus status = SessionStatus.ACTIVE;

    /**
     * 开始时间
     */
    @Column(name = "start_time", nullable = false)
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @Column(name = "end_time")
    private LocalDateTime endTime;

    /**
     * 计划学习时长（分钟）
     */
    @Column(name = "planned_minutes")
    private Integer plannedMinutes;

    /**
     * 实际学习时长（分钟）
     */
    @Column(name = "actual_minutes")
    private Integer actualMinutes;

    /**
     * 有效学习时长（分钟）
     */
    @Column(name = "effective_minutes")
    private Integer effectiveMinutes;

    /**
     * 科目代码
     */
    @Column(name = "subject_code", length = 20)
    private String subjectCode;

    /**
     * 学习内容（JSON格式）
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "study_content", columnDefinition = "JSON")
    private String studyContent;

    /**
     * 完成题目数量
     */
    @Column(name = "questions_completed", nullable = false)
    private Integer questionsCompleted = 0;

    /**
     * 正确题目数量
     */
    @Column(name = "questions_correct", nullable = false)
    private Integer questionsCorrect = 0;

    /**
     * 正确率
     */
    @Column(name = "accuracy_rate", precision = 5, scale = 4)
    private BigDecimal accuracyRate;

    /**
     * 专注度评分（1-10）
     */
    @Column(name = "focus_score", precision = 3, scale = 1)
    private BigDecimal focusScore;

    /**
     * 学习效率评分（1-10）
     */
    @Column(name = "efficiency_score", precision = 3, scale = 1)
    private BigDecimal efficiencyScore;

    /**
     * 中断次数
     */
    @Column(name = "interruption_count", nullable = false)
    private Integer interruptionCount = 0;

    /**
     * 暂停总时长（分钟）
     */
    @Column(name = "pause_minutes", nullable = false)
    private Integer pauseMinutes = 0;

    /**
     * 学习环境信息
     */
    @Column(name = "environment_info", columnDefinition = "TEXT")
    private String environmentInfo;

    /**
     * 设备信息
     */
    @Column(name = "device_info", columnDefinition = "TEXT")
    private String deviceInfo;

    /**
     * IP地址
     */
    @Column(name = "ip_address", length = 50)
    private String ipAddress;

    /**
     * 学习笔记
     */
    @Column(name = "study_notes", columnDefinition = "TEXT")
    private String studyNotes;

    /**
     * 会话总结
     */
    @Column(name = "session_summary", columnDefinition = "TEXT")
    private String sessionSummary;

    /**
     * 涉及的知识点
     */
    @ElementCollection
    @CollectionTable(
        name = "study_session_knowledge_points",
        joinColumns = @JoinColumn(name = "session_id")
    )
    @Column(name = "knowledge_point")
    private Set<String> knowledgePoints = new HashSet<>();

    /**
     * 关联的学习计划
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "plan_id", insertable = false, updatable = false)
    private StudyPlan studyPlan;

    /**
     * 关联的学习阶段
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "stage_id", insertable = false, updatable = false)
    private StudyStage studyStage;

    /**
     * 结束会话
     */
    public void endSession() {
        this.endTime = LocalDateTime.now();
        this.status = SessionStatus.COMPLETED;
        calculateActualMinutes();
        calculateAccuracyRate();
        calculateEfficiencyScore();
    }

    /**
     * 暂停会话
     */
    public void pauseSession() {
        this.status = SessionStatus.PAUSED;
    }

    /**
     * 恢复会话
     */
    public void resumeSession() {
        this.status = SessionStatus.ACTIVE;
    }

    /**
     * 计算实际学习时长
     */
    private void calculateActualMinutes() {
        if (startTime != null && endTime != null) {
            long totalMinutes = java.time.Duration.between(startTime, endTime).toMinutes();
            this.actualMinutes = (int) totalMinutes;
            this.effectiveMinutes = this.actualMinutes - this.pauseMinutes;
        }
    }

    /**
     * 计算正确率
     */
    private void calculateAccuracyRate() {
        if (questionsCompleted > 0) {
            this.accuracyRate = BigDecimal.valueOf(questionsCorrect)
                    .divide(BigDecimal.valueOf(questionsCompleted), 4, BigDecimal.ROUND_HALF_UP);
        }
    }

    /**
     * 计算学习效率评分
     */
    private void calculateEfficiencyScore() {
        if (effectiveMinutes != null && effectiveMinutes > 0 && questionsCompleted > 0) {
            // 基于题目完成数量和有效时长计算效率
            double questionsPerMinute = (double) questionsCompleted / effectiveMinutes;
            // 简单的效率评分算法，可以根据实际情况调整
            this.efficiencyScore = BigDecimal.valueOf(Math.min(10.0, questionsPerMinute * 100))
                    .setScale(1, BigDecimal.ROUND_HALF_UP);
        }
    }

    /**
     * 增加答题统计
     */
    public void addQuestionResult(boolean isCorrect) {
        this.questionsCompleted++;
        if (isCorrect) {
            this.questionsCorrect++;
        }
        calculateAccuracyRate();
    }

    /**
     * 增加中断次数
     */
    public void addInterruption() {
        this.interruptionCount++;
    }

    /**
     * 增加暂停时长
     */
    public void addPauseTime(Integer minutes) {
        this.pauseMinutes += minutes;
    }

    /**
     * 检查是否活跃
     */
    public boolean isActive() {
        return SessionStatus.ACTIVE.equals(this.status);
    }

    /**
     * 检查是否已完成
     */
    public boolean isCompleted() {
        return SessionStatus.COMPLETED.equals(this.status);
    }

    /**
     * 获取学习时长（分钟）
     */
    public Integer getStudyDuration() {
        return effectiveMinutes != null ? effectiveMinutes : 0;
    }

}
