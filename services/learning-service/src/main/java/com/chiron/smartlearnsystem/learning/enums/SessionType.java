package com.chiron.smartlearnsystem.study.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 学习会话类型枚举
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum SessionType {

    /**
     * 计划学习
     */
    PLANNED_STUDY("PLANNED_STUDY", "计划学习", "按照学习计划进行的学习"),

    /**
     * 自由学习
     */
    FREE_STUDY("FREE_STUDY", "自由学习", "用户自主安排的学习"),

    /**
     * 专项练习
     */
    FOCUSED_PRACTICE("FOCUSED_PRACTICE", "专项练习", "针对特定知识点的练习"),

    /**
     * 错题复习
     */
    WRONG_REVIEW("WRONG_REVIEW", "错题复习", "复习历史错题"),

    /**
     * 模拟考试
     */
    MOCK_EXAM("MOCK_EXAM", "模拟考试", "模拟考试练习"),

    /**
     * 知识巩固
     */
    KNOWLEDGE_REVIEW("KNOWLEDGE_REVIEW", "知识巩固", "巩固已学知识点"),

    /**
     * 冲刺训练
     */
    SPRINT_TRAINING("SPRINT_TRAINING", "冲刺训练", "考前冲刺训练");

    private final String code;
    private final String name;
    private final String description;

    /**
     * 根据代码获取会话类型
     */
    public static SessionType getByCode(String code) {
        for (SessionType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return FREE_STUDY; // 默认返回自由学习
    }

    /**
     * 是否为计划内学习
     */
    public boolean isPlannedStudy() {
        return this == PLANNED_STUDY;
    }

    /**
     * 是否为练习类型
     */
    public boolean isPracticeType() {
        return this == FOCUSED_PRACTICE || this == WRONG_REVIEW || this == SPRINT_TRAINING;
    }

    /**
     * 是否为考试类型
     */
    public boolean isExamType() {
        return this == MOCK_EXAM;
    }

    /**
     * 是否为复习类型
     */
    public boolean isReviewType() {
        return this == WRONG_REVIEW || this == KNOWLEDGE_REVIEW;
    }

    /**
     * 获取推荐学习时长（分钟）
     */
    public int getRecommendedDuration() {
        switch (this) {
            case PLANNED_STUDY:
                return 60; // 计划学习1小时
            case FREE_STUDY:
                return 45; // 自由学习45分钟
            case FOCUSED_PRACTICE:
                return 30; // 专项练习30分钟
            case WRONG_REVIEW:
                return 25; // 错题复习25分钟
            case MOCK_EXAM:
                return 150; // 模拟考试2.5小时
            case KNOWLEDGE_REVIEW:
                return 20; // 知识巩固20分钟
            case SPRINT_TRAINING:
                return 90; // 冲刺训练1.5小时
            default:
                return 45;
        }
    }

    /**
     * 获取学习强度系数
     */
    public double getIntensityFactor() {
        switch (this) {
            case PLANNED_STUDY:
                return 1.0; // 标准强度
            case FREE_STUDY:
                return 0.8; // 较低强度
            case FOCUSED_PRACTICE:
                return 1.2; // 较高强度
            case WRONG_REVIEW:
                return 1.1; // 中等强度
            case MOCK_EXAM:
                return 1.3; // 高强度
            case KNOWLEDGE_REVIEW:
                return 0.7; // 低强度
            case SPRINT_TRAINING:
                return 1.5; // 最高强度
            default:
                return 1.0;
        }
    }

}
