package com.chiron.smartlearnsystem.learning.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 学习阶段状态枚举
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum StageStatus {

    /**
     * 待开始
     */
    PENDING("PENDING", "待开始", "阶段等待开始"),

    /**
     * 进行中
     */
    IN_PROGRESS("IN_PROGRESS", "进行中", "阶段正在进行"),

    /**
     * 已完成
     */
    COMPLETED("COMPLETED", "已完成", "阶段已完成"),

    /**
     * 已跳过
     */
    SKIPPED("SKIPPED", "已跳过", "阶段被跳过"),

    /**
     * 已暂停
     */
    PAUSED("PAUSED", "已暂停", "阶段暂时停止"),

    /**
     * 已取消
     */
    CANCELLED("CANCELLED", "已取消", "阶段被取消");

    private final String code;
    private final String name;
    private final String description;

    /**
     * 根据代码获取阶段状态
     */
    public static StageStatus getByCode(String code) {
        for (StageStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return PENDING; // 默认返回待开始
    }

    /**
     * 是否为活跃状态
     */
    public boolean isActive() {
        return this == IN_PROGRESS;
    }

    /**
     * 是否为完成状态
     */
    public boolean isCompleted() {
        return this == COMPLETED;
    }

    /**
     * 是否为最终状态
     */
    public boolean isFinalStatus() {
        return this == COMPLETED || this == SKIPPED || this == CANCELLED;
    }

    /**
     * 是否可以开始
     */
    public boolean canStart() {
        return this == PENDING || this == PAUSED;
    }

    /**
     * 是否可以暂停
     */
    public boolean canPause() {
        return this == IN_PROGRESS;
    }

    /**
     * 是否可以恢复
     */
    public boolean canResume() {
        return this == PAUSED;
    }

    /**
     * 是否可以跳过
     */
    public boolean canSkip() {
        return this == PENDING || this == IN_PROGRESS || this == PAUSED;
    }

    /**
     * 是否可以取消
     */
    public boolean canCancel() {
        return this == PENDING || this == IN_PROGRESS || this == PAUSED;
    }

}
