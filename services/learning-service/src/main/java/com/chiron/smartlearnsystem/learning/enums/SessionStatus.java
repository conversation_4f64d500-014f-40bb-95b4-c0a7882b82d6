package com.chiron.smartlearnsystem.learning.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 学习会话状态枚举
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum SessionStatus {

    /**
     * 活跃状态
     */
    ACTIVE("ACTIVE", "进行中", "学习会话正在进行"),

    /**
     * 暂停状态
     */
    PAUSED("PAUSED", "已暂停", "学习会话暂时停止"),

    /**
     * 已完成
     */
    COMPLETED("COMPLETED", "已完成", "学习会话正常完成"),

    /**
     * 已中断
     */
    INTERRUPTED("INTERRUPTED", "已中断", "学习会话异常中断"),

    /**
     * 已取消
     */
    CANCELLED("CANCELLED", "已取消", "学习会话被取消"),

    /**
     * 超时
     */
    TIMEOUT("TIMEOUT", "超时", "学习会话超时结束");

    private final String code;
    private final String name;
    private final String description;

    /**
     * 根据代码获取会话状态
     */
    public static SessionStatus getByCode(String code) {
        for (SessionStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return ACTIVE; // 默认返回活跃状态
    }

    /**
     * 是否为活跃状态
     */
    public boolean isActive() {
        return this == ACTIVE;
    }

    /**
     * 是否为暂停状态
     */
    public boolean isPaused() {
        return this == PAUSED;
    }

    /**
     * 是否为完成状态
     */
    public boolean isCompleted() {
        return this == COMPLETED;
    }

    /**
     * 是否为最终状态
     */
    public boolean isFinalStatus() {
        return this == COMPLETED || this == INTERRUPTED || this == CANCELLED || this == TIMEOUT;
    }

    /**
     * 是否可以暂停
     */
    public boolean canPause() {
        return this == ACTIVE;
    }

    /**
     * 是否可以恢复
     */
    public boolean canResume() {
        return this == PAUSED;
    }

    /**
     * 是否可以结束
     */
    public boolean canEnd() {
        return this == ACTIVE || this == PAUSED;
    }

    /**
     * 是否可以取消
     */
    public boolean canCancel() {
        return this == ACTIVE || this == PAUSED;
    }

    /**
     * 是否为正常结束
     */
    public boolean isNormalEnd() {
        return this == COMPLETED;
    }

    /**
     * 是否为异常结束
     */
    public boolean isAbnormalEnd() {
        return this == INTERRUPTED || this == CANCELLED || this == TIMEOUT;
    }

}
