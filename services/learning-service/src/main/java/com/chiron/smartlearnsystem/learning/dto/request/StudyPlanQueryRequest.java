package com.chiron.smartlearnsystem.study.dto.request;

import com.chiron.smartlearnsystem.study.enums.PlanStatus;
import com.chiron.smartlearnsystem.study.enums.PlanType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;

/**
 * 学习计划查询请求
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StudyPlanQueryRequest {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 页码
     */
    @Min(value = 1, message = "页码不能小于1")
    private Integer page = 1;

    /**
     * 页大小
     */
    @Min(value = 1, message = "页大小不能小于1")
    @Max(value = 100, message = "页大小不能大于100")
    private Integer size = 20;

    /**
     * 科目代码
     */
    private String subjectCode;

    /**
     * 计划类型
     */
    private PlanType planType;

    /**
     * 计划状态
     */
    private PlanStatus status;

    /**
     * 关键字搜索
     */
    private String keyword;

    /**
     * 是否只查询活跃计划
     */
    private Boolean onlyActive = false;

    /**
     * 排序字段
     */
    private String sortBy = "lastStudyTime";

    /**
     * 排序方向
     */
    private String sortDirection = "DESC";

}
