package com.chiron.smartlearnsystem.study.entity;

import com.chiron.smartlearnsystem.common.core.enums.Difficulty;
import com.chiron.smartlearnsystem.common.database.entity.BaseEntity;
import com.chiron.smartlearnsystem.study.enums.PlanStatus;
import com.chiron.smartlearnsystem.study.enums.PlanType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 学习计划实体
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Entity
@Table(name = "study_plans", indexes = {
    @Index(name = "idx_user_subject", columnList = "user_id, subject_code"),
    @Index(name = "idx_status", columnList = "status"),
    @Index(name = "idx_target_date", columnList = "target_exam_date"),
    @Index(name = "idx_created_at", columnList = "created_at")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StudyPlan extends BaseEntity {

    /**
     * 用户ID
     */
    @Column(name = "user_id", nullable = false)
    private Long userId;

    /**
     * 计划名称
     */
    @Column(nullable = false, length = 200)
    private String name;

    /**
     * 计划描述
     */
    @Column(columnDefinition = "TEXT")
    private String description;

    /**
     * 科目代码
     */
    @Column(name = "subject_code", nullable = false, length = 20)
    private String subjectCode;

    /**
     * 计划类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "plan_type", nullable = false, length = 20)
    private PlanType planType;

    /**
     * 计划状态
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private PlanStatus status = PlanStatus.ACTIVE;

    /**
     * 目标考试日期
     */
    @Column(name = "target_exam_date")
    private LocalDate targetExamDate;

    /**
     * 计划开始日期
     */
    @Column(name = "start_date", nullable = false)
    private LocalDate startDate;

    /**
     * 计划结束日期
     */
    @Column(name = "end_date")
    private LocalDate endDate;

    /**
     * 每日学习时长目标（分钟）
     */
    @Column(name = "daily_study_minutes", nullable = false)
    private Integer dailyStudyMinutes = 60;

    /**
     * 每周学习天数目标
     */
    @Column(name = "weekly_study_days", nullable = false)
    private Integer weeklyStudyDays = 5;

    /**
     * 目标难度
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "target_difficulty", nullable = false)
    private Difficulty targetDifficulty = Difficulty.MEDIUM;

    /**
     * 学习重点配置（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "study_focus", columnDefinition = "JSON")
    private String studyFocus;

    /**
     * 知识点权重配置（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "knowledge_weights", columnDefinition = "JSON")
    private String knowledgeWeights;

    /**
     * 总进度百分比
     */
    @Column(name = "progress_percentage", precision = 5, scale = 2)
    private BigDecimal progressPercentage = BigDecimal.ZERO;

    /**
     * 已完成学习时长（分钟）
     */
    @Column(name = "completed_minutes", nullable = false)
    private Integer completedMinutes = 0;

    /**
     * 目标学习时长（分钟）
     */
    @Column(name = "target_minutes")
    private Integer targetMinutes;

    /**
     * 已完成题目数量
     */
    @Column(name = "completed_questions", nullable = false)
    private Integer completedQuestions = 0;

    /**
     * 目标题目数量
     */
    @Column(name = "target_questions")
    private Integer targetQuestions;

    /**
     * 平均正确率
     */
    @Column(name = "average_accuracy", precision = 5, scale = 4)
    private BigDecimal averageAccuracy;

    /**
     * 最后学习时间
     */
    @Column(name = "last_study_time")
    private LocalDateTime lastStudyTime;

    /**
     * 连续学习天数
     */
    @Column(name = "consecutive_days", nullable = false)
    private Integer consecutiveDays = 0;

    /**
     * 是否启用智能调整
     */
    @Column(name = "auto_adjust_enabled", nullable = false)
    private Boolean autoAdjustEnabled = true;

    /**
     * 提醒设置（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "reminder_settings", columnDefinition = "JSON")
    private String reminderSettings;

    /**
     * 目标知识点
     */
    @ElementCollection
    @CollectionTable(
        name = "study_plan_knowledge_points",
        joinColumns = @JoinColumn(name = "plan_id")
    )
    @Column(name = "knowledge_point")
    private Set<String> targetKnowledgePoints = new HashSet<>();

    /**
     * 学习阶段
     */
    @OneToMany(mappedBy = "studyPlan", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<StudyStage> studyStages = new ArrayList<>();

    /**
     * 更新进度
     */
    public void updateProgress() {
        if (targetMinutes != null && targetMinutes > 0) {
            this.progressPercentage = BigDecimal.valueOf(completedMinutes)
                    .divide(BigDecimal.valueOf(targetMinutes), 4, BigDecimal.ROUND_HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
        }
        
        if (this.progressPercentage.compareTo(BigDecimal.valueOf(100)) >= 0) {
            this.status = PlanStatus.COMPLETED;
        }
    }

    /**
     * 增加学习时长
     */
    public void addStudyTime(Integer minutes) {
        this.completedMinutes += minutes;
        this.lastStudyTime = LocalDateTime.now();
        updateProgress();
    }

    /**
     * 增加完成题目数
     */
    public void addCompletedQuestions(Integer count) {
        this.completedQuestions += count;
    }

    /**
     * 更新平均正确率
     */
    public void updateAverageAccuracy(BigDecimal newAccuracy) {
        if (this.averageAccuracy == null) {
            this.averageAccuracy = newAccuracy;
        } else {
            // 简单的移动平均
            this.averageAccuracy = this.averageAccuracy
                    .multiply(BigDecimal.valueOf(0.8))
                    .add(newAccuracy.multiply(BigDecimal.valueOf(0.2)));
        }
    }

    /**
     * 检查是否过期
     */
    public boolean isExpired() {
        return endDate != null && LocalDate.now().isAfter(endDate);
    }

    /**
     * 检查是否活跃
     */
    public boolean isActive() {
        return PlanStatus.ACTIVE.equals(this.status);
    }

    /**
     * 获取剩余天数
     */
    public long getRemainingDays() {
        if (endDate == null) {
            return -1;
        }
        return java.time.temporal.ChronoUnit.DAYS.between(LocalDate.now(), endDate);
    }

}
