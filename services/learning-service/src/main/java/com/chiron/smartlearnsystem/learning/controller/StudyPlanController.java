package com.chiron.smartlearnsystem.study.controller;

import com.chiron.smartlearnsystem.common.core.domain.ApiResponse;
import com.chiron.smartlearnsystem.common.core.domain.PageResult;
import com.chiron.smartlearnsystem.study.dto.StudyPlanDTO;
import com.chiron.smartlearnsystem.study.dto.request.StudyPlanCreateRequest;
import com.chiron.smartlearnsystem.study.dto.request.StudyPlanQueryRequest;
import com.chiron.smartlearnsystem.study.dto.request.StudyPlanUpdateRequest;
import com.chiron.smartlearnsystem.study.service.StudyPlanService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * 学习计划控制器
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/study-plans")
@RequiredArgsConstructor
@Validated
public class StudyPlanController {

    private final StudyPlanService studyPlanService;

    /**
     * 创建学习计划
     */
    @PostMapping
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<StudyPlanDTO>> createStudyPlan(
            @Valid @RequestBody StudyPlanCreateRequest request) {
        
        log.info("创建学习计划请求: {}", request);
        
        // 验证日期逻辑
        if (!request.isValidDateRange()) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.fail("结束日期不能早于开始日期"));
        }
        
        if (!request.isValidExamDate()) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.fail("考试日期不能早于计划结束日期"));
        }
        
        StudyPlanDTO studyPlanDTO = studyPlanService.createStudyPlan(request);
        
        return ResponseEntity.ok(ApiResponse.success("学习计划创建成功", studyPlanDTO));
    }

    /**
     * 更新学习计划
     */
    @PutMapping("/{planId}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<StudyPlanDTO>> updateStudyPlan(
            @PathVariable Long planId,
            @Valid @RequestBody StudyPlanUpdateRequest request) {
        
        log.info("更新学习计划请求: planId={}", planId);
        
        StudyPlanDTO studyPlanDTO = studyPlanService.updateStudyPlan(planId, request);
        
        return ResponseEntity.ok(ApiResponse.success("学习计划更新成功", studyPlanDTO));
    }

    /**
     * 获取学习计划详情
     */
    @GetMapping("/{planId}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<StudyPlanDTO>> getStudyPlan(@PathVariable Long planId) {
        
        log.info("获取学习计划详情: planId={}", planId);
        
        StudyPlanDTO studyPlanDTO = studyPlanService.getStudyPlanById(planId);
        
        return ResponseEntity.ok(ApiResponse.success("获取学习计划成功", studyPlanDTO));
    }

    /**
     * 分页查询学习计划
     */
    @GetMapping
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<PageResult<StudyPlanDTO>>> getStudyPlans(
            @Valid StudyPlanQueryRequest request) {
        
        log.info("分页查询学习计划: {}", request);
        
        PageResult<StudyPlanDTO> result = studyPlanService.getStudyPlans(request);
        
        return ResponseEntity.ok(ApiResponse.success("查询学习计划成功", result));
    }

    /**
     * 获取用户活跃的学习计划
     */
    @GetMapping("/active")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<StudyPlanDTO>>> getActiveStudyPlans(
            @RequestParam Long userId) {
        
        log.info("获取用户活跃学习计划: userId={}", userId);
        
        List<StudyPlanDTO> activePlans = studyPlanService.getActiveStudyPlans(userId);
        
        return ResponseEntity.ok(ApiResponse.success("获取活跃学习计划成功", activePlans));
    }

    /**
     * 开始学习计划
     */
    @PostMapping("/{planId}/start")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Void>> startStudyPlan(@PathVariable Long planId) {
        
        log.info("开始学习计划: planId={}", planId);
        
        studyPlanService.startStudyPlan(planId);
        
        return ResponseEntity.ok(ApiResponse.success("学习计划已开始"));
    }

    /**
     * 暂停学习计划
     */
    @PostMapping("/{planId}/pause")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Void>> pauseStudyPlan(@PathVariable Long planId) {
        
        log.info("暂停学习计划: planId={}", planId);
        
        studyPlanService.pauseStudyPlan(planId);
        
        return ResponseEntity.ok(ApiResponse.success("学习计划已暂停"));
    }

    /**
     * 完成学习计划
     */
    @PostMapping("/{planId}/complete")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Void>> completeStudyPlan(@PathVariable Long planId) {
        
        log.info("完成学习计划: planId={}", planId);
        
        studyPlanService.completeStudyPlan(planId);
        
        return ResponseEntity.ok(ApiResponse.success("学习计划已完成"));
    }

    /**
     * 记录学习进度
     */
    @PostMapping("/{planId}/progress")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Void>> recordStudyProgress(
            @PathVariable Long planId,
            @RequestParam Integer studyMinutes,
            @RequestParam(defaultValue = "0") Integer questionsCompleted,
            @RequestParam(required = false) BigDecimal accuracy) {
        
        log.debug("记录学习进度: planId={}, minutes={}, questions={}, accuracy={}", 
                planId, studyMinutes, questionsCompleted, accuracy);
        
        studyPlanService.recordStudyProgress(planId, studyMinutes, questionsCompleted, accuracy);
        
        return ResponseEntity.ok(ApiResponse.success("学习进度记录成功"));
    }

    /**
     * 删除学习计划
     */
    @DeleteMapping("/{planId}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Void>> deleteStudyPlan(@PathVariable Long planId) {
        
        log.info("删除学习计划: planId={}", planId);
        
        // TODO: 实现软删除逻辑
        
        return ResponseEntity.ok(ApiResponse.success("学习计划删除成功"));
    }

}
