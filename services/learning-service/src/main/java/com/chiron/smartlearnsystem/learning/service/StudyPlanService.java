package com.chiron.smartlearnsystem.learning.service;

import com.chiron.smartlearnsystem.common.core.domain.PageResult;
import com.chiron.smartlearnsystem.common.core.exception.BusinessException;
import com.chiron.smartlearnsystem.learning.dto.StudyPlanDTO;
import com.chiron.smartlearnsystem.learning.dto.request.StudyPlanCreateRequest;
import com.chiron.smartlearnsystem.learning.dto.request.StudyPlanQueryRequest;
import com.chiron.smartlearnsystem.learning.dto.request.StudyPlanUpdateRequest;
import com.chiron.smartlearnsystem.learning.entity.StudyPlan;
import com.chiron.smartlearnsystem.learning.entity.StudyStage;
import com.chiron.smartlearnsystem.learning.enums.PlanStatus;
import com.chiron.smartlearnsystem.learning.enums.PlanType;
import com.chiron.smartlearnsystem.learning.enums.StageStatus;
import com.chiron.smartlearnsystem.learning.enums.StageType;
import com.chiron.smartlearnsystem.learning.repository.StudyPlanRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 学习计划服务
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class StudyPlanService {

    private final StudyPlanRepository studyPlanRepository;
    private final RedisTemplate<String, Object> redisTemplate;

    private static final String STUDY_PLAN_CACHE_KEY = "study:plan:";
    private static final String USER_PLANS_CACHE_KEY = "study:user:plans:";
    private static final int CACHE_EXPIRE_HOURS = 2;

    /**
     * 创建学习计划
     */
    public StudyPlanDTO createStudyPlan(StudyPlanCreateRequest request) {
        log.info("创建学习计划: userId={}, subject={}, type={}", 
                request.getUserId(), request.getSubjectCode(), request.getPlanType());

        // 检查是否已有同科目的活跃计划
        if (studyPlanRepository.hasActiveSubjectPlan(request.getUserId(), request.getSubjectCode())) {
            throw new BusinessException("该科目已有活跃的学习计划");
        }

        // 创建学习计划
        StudyPlan studyPlan = StudyPlan.builder()
                .userId(request.getUserId())
                .name(request.getName())
                .description(request.getDescription())
                .subjectCode(request.getSubjectCode())
                .planType(request.getPlanType())
                .status(PlanStatus.ACTIVE)
                .targetExamDate(request.getTargetExamDate())
                .startDate(request.getStartDate() != null ? request.getStartDate() : LocalDate.now())
                .endDate(request.getEndDate())
                .dailyStudyMinutes(request.getDailyStudyMinutes())
                .weeklyStudyDays(request.getWeeklyStudyDays())
                .targetDifficulty(request.getTargetDifficulty())
                .targetMinutes(request.getTargetMinutes())
                .targetQuestions(request.getTargetQuestions())
                .autoAdjustEnabled(request.getAutoAdjustEnabled())
                .build();

        // 设置目标知识点
        if (request.getTargetKnowledgePoints() != null) {
            studyPlan.getTargetKnowledgePoints().addAll(request.getTargetKnowledgePoints());
        }

        // 保存学习计划
        StudyPlan savedPlan = studyPlanRepository.save(studyPlan);

        // 生成学习阶段
        generateStudyStages(savedPlan);

        // 清除缓存
        clearUserPlansCache(request.getUserId());

        log.info("学习计划创建成功: planId={}", savedPlan.getId());

        return StudyPlanDTO.from(savedPlan);
    }

    /**
     * 更新学习计划
     */
    public StudyPlanDTO updateStudyPlan(Long planId, StudyPlanUpdateRequest request) {
        log.info("更新学习计划: planId={}", planId);

        StudyPlan studyPlan = studyPlanRepository.findById(planId)
                .orElseThrow(() -> new BusinessException("学习计划不存在"));

        // 检查是否可以编辑
        if (!studyPlan.getStatus().canEdit()) {
            throw new BusinessException("当前状态的学习计划不能编辑");
        }

        // 更新计划信息
        if (request.getName() != null) {
            studyPlan.setName(request.getName());
        }
        if (request.getDescription() != null) {
            studyPlan.setDescription(request.getDescription());
        }
        if (request.getTargetExamDate() != null) {
            studyPlan.setTargetExamDate(request.getTargetExamDate());
        }
        if (request.getEndDate() != null) {
            studyPlan.setEndDate(request.getEndDate());
        }
        if (request.getDailyStudyMinutes() != null) {
            studyPlan.setDailyStudyMinutes(request.getDailyStudyMinutes());
        }
        if (request.getWeeklyStudyDays() != null) {
            studyPlan.setWeeklyStudyDays(request.getWeeklyStudyDays());
        }
        if (request.getTargetDifficulty() != null) {
            studyPlan.setTargetDifficulty(request.getTargetDifficulty());
        }
        if (request.getAutoAdjustEnabled() != null) {
            studyPlan.setAutoAdjustEnabled(request.getAutoAdjustEnabled());
        }

        // 更新目标知识点
        if (request.getTargetKnowledgePoints() != null) {
            studyPlan.getTargetKnowledgePoints().clear();
            studyPlan.getTargetKnowledgePoints().addAll(request.getTargetKnowledgePoints());
        }

        StudyPlan updatedPlan = studyPlanRepository.save(studyPlan);

        // 清除缓存
        clearStudyPlanCache(planId);
        clearUserPlansCache(studyPlan.getUserId());

        log.info("学习计划更新成功: planId={}", planId);

        return StudyPlanDTO.from(updatedPlan);
    }

    /**
     * 获取学习计划详情
     */
    @Transactional(readOnly = true)
    public StudyPlanDTO getStudyPlanById(Long planId) {
        // 先从缓存获取
        String cacheKey = STUDY_PLAN_CACHE_KEY + planId;
        StudyPlanDTO cachedPlan = (StudyPlanDTO) redisTemplate.opsForValue().get(cacheKey);
        if (cachedPlan != null) {
            return cachedPlan;
        }

        // 从数据库获取
        StudyPlan studyPlan = studyPlanRepository.findById(planId)
                .orElseThrow(() -> new BusinessException("学习计划不存在"));

        StudyPlanDTO planDTO = StudyPlanDTO.from(studyPlan);

        // 缓存结果
        redisTemplate.opsForValue().set(cacheKey, planDTO, CACHE_EXPIRE_HOURS, TimeUnit.HOURS);

        return planDTO;
    }

    /**
     * 分页查询学习计划
     */
    @Transactional(readOnly = true)
    public PageResult<StudyPlanDTO> getStudyPlans(StudyPlanQueryRequest request) {
        log.info("分页查询学习计划: {}", request);

        // 构建分页参数
        Sort sort = Sort.by(Sort.Direction.DESC, "lastStudyTime", "createdAt");
        Pageable pageable = PageRequest.of(request.getPage() - 1, request.getSize(), sort);

        // 查询学习计划
        Page<StudyPlan> planPage;
        if (request.getKeyword() != null && !request.getKeyword().trim().isEmpty()) {
            planPage = studyPlanRepository.searchPlans(request.getUserId(), request.getKeyword(), pageable);
        } else {
            planPage = studyPlanRepository.findByUserId(request.getUserId(), pageable);
        }

        // 转换为DTO
        List<StudyPlanDTO> planDTOs = planPage.getContent().stream()
                .map(StudyPlanDTO::from)
                .collect(Collectors.toList());

        return PageResult.of(planDTOs, planPage.getTotalElements(), 
                           (long) request.getPage(), (long) request.getSize());
    }

    /**
     * 获取用户活跃的学习计划
     */
    @Transactional(readOnly = true)
    public List<StudyPlanDTO> getActiveStudyPlans(Long userId) {
        log.info("获取用户活跃学习计划: userId={}", userId);

        List<StudyPlan> activePlans = studyPlanRepository.findActiveStudyPlans(userId);

        return activePlans.stream()
                .map(StudyPlanDTO::from)
                .collect(Collectors.toList());
    }

    /**
     * 开始学习计划
     */
    public void startStudyPlan(Long planId) {
        log.info("开始学习计划: planId={}", planId);

        StudyPlan studyPlan = studyPlanRepository.findById(planId)
                .orElseThrow(() -> new BusinessException("学习计划不存在"));

        if (!studyPlan.getStatus().canStart()) {
            throw new BusinessException("当前状态的学习计划不能开始");
        }

        studyPlan.setStatus(PlanStatus.ACTIVE);
        studyPlan.setStartDate(LocalDate.now());

        studyPlanRepository.save(studyPlan);

        // 清除缓存
        clearStudyPlanCache(planId);
        clearUserPlansCache(studyPlan.getUserId());

        log.info("学习计划已开始: planId={}", planId);
    }

    /**
     * 暂停学习计划
     */
    public void pauseStudyPlan(Long planId) {
        log.info("暂停学习计划: planId={}", planId);

        StudyPlan studyPlan = studyPlanRepository.findById(planId)
                .orElseThrow(() -> new BusinessException("学习计划不存在"));

        if (!studyPlan.getStatus().canPause()) {
            throw new BusinessException("当前状态的学习计划不能暂停");
        }

        studyPlan.setStatus(PlanStatus.PAUSED);
        studyPlanRepository.save(studyPlan);

        // 清除缓存
        clearStudyPlanCache(planId);
        clearUserPlansCache(studyPlan.getUserId());

        log.info("学习计划已暂停: planId={}", planId);
    }

    /**
     * 完成学习计划
     */
    public void completeStudyPlan(Long planId) {
        log.info("完成学习计划: planId={}", planId);

        StudyPlan studyPlan = studyPlanRepository.findById(planId)
                .orElseThrow(() -> new BusinessException("学习计划不存在"));

        studyPlan.setStatus(PlanStatus.COMPLETED);
        studyPlan.setProgressPercentage(BigDecimal.valueOf(100));
        studyPlan.setEndDate(LocalDate.now());

        studyPlanRepository.save(studyPlan);

        // 清除缓存
        clearStudyPlanCache(planId);
        clearUserPlansCache(studyPlan.getUserId());

        log.info("学习计划已完成: planId={}", planId);
    }

    /**
     * 记录学习进度
     */
    public void recordStudyProgress(Long planId, Integer studyMinutes, Integer questionsCompleted, 
                                   BigDecimal accuracy) {
        log.debug("记录学习进度: planId={}, minutes={}, questions={}, accuracy={}", 
                planId, studyMinutes, questionsCompleted, accuracy);

        StudyPlan studyPlan = studyPlanRepository.findById(planId)
                .orElseThrow(() -> new BusinessException("学习计划不存在"));

        // 更新学习进度
        studyPlan.addStudyTime(studyMinutes);
        studyPlan.addCompletedQuestions(questionsCompleted);
        
        if (accuracy != null) {
            studyPlan.updateAverageAccuracy(accuracy);
        }

        studyPlanRepository.save(studyPlan);

        // 清除缓存
        clearStudyPlanCache(planId);
    }

    /**
     * 生成学习阶段
     */
    private void generateStudyStages(StudyPlan studyPlan) {
        List<StudyStage> stages = new ArrayList<>();
        
        // 根据计划类型生成不同的阶段
        switch (studyPlan.getPlanType()) {
            case SYSTEM_RECOMMENDED:
            case AI_INTELLIGENT:
                stages = generateSystemRecommendedStages(studyPlan);
                break;
            case EXAM_SPRINT:
                stages = generateExamSprintStages(studyPlan);
                break;
            case WEAKNESS_IMPROVEMENT:
                stages = generateWeaknessImprovementStages(studyPlan);
                break;
            default:
                stages = generateDefaultStages(studyPlan);
                break;
        }

        studyPlan.getStudyStages().addAll(stages);
    }

    /**
     * 生成系统推荐阶段
     */
    private List<StudyStage> generateSystemRecommendedStages(StudyPlan studyPlan) {
        List<StudyStage> stages = new ArrayList<>();
        LocalDate currentDate = studyPlan.getStartDate();

        // 基础学习阶段
        stages.add(createStudyStage(studyPlan, "基础学习", StageType.FOUNDATION, 1, 
                currentDate, currentDate.plusDays(20), 1200, 200));
        currentDate = currentDate.plusDays(21);

        // 强化练习阶段
        stages.add(createStudyStage(studyPlan, "强化练习", StageType.PRACTICE, 2, 
                currentDate, currentDate.plusDays(13), 840, 300));
        currentDate = currentDate.plusDays(14);

        // 综合提升阶段
        stages.add(createStudyStage(studyPlan, "综合提升", StageType.COMPREHENSIVE, 3, 
                currentDate, currentDate.plusDays(13), 840, 200));
        currentDate = currentDate.plusDays(14);

        // 模拟考试阶段
        stages.add(createStudyStage(studyPlan, "模拟考试", StageType.SIMULATION, 4, 
                currentDate, currentDate.plusDays(6), 420, 100));

        return stages;
    }

    /**
     * 生成考前冲刺阶段
     */
    private List<StudyStage> generateExamSprintStages(StudyPlan studyPlan) {
        List<StudyStage> stages = new ArrayList<>();
        LocalDate currentDate = studyPlan.getStartDate();

        // 重点复习阶段
        stages.add(createStudyStage(studyPlan, "重点复习", StageType.SPRINT_REVIEW, 1, 
                currentDate, currentDate.plusDays(6), 630, 150));
        currentDate = currentDate.plusDays(7);

        // 模拟冲刺阶段
        stages.add(createStudyStage(studyPlan, "模拟冲刺", StageType.SIMULATION, 2, 
                currentDate, currentDate.plusDays(6), 630, 100));

        return stages;
    }

    /**
     * 生成薄弱点强化阶段
     */
    private List<StudyStage> generateWeaknessImprovementStages(StudyPlan studyPlan) {
        List<StudyStage> stages = new ArrayList<>();
        LocalDate currentDate = studyPlan.getStartDate();

        // 专项突破阶段
        stages.add(createStudyStage(studyPlan, "专项突破", StageType.BREAKTHROUGH, 1, 
                currentDate, currentDate.plusDays(9), 600, 200));
        currentDate = currentDate.plusDays(10);

        // 错题重练阶段
        stages.add(createStudyStage(studyPlan, "错题重练", StageType.WRONG_REVIEW, 2, 
                currentDate, currentDate.plusDays(4), 300, 100));

        return stages;
    }

    /**
     * 生成默认阶段
     */
    private List<StudyStage> generateDefaultStages(StudyPlan studyPlan) {
        List<StudyStage> stages = new ArrayList<>();
        LocalDate currentDate = studyPlan.getStartDate();

        // 基础学习阶段
        stages.add(createStudyStage(studyPlan, "基础学习", StageType.FOUNDATION, 1, 
                currentDate, currentDate.plusDays(13), 840, 150));
        currentDate = currentDate.plusDays(14);

        // 强化练习阶段
        stages.add(createStudyStage(studyPlan, "强化练习", StageType.PRACTICE, 2, 
                currentDate, currentDate.plusDays(13), 840, 200));

        return stages;
    }

    /**
     * 创建学习阶段
     */
    private StudyStage createStudyStage(StudyPlan studyPlan, String name, StageType stageType, 
                                       Integer order, LocalDate startDate, LocalDate endDate, 
                                       Integer estimatedMinutes, Integer targetQuestions) {
        return StudyStage.builder()
                .planId(studyPlan.getId())
                .name(name)
                .description(stageType.getDescription())
                .stageType(stageType)
                .status(order == 1 ? StageStatus.PENDING : StageStatus.PENDING)
                .stageOrder(order)
                .startDate(startDate)
                .endDate(endDate)
                .estimatedMinutes(estimatedMinutes)
                .targetQuestions(targetQuestions)
                .targetAccuracy(BigDecimal.valueOf(0.7))
                .build();
    }

    /**
     * 清除学习计划缓存
     */
    private void clearStudyPlanCache(Long planId) {
        String cacheKey = STUDY_PLAN_CACHE_KEY + planId;
        redisTemplate.delete(cacheKey);
    }

    /**
     * 清除用户计划列表缓存
     */
    private void clearUserPlansCache(Long userId) {
        String cacheKey = USER_PLANS_CACHE_KEY + userId;
        redisTemplate.delete(cacheKey);
    }

}
