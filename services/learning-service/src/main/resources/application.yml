server:
  port: 8083

spring:
  application:
    name: learning-service
  
  profiles:
    active: dev
  
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *************************************************************************************************************************************************************
    username: root
    password: 123456
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: LearningServiceHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
  
  redis:
    host: localhost
    port: 6379
    password: 
    database: 2
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0
  
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
    validate-on-migrate: true

eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
    register-with-eureka: true
    fetch-registry: true
  instance:
    prefer-ip-address: true
    lease-renewal-interval-in-seconds: 30
    lease-expiration-duration-in-seconds: 90

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true

logging:
  level:
    com.chiron.smartlearnsystem.learning: INFO
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/learning-service.log

# 自定义配置
smartlearn:
  study:
    # 学习计划配置
    plan:
      max-active-plans-per-user: 5
      default-daily-minutes: 60
      default-weekly-days: 5
      auto-adjust-enabled: true
    
    # 学习会话配置
    session:
      max-session-duration: 480  # 最大会话时长（分钟）
      auto-pause-threshold: 30   # 自动暂停阈值（分钟）
      focus-score-threshold: 6.0 # 专注度评分阈值
    
    # 学习进度配置
    progress:
      mastery-threshold: 0.8     # 掌握程度阈值
      weakness-threshold: 0.3    # 薄弱点阈值
      review-interval-base: 1    # 基础复习间隔（天）
      max-review-interval: 30    # 最大复习间隔（天）
    
    # 缓存配置
    cache:
      plan-expire-hours: 2
      progress-expire-hours: 1
      session-expire-hours: 24
