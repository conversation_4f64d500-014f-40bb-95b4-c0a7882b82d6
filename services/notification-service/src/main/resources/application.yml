server:
  port: 8085

spring:
  application:
    name: notification-service
  
  profiles:
    active: dev
  
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *****************************************************************************************************************************************************************
    username: root
    password: 123456
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: NotificationServiceHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
  
  redis:
    host: localhost
    port: 6379
    password: 
    database: 5
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0
  
  mail:
    host: smtp.gmail.com
    port: 587
    username: <EMAIL>
    password: your-email-password
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
  
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
    validate-on-migrate: true

eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
    register-with-eureka: true
    fetch-registry: true
  instance:
    prefer-ip-address: true
    lease-renewal-interval-in-seconds: 30
    lease-expiration-duration-in-seconds: 90

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true

logging:
  level:
    com.chiron.smartlearnsystem.notification: INFO
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/notification-service.log

# 自定义配置
smartlearn:
  notification:
    # 邮件通知配置
    email:
      enabled: true
      from-name: "SmartLearn智能学习系统"
      template-path: "classpath:/templates/email/"
      max-retry: 3
      retry-interval: 300  # 5分钟
    
    # 短信通知配置
    sms:
      enabled: false
      provider: "aliyun"  # aliyun, tencent, huawei
      access-key: "your-access-key"
      secret-key: "your-secret-key"
      sign-name: "SmartLearn"
      max-retry: 3
      retry-interval: 600  # 10分钟
    
    # 推送通知配置
    push:
      enabled: false
      provider: "firebase"  # firebase, jpush, getui
      server-key: "your-server-key"
      max-retry: 3
      retry-interval: 180  # 3分钟
    
    # 微信通知配置
    wechat:
      enabled: false
      app-id: "your-app-id"
      app-secret: "your-app-secret"
      template-id: "your-template-id"
    
    # 通知处理配置
    processing:
      batch-size: 100
      thread-pool-size: 10
      queue-capacity: 1000
      scheduled-interval: 60  # 1分钟
      cleanup-interval: 3600  # 1小时
      retry-interval: 300     # 5分钟
    
    # 通知限制配置
    limits:
      max-notifications-per-user-per-day: 100
      max-notifications-per-user-per-hour: 20
      max-batch-size: 100
      content-max-length: 10000
      title-max-length: 200
    
    # 缓存配置
    cache:
      template-expire-hours: 24
      notification-expire-minutes: 30
      user-notifications-expire-minutes: 30
    
    # 存储配置
    storage:
      retention-days: 90      # 通知保留90天
      cleanup-batch-size: 1000
      archive-enabled: true
      archive-path: "/data/notification-archive"
