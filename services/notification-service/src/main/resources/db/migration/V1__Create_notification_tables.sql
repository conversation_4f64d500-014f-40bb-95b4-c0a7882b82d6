-- 通知消息表
CREATE TABLE notification_messages (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    notification_type VARCHAR(30) NOT NULL COMMENT '通知类型',
    notification_channel VARCHAR(20) NOT NULL COMMENT '通知渠道',
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING' COMMENT '通知状态',
    priority VARCHAR(10) NOT NULL DEFAULT 'NORMAL' COMMENT '优先级',
    title VARCHAR(200) NOT NULL COMMENT '通知标题',
    content TEXT COMMENT '通知内容',
    rich_content TEXT COMMENT '富文本内容',
    notification_data JSON COMMENT '通知数据',
    template_id VARCHAR(50) COMMENT '模板ID',
    template_params JSON COMMENT '模板参数',
    recipient_info JSON COMMENT '收件人信息',
    sender_id BIGINT COMMENT '发送者ID',
    sender_info VARCHAR(200) COMMENT '发送者信息',
    scheduled_time DATETIME COMMENT '计划发送时间',
    sent_time DATETIME COMMENT '实际发送时间',
    read_time DATETIME COMMENT '阅读时间',
    expires_at DATETIME COMMENT '过期时间',
    is_read BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否已读',
    requires_confirmation BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否需要确认',
    confirmed_at DATETIME COMMENT '确认时间',
    retry_count INT NOT NULL DEFAULT 0 COMMENT '重试次数',
    max_retries INT NOT NULL DEFAULT 3 COMMENT '最大重试次数',
    error_message TEXT COMMENT '错误信息',
    external_message_id VARCHAR(100) COMMENT '外部消息ID',
    push_token VARCHAR(500) COMMENT '推送令牌',
    email_address VARCHAR(100) COMMENT '邮件地址',
    phone_number VARCHAR(20) COMMENT '手机号码',
    attachments JSON COMMENT '附件信息',
    click_action JSON COMMENT '点击动作',
    statistics JSON COMMENT '统计信息',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT NOT NULL DEFAULT 0,
    version BIGINT NOT NULL DEFAULT 0,
    
    INDEX idx_user_id (user_id),
    INDEX idx_type (notification_type),
    INDEX idx_channel (notification_channel),
    INDEX idx_status (status),
    INDEX idx_priority (priority),
    INDEX idx_scheduled_time (scheduled_time),
    INDEX idx_created_at (created_at),
    INDEX idx_user_read (user_id, is_read),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知消息表';

-- 通知标签表
CREATE TABLE notification_tags (
    notification_id BIGINT NOT NULL,
    tag VARCHAR(50) NOT NULL,
    PRIMARY KEY (notification_id, tag),
    FOREIGN KEY (notification_id) REFERENCES notification_messages(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知标签表';

-- 通知模板表
CREATE TABLE notification_templates (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    template_code VARCHAR(50) NOT NULL UNIQUE COMMENT '模板代码',
    template_name VARCHAR(100) NOT NULL COMMENT '模板名称',
    template_description TEXT COMMENT '模板描述',
    notification_type VARCHAR(30) NOT NULL COMMENT '通知类型',
    notification_channel VARCHAR(20) NOT NULL COMMENT '通知渠道',
    title_template VARCHAR(200) NOT NULL COMMENT '模板标题',
    content_template TEXT NOT NULL COMMENT '模板内容',
    rich_content_template TEXT COMMENT '富文本模板',
    parameter_definitions JSON COMMENT '模板参数定义',
    default_parameters JSON COMMENT '默认参数值',
    template_language VARCHAR(10) DEFAULT 'zh-CN' COMMENT '模板语言',
    is_enabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    is_system_template BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否为系统模板',
    template_version VARCHAR(20) DEFAULT '1.0' COMMENT '模板版本',
    template_category VARCHAR(50) COMMENT '模板分类',
    style_config JSON COMMENT '样式配置',
    attachment_config JSON COMMENT '附件配置',
    click_action_config JSON COMMENT '点击动作配置',
    send_config JSON COMMENT '发送配置',
    usage_count BIGINT NOT NULL DEFAULT 0 COMMENT '使用次数',
    last_used_at DATETIME COMMENT '最后使用时间',
    created_by BIGINT COMMENT '创建者ID',
    notes TEXT COMMENT '模板备注',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT NOT NULL DEFAULT 0,
    version BIGINT NOT NULL DEFAULT 0,
    
    INDEX idx_template_code (template_code),
    INDEX idx_type (notification_type),
    INDEX idx_channel (notification_channel),
    INDEX idx_enabled (is_enabled),
    INDEX idx_created_at (created_at),
    INDEX idx_usage_count (usage_count),
    INDEX idx_last_used (last_used_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知模板表';

-- 通知模板标签表
CREATE TABLE notification_template_tags (
    template_id BIGINT NOT NULL,
    tag VARCHAR(50) NOT NULL,
    PRIMARY KEY (template_id, tag),
    FOREIGN KEY (template_id) REFERENCES notification_templates(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知模板标签表';

-- 用户通知设置表
CREATE TABLE user_notification_settings (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL UNIQUE COMMENT '用户ID',
    email_enabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT '邮件通知启用',
    sms_enabled BOOLEAN NOT NULL DEFAULT FALSE COMMENT '短信通知启用',
    push_enabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT '推送通知启用',
    in_app_enabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT '应用内通知启用',
    websocket_enabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'WebSocket通知启用',
    notification_types JSON COMMENT '通知类型设置',
    quiet_hours JSON COMMENT '免打扰时间设置',
    frequency_limits JSON COMMENT '频率限制设置',
    language_preference VARCHAR(10) DEFAULT 'zh-CN' COMMENT '语言偏好',
    timezone VARCHAR(50) DEFAULT 'Asia/Shanghai' COMMENT '时区设置',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT NOT NULL DEFAULT 0,
    version BIGINT NOT NULL DEFAULT 0,
    
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户通知设置表';

-- 通知发送记录表
CREATE TABLE notification_send_records (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    notification_id BIGINT NOT NULL COMMENT '通知ID',
    channel VARCHAR(20) NOT NULL COMMENT '发送渠道',
    status VARCHAR(20) NOT NULL COMMENT '发送状态',
    send_time DATETIME NOT NULL COMMENT '发送时间',
    response_code VARCHAR(20) COMMENT '响应代码',
    response_message TEXT COMMENT '响应消息',
    external_id VARCHAR(100) COMMENT '外部ID',
    cost DECIMAL(10,4) COMMENT '发送成本',
    duration_ms INT COMMENT '发送耗时（毫秒）',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_notification_id (notification_id),
    INDEX idx_channel (channel),
    INDEX idx_status (status),
    INDEX idx_send_time (send_time),
    FOREIGN KEY (notification_id) REFERENCES notification_messages(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知发送记录表';

-- 通知统计表
CREATE TABLE notification_statistics (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    stat_date DATE NOT NULL COMMENT '统计日期',
    notification_type VARCHAR(30) COMMENT '通知类型',
    notification_channel VARCHAR(20) COMMENT '通知渠道',
    total_sent BIGINT NOT NULL DEFAULT 0 COMMENT '总发送数',
    total_delivered BIGINT NOT NULL DEFAULT 0 COMMENT '总送达数',
    total_read BIGINT NOT NULL DEFAULT 0 COMMENT '总阅读数',
    total_failed BIGINT NOT NULL DEFAULT 0 COMMENT '总失败数',
    delivery_rate DECIMAL(5,4) COMMENT '送达率',
    read_rate DECIMAL(5,4) COMMENT '阅读率',
    failure_rate DECIMAL(5,4) COMMENT '失败率',
    avg_delivery_time DECIMAL(8,2) COMMENT '平均送达时间（秒）',
    avg_read_time DECIMAL(8,2) COMMENT '平均阅读时间（秒）',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_stat_date_type_channel (stat_date, notification_type, notification_channel),
    INDEX idx_stat_date (stat_date),
    INDEX idx_type (notification_type),
    INDEX idx_channel (notification_channel)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知统计表';
