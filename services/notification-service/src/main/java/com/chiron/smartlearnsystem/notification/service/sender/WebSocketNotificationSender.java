package com.chiron.smartlearnsystem.notification.service.sender;

import com.chiron.smartlearnsystem.notification.entity.NotificationMessage;
import com.chiron.smartlearnsystem.notification.enums.NotificationStatus;
import com.chiron.smartlearnsystem.notification.repository.NotificationMessageRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * WebSocket实时通知发送器
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WebSocketNotificationSender implements NotificationChannelSender {

    private final SimpMessagingTemplate messagingTemplate;
    private final NotificationMessageRepository notificationRepository;

    @Override
    public boolean send(NotificationMessage notification) {
        log.debug("发送WebSocket通知: id={}, userId={}", notification.getId(), notification.getUserId());

        try {
            // 验证通知
            if (!validate(notification)) {
                log.warn("WebSocket通知验证失败: id={}", notification.getId());
                return false;
            }

            // 构建WebSocket消息
            Map<String, Object> message = buildWebSocketMessage(notification);

            // 发送到用户特定的WebSocket频道
            String destination = "/user/" + notification.getUserId() + "/notifications";
            messagingTemplate.convertAndSend(destination, message);

            // 更新通知状态
            notification.setStatus(NotificationStatus.SENT);
            notification.setSentTime(LocalDateTime.now());
            notificationRepository.save(notification);

            log.debug("WebSocket通知发送成功: id={}", notification.getId());
            return true;

        } catch (Exception e) {
            log.error("WebSocket通知发送失败: id={}", notification.getId(), e);
            return false;
        }
    }

    @Override
    public boolean validate(NotificationMessage notification) {
        if (!NotificationChannelSender.super.validate(notification)) {
            return false;
        }

        // 检查用户ID
        if (notification.getUserId() == null) {
            log.warn("WebSocket通知缺少用户ID: id={}", notification.getId());
            return false;
        }

        return true;
    }

    @Override
    public String getSenderName() {
        return "WebSocketNotificationSender";
    }

    /**
     * 构建WebSocket消息
     */
    private Map<String, Object> buildWebSocketMessage(NotificationMessage notification) {
        Map<String, Object> message = new HashMap<>();
        message.put("id", notification.getId());
        message.put("type", notification.getNotificationType().getCode());
        message.put("channel", notification.getNotificationChannel().getCode());
        message.put("priority", notification.getPriority().getCode());
        message.put("title", notification.getTitle());
        message.put("content", notification.getContent());
        message.put("richContent", notification.getRichContent());
        message.put("notificationData", notification.getNotificationData());
        message.put("clickAction", notification.getClickAction());
        message.put("requiresConfirmation", notification.getRequiresConfirmation());
        message.put("tags", notification.getTags());
        message.put("sentTime", notification.getSentTime());
        message.put("expiresAt", notification.getExpiresAt());
        
        return message;
    }

    /**
     * 发送系统广播通知
     */
    public void sendBroadcastNotification(NotificationMessage notification) {
        log.debug("发送系统广播通知: id={}", notification.getId());

        try {
            Map<String, Object> message = buildWebSocketMessage(notification);
            messagingTemplate.convertAndSend("/topic/notifications", message);
            
            log.debug("系统广播通知发送成功: id={}", notification.getId());
        } catch (Exception e) {
            log.error("系统广播通知发送失败: id={}", notification.getId(), e);
        }
    }

}
