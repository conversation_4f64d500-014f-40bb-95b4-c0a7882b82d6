package com.chiron.smartlearnsystem.notification.repository;

import com.chiron.smartlearnsystem.notification.entity.NotificationMessage;
import com.chiron.smartlearnsystem.notification.enums.NotificationChannel;
import com.chiron.smartlearnsystem.notification.enums.NotificationStatus;
import com.chiron.smartlearnsystem.notification.enums.NotificationType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 通知消息数据访问层
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Repository
public interface NotificationMessageRepository extends JpaRepository<NotificationMessage, Long>, 
                                                     JpaSpecificationExecutor<NotificationMessage> {

    /**
     * 根据用户ID查找通知
     */
    Page<NotificationMessage> findByUserId(Long userId, Pageable pageable);

    /**
     * 根据用户ID和已读状态查找通知
     */
    List<NotificationMessage> findByUserIdAndIsReadFalseOrderByCreatedAtDesc(Long userId);

    /**
     * 根据用户ID查找未读通知
     */
    List<NotificationMessage> findByUserIdAndIsReadFalse(Long userId);

    /**
     * 统计用户未读通知数量
     */
    Long countByUserIdAndIsReadFalse(Long userId);

    /**
     * 根据状态查找通知
     */
    List<NotificationMessage> findByStatus(NotificationStatus status);

    /**
     * 根据通知类型查找通知
     */
    List<NotificationMessage> findByNotificationType(NotificationType notificationType);

    /**
     * 根据通知渠道查找通知
     */
    List<NotificationMessage> findByNotificationChannel(NotificationChannel notificationChannel);

    /**
     * 查找计划发送的通知
     */
    @Query("SELECT nm FROM NotificationMessage nm WHERE nm.status = 'SCHEDULED' " +
           "AND nm.scheduledTime <= :now")
    List<NotificationMessage> findDueScheduledNotifications(@Param("now") LocalDateTime now);

    /**
     * 查找可重试的失败通知
     */
    @Query("SELECT nm FROM NotificationMessage nm WHERE nm.status = 'FAILED' " +
           "AND nm.retryCount < nm.maxRetries AND nm.updatedAt > :since")
    List<NotificationMessage> findRetryableFailedNotifications(@Param("since") LocalDateTime since);

    /**
     * 查找过期的通知
     */
    @Query("SELECT nm FROM NotificationMessage nm WHERE nm.expiresAt < :now " +
           "AND nm.status NOT IN ('EXPIRED', 'CANCELLED')")
    List<NotificationMessage> findExpiredNotifications(@Param("now") LocalDateTime now);

    /**
     * 标记过期通知
     */
    @Modifying
    @Query("UPDATE NotificationMessage nm SET nm.status = 'EXPIRED' " +
           "WHERE nm.expiresAt < :now AND nm.status NOT IN ('EXPIRED', 'CANCELLED')")
    int markExpiredNotifications(@Param("now") LocalDateTime now);

    /**
     * 删除过期的已读通知
     */
    @Modifying
    @Query("DELETE FROM NotificationMessage nm WHERE nm.expiresAt < :cutoffTime " +
           "AND nm.isRead = true AND nm.status = 'EXPIRED'")
    int deleteExpiredReadNotifications(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 统计各状态通知数量
     */
    @Query("SELECT nm.status, COUNT(nm) FROM NotificationMessage nm GROUP BY nm.status")
    List<Object[]> countNotificationsByStatus();

    /**
     * 统计各渠道通知数量
     */
    @Query("SELECT nm.notificationChannel, COUNT(nm) FROM NotificationMessage nm GROUP BY nm.notificationChannel")
    List<Object[]> countNotificationsByChannel();

    /**
     * 统计各类型通知数量
     */
    @Query("SELECT nm.notificationType, COUNT(nm) FROM NotificationMessage nm GROUP BY nm.notificationType")
    List<Object[]> countNotificationsByType();

    /**
     * 统计指定时间后发送的通知数量
     */
    @Query("SELECT COUNT(nm) FROM NotificationMessage nm WHERE nm.sentTime >= :since")
    Long countSentNotificationsSince(@Param("since") LocalDateTime since);

    /**
     * 根据状态统计通知数量
     */
    Long countByStatus(NotificationStatus status);

    /**
     * 查找用户指定类型的通知
     */
    List<NotificationMessage> findByUserIdAndNotificationType(Long userId, NotificationType notificationType);

    /**
     * 查找用户指定渠道的通知
     */
    List<NotificationMessage> findByUserIdAndNotificationChannel(Long userId, NotificationChannel notificationChannel);

    /**
     * 查找用户指定时间范围的通知
     */
    @Query("SELECT nm FROM NotificationMessage nm WHERE nm.userId = :userId " +
           "AND nm.createdAt BETWEEN :startTime AND :endTime ORDER BY nm.createdAt DESC")
    List<NotificationMessage> findByUserIdAndTimeRange(@Param("userId") Long userId,
                                                       @Param("startTime") LocalDateTime startTime,
                                                       @Param("endTime") LocalDateTime endTime);

    /**
     * 查找需要确认的通知
     */
    @Query("SELECT nm FROM NotificationMessage nm WHERE nm.userId = :userId " +
           "AND nm.requiresConfirmation = true AND nm.confirmedAt IS NULL " +
           "AND nm.status = 'SENT'")
    List<NotificationMessage> findUnconfirmedNotifications(@Param("userId") Long userId);

    /**
     * 查找包含特定标签的通知
     */
    @Query("SELECT nm FROM NotificationMessage nm JOIN nm.tags t " +
           "WHERE nm.userId = :userId AND t = :tag")
    List<NotificationMessage> findByUserIdAndTag(@Param("userId") Long userId, @Param("tag") String tag);

    /**
     * 查找发送失败次数超过阈值的通知
     */
    @Query("SELECT nm FROM NotificationMessage nm WHERE nm.retryCount >= :threshold")
    List<NotificationMessage> findNotificationsWithHighRetryCount(@Param("threshold") Integer threshold);

    /**
     * 获取用户通知统计
     */
    @Query("SELECT " +
           "COUNT(nm) as totalCount, " +
           "SUM(CASE WHEN nm.isRead = true THEN 1 ELSE 0 END) as readCount, " +
           "SUM(CASE WHEN nm.status = 'SENT' THEN 1 ELSE 0 END) as sentCount, " +
           "SUM(CASE WHEN nm.status = 'FAILED' THEN 1 ELSE 0 END) as failedCount " +
           "FROM NotificationMessage nm WHERE nm.userId = :userId")
    Object[] getUserNotificationStatistics(@Param("userId") Long userId);

    /**
     * 查找最近的通知
     */
    @Query("SELECT nm FROM NotificationMessage nm WHERE nm.createdAt >= :since " +
           "ORDER BY nm.createdAt DESC")
    List<NotificationMessage> findRecentNotifications(@Param("since") LocalDateTime since, Pageable pageable);

    /**
     * 查找高优先级未读通知
     */
    @Query("SELECT nm FROM NotificationMessage nm WHERE nm.userId = :userId " +
           "AND nm.isRead = false AND nm.priority IN ('HIGH', 'URGENT') " +
           "ORDER BY nm.priority DESC, nm.createdAt DESC")
    List<NotificationMessage> findHighPriorityUnreadNotifications(@Param("userId") Long userId);

    /**
     * 批量更新通知为已读
     */
    @Modifying
    @Query("UPDATE NotificationMessage nm SET nm.isRead = true, nm.readTime = :readTime " +
           "WHERE nm.id IN :notificationIds AND nm.userId = :userId")
    int batchMarkAsRead(@Param("notificationIds") List<Long> notificationIds, 
                       @Param("userId") Long userId, 
                       @Param("readTime") LocalDateTime readTime);

    /**
     * 批量删除通知
     */
    @Modifying
    @Query("UPDATE NotificationMessage nm SET nm.deleted = true " +
           "WHERE nm.id IN :notificationIds AND nm.userId = :userId")
    int batchDelete(@Param("notificationIds") List<Long> notificationIds, @Param("userId") Long userId);

}
