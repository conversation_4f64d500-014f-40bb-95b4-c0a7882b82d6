package com.chiron.smartlearnsystem.notification.entity;

import com.chiron.smartlearnsystem.notification.enums.NotificationChannel;
import com.chiron.smartlearnsystem.notification.enums.NotificationType;
import com.chiron.smartlearnsystem.common.database.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.util.HashSet;
import java.util.Set;

/**
 * 通知模板实体
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Entity
@Table(name = "notification_templates", indexes = {
    @Index(name = "idx_template_code", columnList = "template_code"),
    @Index(name = "idx_type", columnList = "notification_type"),
    @Index(name = "idx_channel", columnList = "notification_channel"),
    @Index(name = "idx_enabled", columnList = "is_enabled"),
    @Index(name = "idx_created_at", columnList = "created_at")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationTemplate extends BaseEntity {

    /**
     * 模板代码（唯一标识）
     */
    @Column(name = "template_code", nullable = false, unique = true, length = 50)
    private String templateCode;

    /**
     * 模板名称
     */
    @Column(name = "template_name", nullable = false, length = 100)
    private String templateName;

    /**
     * 模板描述
     */
    @Column(name = "template_description", columnDefinition = "TEXT")
    private String templateDescription;

    /**
     * 通知类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "notification_type", nullable = false, length = 30)
    private NotificationType notificationType;

    /**
     * 通知渠道
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "notification_channel", nullable = false, length = 20)
    private NotificationChannel notificationChannel;

    /**
     * 模板标题
     */
    @Column(name = "title_template", nullable = false, length = 200)
    private String titleTemplate;

    /**
     * 模板内容
     */
    @Column(name = "content_template", nullable = false, columnDefinition = "TEXT")
    private String contentTemplate;

    /**
     * 富文本模板（HTML格式）
     */
    @Column(name = "rich_content_template", columnDefinition = "TEXT")
    private String richContentTemplate;

    /**
     * 模板参数定义（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "parameter_definitions", columnDefinition = "JSON")
    private String parameterDefinitions;

    /**
     * 默认参数值（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "default_parameters", columnDefinition = "JSON")
    private String defaultParameters;

    /**
     * 模板语言
     */
    @Column(name = "template_language", length = 10)
    private String templateLanguage = "zh-CN";

    /**
     * 是否启用
     */
    @Column(name = "is_enabled", nullable = false)
    private Boolean isEnabled = true;

    /**
     * 是否为系统模板
     */
    @Column(name = "is_system_template", nullable = false)
    private Boolean isSystemTemplate = false;

    /**
     * 模板版本
     */
    @Column(name = "template_version", length = 20)
    private String templateVersion = "1.0";

    /**
     * 模板分类
     */
    @Column(name = "template_category", length = 50)
    private String templateCategory;

    /**
     * 模板标签
     */
    @ElementCollection
    @CollectionTable(
        name = "notification_template_tags",
        joinColumns = @JoinColumn(name = "template_id")
    )
    @Column(name = "tag")
    private Set<String> tags = new HashSet<>();

    /**
     * 样式配置（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "style_config", columnDefinition = "JSON")
    private String styleConfig;

    /**
     * 附件配置（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "attachment_config", columnDefinition = "JSON")
    private String attachmentConfig;

    /**
     * 点击动作配置（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "click_action_config", columnDefinition = "JSON")
    private String clickActionConfig;

    /**
     * 发送配置（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "send_config", columnDefinition = "JSON")
    private String sendConfig;

    /**
     * 使用次数
     */
    @Column(name = "usage_count", nullable = false)
    private Long usageCount = 0L;

    /**
     * 最后使用时间
     */
    @Column(name = "last_used_at")
    private java.time.LocalDateTime lastUsedAt;

    /**
     * 创建者ID
     */
    @Column(name = "created_by")
    private Long createdBy;

    /**
     * 模板备注
     */
    @Column(columnDefinition = "TEXT")
    private String notes;

    /**
     * 增加使用次数
     */
    public void incrementUsageCount() {
        this.usageCount++;
        this.lastUsedAt = java.time.LocalDateTime.now();
    }

    /**
     * 启用模板
     */
    public void enable() {
        this.isEnabled = true;
    }

    /**
     * 禁用模板
     */
    public void disable() {
        this.isEnabled = false;
    }

    /**
     * 添加标签
     */
    public void addTag(String tag) {
        this.tags.add(tag);
    }

    /**
     * 移除标签
     */
    public void removeTag(String tag) {
        this.tags.remove(tag);
    }

    /**
     * 检查是否包含标签
     */
    public boolean hasTag(String tag) {
        return this.tags.contains(tag);
    }

    /**
     * 检查是否为多语言模板
     */
    public boolean isMultiLanguage() {
        return this.tags.contains("multilingual");
    }

    /**
     * 检查是否支持富文本
     */
    public boolean supportsRichContent() {
        return this.richContentTemplate != null && !this.richContentTemplate.trim().isEmpty();
    }

    /**
     * 检查是否有附件配置
     */
    public boolean hasAttachmentConfig() {
        return this.attachmentConfig != null && !this.attachmentConfig.trim().isEmpty();
    }

    /**
     * 检查是否有点击动作配置
     */
    public boolean hasClickActionConfig() {
        return this.clickActionConfig != null && !this.clickActionConfig.trim().isEmpty();
    }

}
