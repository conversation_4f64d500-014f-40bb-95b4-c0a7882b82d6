package com.chiron.smartlearnsystem.notification.service.sender;

import com.chiron.smartlearnsystem.notification.entity.NotificationMessage;
import com.chiron.smartlearnsystem.notification.enums.NotificationStatus;
import com.chiron.smartlearnsystem.notification.repository.NotificationMessageRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 应用内通知发送器
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class InAppNotificationSender implements NotificationChannelSender {

    private final NotificationMessageRepository notificationRepository;
    private final RedisTemplate<String, Object> redisTemplate;

    private static final String IN_APP_NOTIFICATION_KEY = "notification:inapp:";
    private static final int NOTIFICATION_EXPIRE_DAYS = 30;

    @Override
    public boolean send(NotificationMessage notification) {
        log.debug("发送应用内通知: id={}, userId={}", notification.getId(), notification.getUserId());

        try {
            // 验证通知
            if (!validate(notification)) {
                log.warn("应用内通知验证失败: id={}", notification.getId());
                return false;
            }

            // 更新通知状态
            notification.setStatus(NotificationStatus.SENT);
            notification.setSentTime(LocalDateTime.now());
            notificationRepository.save(notification);

            // 将通知存储到Redis中，用于实时获取
            String cacheKey = IN_APP_NOTIFICATION_KEY + notification.getUserId();
            redisTemplate.opsForList().leftPush(cacheKey, notification.getId());
            redisTemplate.expire(cacheKey, NOTIFICATION_EXPIRE_DAYS, TimeUnit.DAYS);

            // 清除用户未读通知缓存
            clearUserUnreadCache(notification.getUserId());

            log.debug("应用内通知发送成功: id={}", notification.getId());
            return true;

        } catch (Exception e) {
            log.error("应用内通知发送失败: id={}", notification.getId(), e);
            return false;
        }
    }

    @Override
    public boolean sendBatch(List<NotificationMessage> notifications) {
        log.debug("批量发送应用内通知: count={}", notifications.size());

        try {
            // 按用户分组
            notifications.stream()
                    .collect(java.util.stream.Collectors.groupingBy(NotificationMessage::getUserId))
                    .forEach((userId, userNotifications) -> {
                        String cacheKey = IN_APP_NOTIFICATION_KEY + userId;
                        
                        // 批量更新通知状态
                        userNotifications.forEach(notification -> {
                            notification.setStatus(NotificationStatus.SENT);
                            notification.setSentTime(LocalDateTime.now());
                        });
                        
                        // 批量保存
                        notificationRepository.saveAll(userNotifications);
                        
                        // 批量添加到Redis
                        List<Long> notificationIds = userNotifications.stream()
                                .map(NotificationMessage::getId)
                                .collect(java.util.stream.Collectors.toList());
                        
                        redisTemplate.opsForList().leftPushAll(cacheKey, notificationIds.toArray());
                        redisTemplate.expire(cacheKey, NOTIFICATION_EXPIRE_DAYS, TimeUnit.DAYS);
                        
                        // 清除用户未读通知缓存
                        clearUserUnreadCache(userId);
                    });

            log.debug("批量应用内通知发送成功: count={}", notifications.size());
            return true;

        } catch (Exception e) {
            log.error("批量应用内通知发送失败", e);
            return false;
        }
    }

    @Override
    public boolean supportsBatchSend() {
        return true;
    }

    @Override
    public boolean validate(NotificationMessage notification) {
        if (!NotificationChannelSender.super.validate(notification)) {
            return false;
        }

        // 检查用户ID
        if (notification.getUserId() == null) {
            log.warn("应用内通知缺少用户ID: id={}", notification.getId());
            return false;
        }

        // 检查内容长度
        if (notification.getContent().length() > 1000) {
            log.warn("应用内通知内容过长: id={}, length={}", 
                    notification.getId(), notification.getContent().length());
            return false;
        }

        return true;
    }

    @Override
    public String getSenderName() {
        return "InAppNotificationSender";
    }

    /**
     * 获取用户的应用内通知
     */
    public List<Long> getUserInAppNotifications(Long userId, int limit) {
        String cacheKey = IN_APP_NOTIFICATION_KEY + userId;
        return redisTemplate.opsForList().range(cacheKey, 0, limit - 1)
                .stream()
                .map(obj -> (Long) obj)
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 移除用户的应用内通知
     */
    public void removeUserInAppNotification(Long userId, Long notificationId) {
        String cacheKey = IN_APP_NOTIFICATION_KEY + userId;
        redisTemplate.opsForList().remove(cacheKey, 1, notificationId);
    }

    /**
     * 清除用户未读通知缓存
     */
    private void clearUserUnreadCache(Long userId) {
        String unreadCacheKey = "notification:user:" + userId + ":unread";
        String unreadCountCacheKey = "notification:user:" + userId + ":unread:count";
        redisTemplate.delete(unreadCacheKey);
        redisTemplate.delete(unreadCountCacheKey);
    }

}
