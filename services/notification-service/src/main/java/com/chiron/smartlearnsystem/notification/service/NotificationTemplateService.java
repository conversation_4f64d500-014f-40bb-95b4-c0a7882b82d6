package com.chiron.smartlearnsystem.notification.service;

import com.chiron.smartlearnsystem.notification.entity.NotificationTemplate;
import com.chiron.smartlearnsystem.notification.enums.NotificationChannel;
import com.chiron.smartlearnsystem.notification.enums.NotificationType;
import com.chiron.smartlearnsystem.notification.repository.NotificationTemplateRepository;
import com.chiron.smartlearnsystem.common.core.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 通知模板服务
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class NotificationTemplateService {

    private final NotificationTemplateRepository templateRepository;
    private final RedisTemplate<String, Object> redisTemplate;

    private static final String TEMPLATE_CACHE_KEY = "notification:template:";
    private static final int CACHE_EXPIRE_HOURS = 24;
    private static final Pattern PARAMETER_PATTERN = Pattern.compile("\\{\\{(\\w+)\\}\\}");

    /**
     * 根据模板代码获取模板
     */
    @Transactional(readOnly = true)
    public NotificationTemplate getTemplateByCode(String templateCode) {
        log.debug("获取通知模板: templateCode={}", templateCode);

        // 先从缓存获取
        String cacheKey = TEMPLATE_CACHE_KEY + templateCode;
        NotificationTemplate cachedTemplate = (NotificationTemplate) redisTemplate.opsForValue().get(cacheKey);
        if (cachedTemplate != null) {
            return cachedTemplate;
        }

        // 从数据库获取
        NotificationTemplate template = templateRepository.findByTemplateCodeAndIsEnabledTrue(templateCode)
                .orElseThrow(() -> new BusinessException("通知模板不存在或已禁用: " + templateCode));

        // 缓存模板
        redisTemplate.opsForValue().set(cacheKey, template, CACHE_EXPIRE_HOURS, TimeUnit.HOURS);

        return template;
    }

    /**
     * 根据类型和渠道获取模板
     */
    @Transactional(readOnly = true)
    public List<NotificationTemplate> getTemplatesByTypeAndChannel(NotificationType type, NotificationChannel channel) {
        log.debug("获取通知模板: type={}, channel={}", type, channel);

        return templateRepository.findByNotificationTypeAndNotificationChannelAndIsEnabledTrue(type, channel);
    }

    /**
     * 渲染模板内容
     */
    public String renderTemplate(String templateCode, Map<String, Object> parameters) {
        log.debug("渲染模板: templateCode={}", templateCode);

        NotificationTemplate template = getTemplateByCode(templateCode);
        
        // 渲染标题
        String renderedTitle = renderContent(template.getTitleTemplate(), parameters);
        
        // 渲染内容
        String renderedContent = renderContent(template.getContentTemplate(), parameters);
        
        // 更新使用统计
        template.incrementUsageCount();
        templateRepository.save(template);
        
        return renderedContent;
    }

    /**
     * 渲染模板标题
     */
    public String renderTemplateTitle(String templateCode, Map<String, Object> parameters) {
        log.debug("渲染模板标题: templateCode={}", templateCode);

        NotificationTemplate template = getTemplateByCode(templateCode);
        return renderContent(template.getTitleTemplate(), parameters);
    }

    /**
     * 渲染富文本模板
     */
    public String renderRichTemplate(String templateCode, Map<String, Object> parameters) {
        log.debug("渲染富文本模板: templateCode={}", templateCode);

        NotificationTemplate template = getTemplateByCode(templateCode);
        
        if (template.getRichContentTemplate() == null || template.getRichContentTemplate().trim().isEmpty()) {
            // 如果没有富文本模板，使用普通模板
            return renderContent(template.getContentTemplate(), parameters);
        }
        
        return renderContent(template.getRichContentTemplate(), parameters);
    }

    /**
     * 验证模板参数
     */
    public boolean validateTemplateParameters(String templateCode, Map<String, Object> parameters) {
        log.debug("验证模板参数: templateCode={}", templateCode);

        try {
            NotificationTemplate template = getTemplateByCode(templateCode);
            
            // 提取模板中的参数
            List<String> requiredParams = extractParameters(template.getTitleTemplate());
            requiredParams.addAll(extractParameters(template.getContentTemplate()));
            
            // 检查必需参数是否都提供了
            for (String param : requiredParams) {
                if (!parameters.containsKey(param)) {
                    log.warn("缺少必需参数: templateCode={}, param={}", templateCode, param);
                    return false;
                }
            }
            
            return true;
        } catch (Exception e) {
            log.error("验证模板参数失败: templateCode={}", templateCode, e);
            return false;
        }
    }

    /**
     * 获取模板参数列表
     */
    public List<String> getTemplateParameters(String templateCode) {
        log.debug("获取模板参数: templateCode={}", templateCode);

        NotificationTemplate template = getTemplateByCode(templateCode);
        
        List<String> parameters = extractParameters(template.getTitleTemplate());
        parameters.addAll(extractParameters(template.getContentTemplate()));
        
        // 去重
        return parameters.stream().distinct().collect(java.util.stream.Collectors.toList());
    }

    /**
     * 创建模板
     */
    public NotificationTemplate createTemplate(NotificationTemplate template) {
        log.info("创建通知模板: templateCode={}", template.getTemplateCode());

        // 检查模板代码是否已存在
        if (templateRepository.existsByTemplateCode(template.getTemplateCode())) {
            throw new BusinessException("模板代码已存在: " + template.getTemplateCode());
        }

        // 验证模板语法
        validateTemplateSyntax(template);

        NotificationTemplate savedTemplate = templateRepository.save(template);
        
        // 清除相关缓存
        clearTemplateCache(template.getTemplateCode());
        
        return savedTemplate;
    }

    /**
     * 更新模板
     */
    public NotificationTemplate updateTemplate(NotificationTemplate template) {
        log.info("更新通知模板: templateCode={}", template.getTemplateCode());

        // 验证模板语法
        validateTemplateSyntax(template);

        NotificationTemplate savedTemplate = templateRepository.save(template);
        
        // 清除相关缓存
        clearTemplateCache(template.getTemplateCode());
        
        return savedTemplate;
    }

    /**
     * 启用模板
     */
    public void enableTemplate(String templateCode) {
        log.info("启用通知模板: templateCode={}", templateCode);

        NotificationTemplate template = templateRepository.findByTemplateCode(templateCode)
                .orElseThrow(() -> new BusinessException("模板不存在: " + templateCode));

        template.enable();
        templateRepository.save(template);
        
        // 清除缓存
        clearTemplateCache(templateCode);
    }

    /**
     * 禁用模板
     */
    public void disableTemplate(String templateCode) {
        log.info("禁用通知模板: templateCode={}", templateCode);

        NotificationTemplate template = templateRepository.findByTemplateCode(templateCode)
                .orElseThrow(() -> new BusinessException("模板不存在: " + templateCode));

        template.disable();
        templateRepository.save(template);
        
        // 清除缓存
        clearTemplateCache(templateCode);
    }

    /**
     * 渲染内容
     */
    private String renderContent(String template, Map<String, Object> parameters) {
        if (template == null || template.trim().isEmpty()) {
            return "";
        }

        String result = template;
        
        // 替换参数
        Matcher matcher = PARAMETER_PATTERN.matcher(template);
        while (matcher.find()) {
            String paramName = matcher.group(1);
            Object paramValue = parameters.get(paramName);
            
            if (paramValue != null) {
                result = result.replace("{{" + paramName + "}}", paramValue.toString());
            } else {
                log.warn("模板参数未提供: paramName={}", paramName);
                result = result.replace("{{" + paramName + "}}", "");
            }
        }
        
        return result;
    }

    /**
     * 提取模板参数
     */
    private List<String> extractParameters(String template) {
        List<String> parameters = new java.util.ArrayList<>();
        
        if (template == null || template.trim().isEmpty()) {
            return parameters;
        }

        Matcher matcher = PARAMETER_PATTERN.matcher(template);
        while (matcher.find()) {
            parameters.add(matcher.group(1));
        }
        
        return parameters;
    }

    /**
     * 验证模板语法
     */
    private void validateTemplateSyntax(NotificationTemplate template) {
        // 验证标题模板
        if (template.getTitleTemplate() == null || template.getTitleTemplate().trim().isEmpty()) {
            throw new BusinessException("模板标题不能为空");
        }

        // 验证内容模板
        if (template.getContentTemplate() == null || template.getContentTemplate().trim().isEmpty()) {
            throw new BusinessException("模板内容不能为空");
        }

        // 验证参数语法
        try {
            extractParameters(template.getTitleTemplate());
            extractParameters(template.getContentTemplate());
            if (template.getRichContentTemplate() != null) {
                extractParameters(template.getRichContentTemplate());
            }
        } catch (Exception e) {
            throw new BusinessException("模板语法错误: " + e.getMessage());
        }
    }

    /**
     * 清除模板缓存
     */
    private void clearTemplateCache(String templateCode) {
        String cacheKey = TEMPLATE_CACHE_KEY + templateCode;
        redisTemplate.delete(cacheKey);
    }

}
