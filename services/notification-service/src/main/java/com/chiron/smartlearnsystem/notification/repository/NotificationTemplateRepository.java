package com.chiron.smartlearnsystem.notification.repository;

import com.chiron.smartlearnsystem.notification.entity.NotificationTemplate;
import com.chiron.smartlearnsystem.notification.enums.NotificationChannel;
import com.chiron.smartlearnsystem.notification.enums.NotificationType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 通知模板数据访问层
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Repository
public interface NotificationTemplateRepository extends JpaRepository<NotificationTemplate, Long>, 
                                                      JpaSpecificationExecutor<NotificationTemplate> {

    /**
     * 根据模板代码查找模板
     */
    Optional<NotificationTemplate> findByTemplateCode(String templateCode);

    /**
     * 根据模板代码查找启用的模板
     */
    Optional<NotificationTemplate> findByTemplateCodeAndIsEnabledTrue(String templateCode);

    /**
     * 检查模板代码是否存在
     */
    boolean existsByTemplateCode(String templateCode);

    /**
     * 根据通知类型查找模板
     */
    List<NotificationTemplate> findByNotificationType(NotificationType notificationType);

    /**
     * 根据通知渠道查找模板
     */
    List<NotificationTemplate> findByNotificationChannel(NotificationChannel notificationChannel);

    /**
     * 根据通知类型和渠道查找启用的模板
     */
    List<NotificationTemplate> findByNotificationTypeAndNotificationChannelAndIsEnabledTrue(
            NotificationType notificationType, NotificationChannel notificationChannel);

    /**
     * 查找启用的模板
     */
    List<NotificationTemplate> findByIsEnabledTrue();

    /**
     * 查找系统模板
     */
    List<NotificationTemplate> findByIsSystemTemplateTrue();

    /**
     * 根据模板分类查找模板
     */
    List<NotificationTemplate> findByTemplateCategory(String templateCategory);

    /**
     * 根据模板语言查找模板
     */
    List<NotificationTemplate> findByTemplateLanguage(String templateLanguage);

    /**
     * 查找包含特定标签的模板
     */
    @Query("SELECT nt FROM NotificationTemplate nt JOIN nt.tags t WHERE t = :tag")
    List<NotificationTemplate> findByTag(@Param("tag") String tag);

    /**
     * 查找使用次数最多的模板
     */
    @Query("SELECT nt FROM NotificationTemplate nt WHERE nt.isEnabled = true " +
           "ORDER BY nt.usageCount DESC")
    List<NotificationTemplate> findMostUsedTemplates(org.springframework.data.domain.Pageable pageable);

    /**
     * 查找最近使用的模板
     */
    @Query("SELECT nt FROM NotificationTemplate nt WHERE nt.lastUsedAt IS NOT NULL " +
           "ORDER BY nt.lastUsedAt DESC")
    List<NotificationTemplate> findRecentlyUsedTemplates(org.springframework.data.domain.Pageable pageable);

    /**
     * 统计各类型模板数量
     */
    @Query("SELECT nt.notificationType, COUNT(nt) FROM NotificationTemplate nt " +
           "WHERE nt.isEnabled = true GROUP BY nt.notificationType")
    List<Object[]> countTemplatesByType();

    /**
     * 统计各渠道模板数量
     */
    @Query("SELECT nt.notificationChannel, COUNT(nt) FROM NotificationTemplate nt " +
           "WHERE nt.isEnabled = true GROUP BY nt.notificationChannel")
    List<Object[]> countTemplatesByChannel();

    /**
     * 查找未使用的模板
     */
    @Query("SELECT nt FROM NotificationTemplate nt WHERE nt.usageCount = 0 " +
           "OR nt.lastUsedAt IS NULL")
    List<NotificationTemplate> findUnusedTemplates();

    /**
     * 根据创建者查找模板
     */
    List<NotificationTemplate> findByCreatedBy(Long createdBy);

    /**
     * 查找特定版本的模板
     */
    List<NotificationTemplate> findByTemplateVersion(String templateVersion);

}
