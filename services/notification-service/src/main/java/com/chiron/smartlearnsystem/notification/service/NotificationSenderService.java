package com.chiron.smartlearnsystem.notification.service;

import com.chiron.smartlearnsystem.notification.entity.NotificationMessage;
import com.chiron.smartlearnsystem.notification.enums.NotificationChannel;
import com.chiron.smartlearnsystem.notification.enums.NotificationStatus;
import com.chiron.smartlearnsystem.notification.repository.NotificationMessageRepository;
import com.chiron.smartlearnsystem.notification.service.sender.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 通知发送服务
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class NotificationSenderService {

    private final NotificationMessageRepository notificationRepository;
    private final InAppNotificationSender inAppSender;
    private final PushNotificationSender pushSender;
    private final EmailNotificationSender emailSender;
    private final SmsNotificationSender smsSender;
    private final WebSocketNotificationSender webSocketSender;

    private final Map<NotificationChannel, NotificationChannelSender> senders = new HashMap<>();

    /**
     * 初始化发送器映射
     */
    public void initializeSenders() {
        senders.put(NotificationChannel.IN_APP, inAppSender);
        senders.put(NotificationChannel.PUSH, pushSender);
        senders.put(NotificationChannel.EMAIL, emailSender);
        senders.put(NotificationChannel.SMS, smsSender);
        senders.put(NotificationChannel.WEBSOCKET, webSocketSender);
    }

    /**
     * 发送通知
     */
    public void sendNotification(NotificationMessage notification) {
        log.info("发送通知: id={}, channel={}, type={}", 
                notification.getId(), notification.getNotificationChannel(), notification.getNotificationType());

        try {
            // 更新状态为发送中
            notification.setStatus(NotificationStatus.SENDING);
            notificationRepository.save(notification);

            // 获取对应的发送器
            NotificationChannelSender sender = getSender(notification.getNotificationChannel());
            if (sender == null) {
                throw new RuntimeException("不支持的通知渠道: " + notification.getNotificationChannel());
            }

            // 发送通知
            boolean success = sender.send(notification);

            if (success) {
                // 发送成功
                notification.markAsSent();
                log.info("通知发送成功: id={}", notification.getId());
            } else {
                // 发送失败
                notification.incrementRetryCount();
                notification.markAsFailed("发送失败");
                log.warn("通知发送失败: id={}", notification.getId());
            }

        } catch (Exception e) {
            log.error("通知发送异常: id={}", notification.getId(), e);
            
            // 发送异常
            notification.incrementRetryCount();
            notification.markAsFailed("发送异常: " + e.getMessage());
        } finally {
            // 保存最终状态
            notificationRepository.save(notification);
        }
    }

    /**
     * 批量发送通知
     */
    public void sendBatchNotifications(java.util.List<NotificationMessage> notifications) {
        log.info("批量发送通知: count={}", notifications.size());

        // 按渠道分组
        Map<NotificationChannel, java.util.List<NotificationMessage>> groupedNotifications = 
                notifications.stream()
                        .collect(java.util.stream.Collectors.groupingBy(NotificationMessage::getNotificationChannel));

        // 分渠道批量发送
        groupedNotifications.forEach((channel, channelNotifications) -> {
            NotificationChannelSender sender = getSender(channel);
            if (sender != null && sender.supportsBatchSend()) {
                try {
                    sender.sendBatch(channelNotifications);
                } catch (Exception e) {
                    log.error("批量发送通知失败: channel={}", channel, e);
                    // 逐个发送失败的通知
                    channelNotifications.forEach(this::sendNotification);
                }
            } else {
                // 逐个发送
                channelNotifications.forEach(this::sendNotification);
            }
        });
    }

    /**
     * 重试发送失败的通知
     */
    public void retryFailedNotifications() {
        log.info("重试发送失败的通知");

        // 查找可重试的失败通知
        java.util.List<NotificationMessage> failedNotifications = 
                notificationRepository.findRetryableFailedNotifications(LocalDateTime.now().minusHours(1));

        log.info("找到可重试的失败通知: count={}", failedNotifications.size());

        // 重试发送
        failedNotifications.forEach(notification -> {
            if (notification.canRetry()) {
                try {
                    sendNotification(notification);
                } catch (Exception e) {
                    log.error("重试发送通知失败: id={}", notification.getId(), e);
                }
            }
        });
    }

    /**
     * 处理计划发送的通知
     */
    public void processScheduledNotifications() {
        log.debug("处理计划发送的通知");

        // 查找到期的计划通知
        java.util.List<NotificationMessage> scheduledNotifications = 
                notificationRepository.findDueScheduledNotifications(LocalDateTime.now());

        log.debug("找到到期的计划通知: count={}", scheduledNotifications.size());

        // 发送到期的通知
        scheduledNotifications.forEach(notification -> {
            try {
                sendNotification(notification);
            } catch (Exception e) {
                log.error("发送计划通知失败: id={}", notification.getId(), e);
            }
        });
    }

    /**
     * 清理过期通知
     */
    public void cleanupExpiredNotifications() {
        log.info("清理过期通知");

        // 标记过期通知
        int expiredCount = notificationRepository.markExpiredNotifications(LocalDateTime.now());
        log.info("标记过期通知: count={}", expiredCount);

        // 删除过期的已读通知（保留30天）
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(30);
        int deletedCount = notificationRepository.deleteExpiredReadNotifications(cutoffTime);
        log.info("删除过期已读通知: count={}", deletedCount);
    }

    /**
     * 获取通知发送统计
     */
    public Map<String, Object> getNotificationStatistics() {
        Map<String, Object> statistics = new HashMap<>();

        // 按状态统计
        java.util.List<Object[]> statusStats = notificationRepository.countNotificationsByStatus();
        Map<String, Long> statusCounts = new HashMap<>();
        statusStats.forEach(stat -> statusCounts.put((String) stat[0], (Long) stat[1]));
        statistics.put("statusCounts", statusCounts);

        // 按渠道统计
        java.util.List<Object[]> channelStats = notificationRepository.countNotificationsByChannel();
        Map<String, Long> channelCounts = new HashMap<>();
        channelStats.forEach(stat -> channelCounts.put((String) stat[0], (Long) stat[1]));
        statistics.put("channelCounts", channelCounts);

        // 按类型统计
        java.util.List<Object[]> typeStats = notificationRepository.countNotificationsByType();
        Map<String, Long> typeCounts = new HashMap<>();
        typeStats.forEach(stat -> typeCounts.put((String) stat[0], (Long) stat[1]));
        statistics.put("typeCounts", typeCounts);

        // 今日发送统计
        LocalDateTime todayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
        Long todaySentCount = notificationRepository.countSentNotificationsSince(todayStart);
        statistics.put("todaySentCount", todaySentCount);

        // 失败率统计
        Long totalCount = notificationRepository.count();
        Long failedCount = notificationRepository.countByStatus(NotificationStatus.FAILED);
        double failureRate = totalCount > 0 ? (double) failedCount / totalCount * 100 : 0;
        statistics.put("failureRate", failureRate);

        return statistics;
    }

    /**
     * 获取发送器
     */
    private NotificationChannelSender getSender(NotificationChannel channel) {
        if (senders.isEmpty()) {
            initializeSenders();
        }
        return senders.get(channel);
    }

}
