package com.chiron.smartlearnsystem.notification.controller;

import com.chiron.smartlearnsystem.notification.dto.NotificationMessageDTO;
import com.chiron.smartlearnsystem.notification.dto.request.NotificationSendRequest;
import com.chiron.smartlearnsystem.notification.service.NotificationService;
import com.chiron.smartlearnsystem.common.core.domain.ApiResponse;
import com.chiron.smartlearnsystem.common.core.domain.PageResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.util.List;

/**
 * 通知控制器
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/notifications")
@RequiredArgsConstructor
@Validated
public class NotificationController {

    private final NotificationService notificationService;

    /**
     * 发送通知
     */
    @PostMapping("/send")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SYSTEM')")
    public ResponseEntity<ApiResponse<NotificationMessageDTO>> sendNotification(
            @Valid @RequestBody NotificationSendRequest request) {
        
        log.info("发送通知请求: {}", request);
        
        // 验证请求参数
        if (!request.isValid()) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.fail("请求参数无效"));
        }
        
        // 检查内容长度
        if (request.isContentTooLong()) {
            request.truncateContentIfNeeded();
            log.warn("通知内容过长，已自动截断: userId={}", request.getUserId());
        }
        
        NotificationMessageDTO notification = notificationService.sendNotification(request);
        
        return ResponseEntity.ok(ApiResponse.success("通知发送成功", notification));
    }

    /**
     * 批量发送通知
     */
    @PostMapping("/send/batch")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SYSTEM')")
    public ResponseEntity<ApiResponse<List<NotificationMessageDTO>>> sendBatchNotifications(
            @Valid @RequestBody List<NotificationSendRequest> requests) {
        
        log.info("批量发送通知请求: count={}", requests.size());
        
        // 验证批量大小
        if (requests.size() > 100) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.fail("批量发送数量不能超过100"));
        }
        
        // 验证每个请求
        for (NotificationSendRequest request : requests) {
            if (!request.isValid()) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.fail("存在无效的请求参数"));
            }
            request.truncateContentIfNeeded();
        }
        
        List<NotificationMessageDTO> notifications = notificationService.sendBatchNotifications(requests);
        
        return ResponseEntity.ok(ApiResponse.success("批量通知发送成功", notifications));
    }

    /**
     * 获取用户通知列表
     */
    @GetMapping("/user/{userId}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<PageResult<NotificationMessageDTO>>> getUserNotifications(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "1") @Min(1) Integer page,
            @RequestParam(defaultValue = "20") @Min(1) @Max(100) Integer size) {
        
        log.info("获取用户通知列表: userId={}", userId);
        
        PageResult<NotificationMessageDTO> result = notificationService.getUserNotifications(userId, page, size);
        
        return ResponseEntity.ok(ApiResponse.success("获取通知列表成功", result));
    }

    /**
     * 获取用户未读通知
     */
    @GetMapping("/user/{userId}/unread")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<NotificationMessageDTO>>> getUserUnreadNotifications(
            @PathVariable Long userId) {
        
        log.info("获取用户未读通知: userId={}", userId);
        
        List<NotificationMessageDTO> notifications = notificationService.getUserUnreadNotifications(userId);
        
        return ResponseEntity.ok(ApiResponse.success("获取未读通知成功", notifications));
    }

    /**
     * 获取用户未读通知数量
     */
    @GetMapping("/user/{userId}/unread/count")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Long>> getUserUnreadCount(@PathVariable Long userId) {
        
        log.debug("获取用户未读通知数量: userId={}", userId);
        
        Long unreadCount = notificationService.getUserUnreadCount(userId);
        
        return ResponseEntity.ok(ApiResponse.success("获取未读数量成功", unreadCount));
    }

    /**
     * 标记通知为已读
     */
    @PostMapping("/{notificationId}/read")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Void>> markNotificationAsRead(
            @PathVariable Long notificationId,
            @RequestParam Long userId) {
        
        log.debug("标记通知已读: notificationId={}, userId={}", notificationId, userId);
        
        notificationService.markNotificationAsRead(notificationId, userId);
        
        return ResponseEntity.ok(ApiResponse.<Void>success("标记已读成功", null));
    }

    /**
     * 批量标记通知为已读
     */
    @PostMapping("/read/batch")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Void>> markNotificationsAsRead(
            @RequestBody List<Long> notificationIds,
            @RequestParam Long userId) {
        
        log.info("批量标记通知已读: count={}, userId={}", notificationIds.size(), userId);
        
        if (notificationIds.size() > 100) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.fail("批量操作数量不能超过100"));
        }
        
        notificationService.markNotificationsAsRead(notificationIds, userId);
        
        return ResponseEntity.ok(ApiResponse.<Void>success("批量标记已读成功", null));
    }

    /**
     * 标记所有通知为已读
     */
    @PostMapping("/user/{userId}/read/all")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Void>> markAllNotificationsAsRead(@PathVariable Long userId) {
        
        log.info("标记所有通知已读: userId={}", userId);
        
        notificationService.markAllNotificationsAsRead(userId);
        
        return ResponseEntity.ok(ApiResponse.<Void>success("标记所有通知已读成功", null));
    }

    /**
     * 删除通知
     */
    @DeleteMapping("/{notificationId}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Void>> deleteNotification(
            @PathVariable Long notificationId,
            @RequestParam Long userId) {
        
        log.info("删除通知: notificationId={}, userId={}", notificationId, userId);
        
        notificationService.deleteNotification(notificationId, userId);
        
        return ResponseEntity.ok(ApiResponse.<Void>success("删除通知成功", null));
    }

    /**
     * 重新发送失败的通知
     */
    @PostMapping("/{notificationId}/resend")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> resendFailedNotification(@PathVariable Long notificationId) {
        
        log.info("重新发送失败通知: notificationId={}", notificationId);
        
        notificationService.resendFailedNotification(notificationId);
        
        return ResponseEntity.ok(ApiResponse.<Void>success("重新发送成功", null));
    }

}
