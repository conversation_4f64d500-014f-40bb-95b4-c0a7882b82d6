package com.chiron.smartlearnsystem.notification.service;

import com.chiron.smartlearnsystem.notification.dto.NotificationMessageDTO;
import com.chiron.smartlearnsystem.notification.dto.request.NotificationSendRequest;
import com.chiron.smartlearnsystem.notification.entity.NotificationMessage;
import com.chiron.smartlearnsystem.notification.enums.NotificationChannel;
import com.chiron.smartlearnsystem.notification.enums.NotificationStatus;
import com.chiron.smartlearnsystem.notification.enums.NotificationType;
import com.chiron.smartlearnsystem.notification.repository.NotificationMessageRepository;
import com.chiron.smartlearnsystem.common.core.domain.PageResult;
import com.chiron.smartlearnsystem.common.core.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 通知服务
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class NotificationService {

    private final NotificationMessageRepository notificationRepository;
    private final NotificationTemplateService templateService;
    private final NotificationSenderService senderService;
    private final RedisTemplate<String, Object> redisTemplate;

    private static final String NOTIFICATION_CACHE_KEY = "notification:";
    private static final String USER_NOTIFICATIONS_CACHE_KEY = "notification:user:";
    private static final int CACHE_EXPIRE_MINUTES = 30;

    /**
     * 发送通知
     */
    public NotificationMessageDTO sendNotification(NotificationSendRequest request) {
        log.info("发送通知: userId={}, type={}, channel={}", 
                request.getUserId(), request.getNotificationType(), request.getNotificationChannel());

        // 创建通知消息
        NotificationMessage notification = createNotificationMessage(request);

        // 保存通知记录
        NotificationMessage savedNotification = notificationRepository.save(notification);

        // 异步发送通知
        sendNotificationAsync(savedNotification.getId());

        // 清除用户通知缓存
        clearUserNotificationsCache(request.getUserId());

        log.info("通知创建成功: notificationId={}", savedNotification.getId());

        return NotificationMessageDTO.from(savedNotification);
    }

    /**
     * 批量发送通知
     */
    public List<NotificationMessageDTO> sendBatchNotifications(List<NotificationSendRequest> requests) {
        log.info("批量发送通知: count={}", requests.size());

        List<NotificationMessage> notifications = requests.stream()
                .map(this::createNotificationMessage)
                .collect(Collectors.toList());

        // 批量保存
        List<NotificationMessage> savedNotifications = notificationRepository.saveAll(notifications);

        // 异步批量发送
        savedNotifications.forEach(notification -> 
                sendNotificationAsync(notification.getId()));

        // 清除相关用户缓存
        requests.stream()
                .map(NotificationSendRequest::getUserId)
                .distinct()
                .forEach(this::clearUserNotificationsCache);

        log.info("批量通知创建成功: count={}", savedNotifications.size());

        return savedNotifications.stream()
                .map(NotificationMessageDTO::from)
                .collect(Collectors.toList());
    }

    /**
     * 获取用户通知列表
     */
    @Transactional(readOnly = true)
    public PageResult<NotificationMessageDTO> getUserNotifications(Long userId, Integer page, Integer size) {
        log.info("获取用户通知列表: userId={}", userId);

        Sort sort = Sort.by(Sort.Direction.DESC, "createdAt");
        Pageable pageable = PageRequest.of(page - 1, size, sort);

        Page<NotificationMessage> notificationPage = notificationRepository.findByUserId(userId, pageable);

        List<NotificationMessageDTO> notificationDTOs = notificationPage.getContent().stream()
                .map(NotificationMessageDTO::from)
                .collect(Collectors.toList());

        return PageResult.of(notificationDTOs, notificationPage.getTotalElements(), 
                           (long) page, (long) size);
    }

    /**
     * 获取用户未读通知
     */
    @Transactional(readOnly = true)
    public List<NotificationMessageDTO> getUserUnreadNotifications(Long userId) {
        log.debug("获取用户未读通知: userId={}", userId);

        // 先从缓存获取
        String cacheKey = USER_NOTIFICATIONS_CACHE_KEY + userId + ":unread";
        List<NotificationMessageDTO> cachedNotifications = 
                (List<NotificationMessageDTO>) redisTemplate.opsForValue().get(cacheKey);
        if (cachedNotifications != null) {
            return cachedNotifications;
        }

        // 从数据库获取
        List<NotificationMessage> unreadNotifications = 
                notificationRepository.findByUserIdAndIsReadFalseOrderByCreatedAtDesc(userId);

        List<NotificationMessageDTO> notificationDTOs = unreadNotifications.stream()
                .map(NotificationMessageDTO::from)
                .collect(Collectors.toList());

        // 缓存结果
        redisTemplate.opsForValue().set(cacheKey, notificationDTOs, CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);

        return notificationDTOs;
    }

    /**
     * 标记通知为已读
     */
    public void markNotificationAsRead(Long notificationId, Long userId) {
        log.debug("标记通知已读: notificationId={}, userId={}", notificationId, userId);

        NotificationMessage notification = notificationRepository.findById(notificationId)
                .orElseThrow(() -> new BusinessException("通知不存在"));

        if (!notification.getUserId().equals(userId)) {
            throw new BusinessException("无权限操作此通知");
        }

        if (!notification.getIsRead()) {
            notification.markAsRead();
            notificationRepository.save(notification);

            // 清除缓存
            clearUserNotificationsCache(userId);
        }
    }

    /**
     * 批量标记通知为已读
     */
    public void markNotificationsAsRead(List<Long> notificationIds, Long userId) {
        log.info("批量标记通知已读: count={}, userId={}", notificationIds.size(), userId);

        List<NotificationMessage> notifications = notificationRepository.findAllById(notificationIds);

        notifications.stream()
                .filter(notification -> notification.getUserId().equals(userId))
                .filter(notification -> !notification.getIsRead())
                .forEach(notification -> {
                    notification.markAsRead();
                });

        notificationRepository.saveAll(notifications);

        // 清除缓存
        clearUserNotificationsCache(userId);
    }

    /**
     * 标记所有通知为已读
     */
    public void markAllNotificationsAsRead(Long userId) {
        log.info("标记所有通知已读: userId={}", userId);

        List<NotificationMessage> unreadNotifications = 
                notificationRepository.findByUserIdAndIsReadFalse(userId);

        unreadNotifications.forEach(NotificationMessage::markAsRead);
        notificationRepository.saveAll(unreadNotifications);

        // 清除缓存
        clearUserNotificationsCache(userId);
    }

    /**
     * 删除通知
     */
    public void deleteNotification(Long notificationId, Long userId) {
        log.info("删除通知: notificationId={}, userId={}", notificationId, userId);

        NotificationMessage notification = notificationRepository.findById(notificationId)
                .orElseThrow(() -> new BusinessException("通知不存在"));

        if (!notification.getUserId().equals(userId)) {
            throw new BusinessException("无权限删除此通知");
        }

        // 软删除
        notification.setDeleted(1);
        notificationRepository.save(notification);

        // 清除缓存
        clearUserNotificationsCache(userId);
    }

    /**
     * 获取用户未读通知数量
     */
    @Transactional(readOnly = true)
    public Long getUserUnreadCount(Long userId) {
        log.debug("获取用户未读通知数量: userId={}", userId);

        // 先从缓存获取
        String cacheKey = USER_NOTIFICATIONS_CACHE_KEY + userId + ":unread:count";
        Long cachedCount = (Long) redisTemplate.opsForValue().get(cacheKey);
        if (cachedCount != null) {
            return cachedCount;
        }

        // 从数据库获取
        Long unreadCount = notificationRepository.countByUserIdAndIsReadFalse(userId);

        // 缓存结果
        redisTemplate.opsForValue().set(cacheKey, unreadCount, CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);

        return unreadCount;
    }

    /**
     * 重新发送失败的通知
     */
    public void resendFailedNotification(Long notificationId) {
        log.info("重新发送失败通知: notificationId={}", notificationId);

        NotificationMessage notification = notificationRepository.findById(notificationId)
                .orElseThrow(() -> new BusinessException("通知不存在"));

        if (!notification.canRetry()) {
            throw new BusinessException("通知不能重试");
        }

        // 重置状态
        notification.setStatus(NotificationStatus.PENDING);
        notification.setErrorMessage(null);
        notificationRepository.save(notification);

        // 异步重新发送
        sendNotificationAsync(notificationId);
    }

    /**
     * 异步发送通知
     */
    @Async
    public CompletableFuture<Void> sendNotificationAsync(Long notificationId) {
        try {
            log.debug("异步发送通知: notificationId={}", notificationId);

            NotificationMessage notification = notificationRepository.findById(notificationId)
                    .orElseThrow(() -> new BusinessException("通知不存在"));

            // 检查是否应该发送
            if (!shouldSendNotification(notification)) {
                log.debug("通知不需要发送: notificationId={}", notificationId);
                return CompletableFuture.completedFuture(null);
            }

            // 发送通知
            senderService.sendNotification(notification);

            log.debug("通知发送完成: notificationId={}", notificationId);

        } catch (Exception e) {
            log.error("通知发送失败: notificationId={}", notificationId, e);
            
            // 更新失败状态
            NotificationMessage notification = notificationRepository.findById(notificationId).orElse(null);
            if (notification != null) {
                notification.incrementRetryCount();
                notification.markAsFailed(e.getMessage());
                notificationRepository.save(notification);
            }
        }

        return CompletableFuture.completedFuture(null);
    }

    /**
     * 创建通知消息
     */
    private NotificationMessage createNotificationMessage(NotificationSendRequest request) {
        NotificationMessage.NotificationMessageBuilder builder = NotificationMessage.builder()
                .userId(request.getUserId())
                .notificationType(request.getNotificationType())
                .notificationChannel(request.getNotificationChannel())
                .priority(request.getPriority() != null ? request.getPriority() : 
                         request.getNotificationType().getDefaultPriority())
                .title(request.getTitle())
                .content(request.getContent())
                .richContent(request.getRichContent())
                .notificationData(request.getNotificationData())
                .templateId(request.getTemplateId())
                .templateParams(request.getTemplateParams())
                .recipientInfo(request.getRecipientInfo())
                .senderId(request.getSenderId())
                .senderInfo(request.getSenderInfo())
                .scheduledTime(request.getScheduledTime())
                .requiresConfirmation(request.getRequiresConfirmation() != null ? 
                                    request.getRequiresConfirmation() : 
                                    request.getNotificationType().requiresConfirmation())
                .maxRetries(request.getMaxRetries() != null ? request.getMaxRetries() : 
                           request.getPriority() != null ? request.getPriority().getMaxRetries() : 3)
                .pushToken(request.getPushToken())
                .emailAddress(request.getEmailAddress())
                .phoneNumber(request.getPhoneNumber())
                .attachments(request.getAttachments())
                .clickAction(request.getClickAction());

        // 设置过期时间
        if (request.getExpiresAt() != null) {
            builder.expiresAt(request.getExpiresAt());
        } else {
            int expiryHours = request.getNotificationType().getDefaultExpiryHours();
            builder.expiresAt(LocalDateTime.now().plusHours(expiryHours));
        }

        NotificationMessage notification = builder.build();

        // 添加标签
        if (request.getTags() != null) {
            request.getTags().forEach(notification::addTag);
        }

        return notification;
    }

    /**
     * 检查是否应该发送通知
     */
    private boolean shouldSendNotification(NotificationMessage notification) {
        // 检查是否过期
        if (notification.isExpired()) {
            notification.setStatus(NotificationStatus.EXPIRED);
            notificationRepository.save(notification);
            return false;
        }

        // 检查是否已取消
        if (notification.getStatus() == NotificationStatus.CANCELLED) {
            return false;
        }

        // 检查是否为计划发送且时间未到
        if (notification.getScheduledTime() != null && 
            notification.getScheduledTime().isAfter(LocalDateTime.now()) &&
            !notification.isHighPriority()) {
            return false;
        }

        return true;
    }

    /**
     * 清除用户通知缓存
     */
    private void clearUserNotificationsCache(Long userId) {
        String pattern = USER_NOTIFICATIONS_CACHE_KEY + userId + ":*";
        redisTemplate.delete(redisTemplate.keys(pattern));
    }

}
