package com.chiron.smartlearnsystem.notification.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 通知类型枚举
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum NotificationType {

    /**
     * 系统通知
     */
    SYSTEM_NOTIFICATION("SYSTEM_NOTIFICATION", "系统通知", "系统相关的重要通知"),

    /**
     * 学习提醒
     */
    STUDY_REMINDER("STUDY_REMINDER", "学习提醒", "提醒用户进行学习"),

    /**
     * 作业提醒
     */
    HOMEWORK_REMINDER("HOMEWORK_REMINDER", "作业提醒", "提醒用户完成作业"),

    /**
     * 考试通知
     */
    EXAM_NOTIFICATION("EXAM_NOTIFICATION", "考试通知", "考试相关的通知"),

    /**
     * 成绩通知
     */
    GRADE_NOTIFICATION("GRADE_NOTIFICATION", "成绩通知", "成绩发布通知"),

    /**
     * 进度通知
     */
    PROGRESS_NOTIFICATION("PROGRESS_NOTIFICATION", "进度通知", "学习进度相关通知"),

    /**
     * 成就通知
     */
    ACHIEVEMENT_NOTIFICATION("ACHIEVEMENT_NOTIFICATION", "成就通知", "获得成就的通知"),

    /**
     * 课程通知
     */
    COURSE_NOTIFICATION("COURSE_NOTIFICATION", "课程通知", "课程相关通知"),

    /**
     * 计划提醒
     */
    PLAN_REMINDER("PLAN_REMINDER", "计划提醒", "学习计划提醒"),

    /**
     * 复习提醒
     */
    REVIEW_REMINDER("REVIEW_REMINDER", "复习提醒", "复习时间提醒"),

    /**
     * 社交通知
     */
    SOCIAL_NOTIFICATION("SOCIAL_NOTIFICATION", "社交通知", "社交互动通知"),

    /**
     * 营销通知
     */
    MARKETING_NOTIFICATION("MARKETING_NOTIFICATION", "营销通知", "营销活动通知"),

    /**
     * 安全通知
     */
    SECURITY_NOTIFICATION("SECURITY_NOTIFICATION", "安全通知", "账户安全相关通知"),

    /**
     * 维护通知
     */
    MAINTENANCE_NOTIFICATION("MAINTENANCE_NOTIFICATION", "维护通知", "系统维护通知"),

    /**
     * 自定义通知
     */
    CUSTOM_NOTIFICATION("CUSTOM_NOTIFICATION", "自定义通知", "用户自定义通知");

    private final String code;
    private final String name;
    private final String description;

    /**
     * 根据代码获取通知类型
     */
    public static NotificationType getByCode(String code) {
        for (NotificationType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return SYSTEM_NOTIFICATION; // 默认返回系统通知
    }

    /**
     * 是否为学习相关通知
     */
    public boolean isLearningRelated() {
        return this == STUDY_REMINDER || this == HOMEWORK_REMINDER || 
               this == PLAN_REMINDER || this == REVIEW_REMINDER ||
               this == PROGRESS_NOTIFICATION || this == COURSE_NOTIFICATION;
    }

    /**
     * 是否为考试相关通知
     */
    public boolean isExamRelated() {
        return this == EXAM_NOTIFICATION || this == GRADE_NOTIFICATION;
    }

    /**
     * 是否为系统通知
     */
    public boolean isSystemNotification() {
        return this == SYSTEM_NOTIFICATION || this == SECURITY_NOTIFICATION || 
               this == MAINTENANCE_NOTIFICATION;
    }

    /**
     * 是否为社交通知
     */
    public boolean isSocialNotification() {
        return this == SOCIAL_NOTIFICATION || this == ACHIEVEMENT_NOTIFICATION;
    }

    /**
     * 获取默认优先级
     */
    public NotificationPriority getDefaultPriority() {
        switch (this) {
            case SECURITY_NOTIFICATION:
            case MAINTENANCE_NOTIFICATION:
                return NotificationPriority.HIGH;
            case EXAM_NOTIFICATION:
            case GRADE_NOTIFICATION:
                return NotificationPriority.HIGH;
            case STUDY_REMINDER:
            case HOMEWORK_REMINDER:
            case PLAN_REMINDER:
            case REVIEW_REMINDER:
                return NotificationPriority.NORMAL;
            case PROGRESS_NOTIFICATION:
            case ACHIEVEMENT_NOTIFICATION:
            case COURSE_NOTIFICATION:
                return NotificationPriority.NORMAL;
            case SOCIAL_NOTIFICATION:
            case MARKETING_NOTIFICATION:
                return NotificationPriority.LOW;
            case SYSTEM_NOTIFICATION:
            case CUSTOM_NOTIFICATION:
                return NotificationPriority.NORMAL;
            default:
                return NotificationPriority.NORMAL;
        }
    }

    /**
     * 获取默认过期时间（小时）
     */
    public int getDefaultExpiryHours() {
        switch (this) {
            case STUDY_REMINDER:
            case HOMEWORK_REMINDER:
            case PLAN_REMINDER:
            case REVIEW_REMINDER:
                return 24; // 学习提醒24小时过期
            case EXAM_NOTIFICATION:
                return 168; // 考试通知7天过期
            case GRADE_NOTIFICATION:
                return 720; // 成绩通知30天过期
            case PROGRESS_NOTIFICATION:
            case ACHIEVEMENT_NOTIFICATION:
                return 168; // 进度和成就通知7天过期
            case COURSE_NOTIFICATION:
                return 72; // 课程通知3天过期
            case SOCIAL_NOTIFICATION:
                return 168; // 社交通知7天过期
            case MARKETING_NOTIFICATION:
                return 72; // 营销通知3天过期
            case SECURITY_NOTIFICATION:
                return 720; // 安全通知30天过期
            case MAINTENANCE_NOTIFICATION:
                return 48; // 维护通知2天过期
            case SYSTEM_NOTIFICATION:
                return 168; // 系统通知7天过期
            case CUSTOM_NOTIFICATION:
                return 168; // 自定义通知7天过期
            default:
                return 168; // 默认7天过期
        }
    }

    /**
     * 是否支持推送通知
     */
    public boolean supportsPushNotification() {
        return this != MARKETING_NOTIFICATION; // 营销通知不支持推送
    }

    /**
     * 是否支持邮件通知
     */
    public boolean supportsEmailNotification() {
        return true; // 所有类型都支持邮件通知
    }

    /**
     * 是否支持短信通知
     */
    public boolean supportsSmsNotification() {
        return this == SECURITY_NOTIFICATION || this == EXAM_NOTIFICATION || 
               this == MAINTENANCE_NOTIFICATION; // 只有重要通知支持短信
    }

    /**
     * 是否需要用户确认
     */
    public boolean requiresConfirmation() {
        return this == EXAM_NOTIFICATION || this == MAINTENANCE_NOTIFICATION || 
               this == SECURITY_NOTIFICATION;
    }

}
