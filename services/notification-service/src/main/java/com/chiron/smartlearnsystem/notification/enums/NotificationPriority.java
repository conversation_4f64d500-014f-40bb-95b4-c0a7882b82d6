package com.chiron.smartlearnsystem.notification.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 通知优先级枚举
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum NotificationPriority {

    /**
     * 低优先级
     */
    LOW("LOW", "低", "低优先级通知", 1),

    /**
     * 普通优先级
     */
    NORMAL("NORMAL", "普通", "普通优先级通知", 5),

    /**
     * 高优先级
     */
    HIGH("HIGH", "高", "高优先级通知", 8),

    /**
     * 紧急优先级
     */
    URGENT("URGENT", "紧急", "紧急优先级通知", 10);

    private final String code;
    private final String name;
    private final String description;
    private final int level;

    /**
     * 根据代码获取通知优先级
     */
    public static NotificationPriority getByCode(String code) {
        for (NotificationPriority priority : values()) {
            if (priority.getCode().equals(code)) {
                return priority;
            }
        }
        return NORMAL; // 默认返回普通优先级
    }

    /**
     * 根据级别获取通知优先级
     */
    public static NotificationPriority getByLevel(int level) {
        if (level <= 2) return LOW;
        if (level <= 6) return NORMAL;
        if (level <= 9) return HIGH;
        return URGENT;
    }

    /**
     * 是否为高优先级
     */
    public boolean isHighPriority() {
        return this == HIGH || this == URGENT;
    }

    /**
     * 是否为紧急优先级
     */
    public boolean isUrgent() {
        return this == URGENT;
    }

    /**
     * 获取发送延迟（秒）
     */
    public int getSendDelaySeconds() {
        switch (this) {
            case URGENT:
                return 0; // 立即发送
            case HIGH:
                return 30; // 30秒延迟
            case NORMAL:
                return 300; // 5分钟延迟
            case LOW:
                return 1800; // 30分钟延迟
            default:
                return 300;
        }
    }

    /**
     * 获取重试次数
     */
    public int getMaxRetries() {
        switch (this) {
            case URGENT:
                return 5; // 紧急通知最多重试5次
            case HIGH:
                return 3; // 高优先级重试3次
            case NORMAL:
                return 2; // 普通优先级重试2次
            case LOW:
                return 1; // 低优先级重试1次
            default:
                return 2;
        }
    }

    /**
     * 获取过期时间（小时）
     */
    public int getExpiryHours() {
        switch (this) {
            case URGENT:
                return 1; // 紧急通知1小时过期
            case HIGH:
                return 6; // 高优先级6小时过期
            case NORMAL:
                return 24; // 普通优先级24小时过期
            case LOW:
                return 72; // 低优先级72小时过期
            default:
                return 24;
        }
    }

    /**
     * 是否需要立即处理
     */
    public boolean needsImmediateProcessing() {
        return this == URGENT || this == HIGH;
    }

    /**
     * 是否可以批量处理
     */
    public boolean canBatchProcess() {
        return this == LOW || this == NORMAL;
    }

}
