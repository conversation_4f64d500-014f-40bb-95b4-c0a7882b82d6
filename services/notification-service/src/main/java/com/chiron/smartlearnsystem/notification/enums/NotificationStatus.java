package com.chiron.smartlearnsystem.notification.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 通知状态枚举
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum NotificationStatus {

    /**
     * 待发送
     */
    PENDING("PENDING", "待发送", "通知等待发送"),

    /**
     * 已计划
     */
    SCHEDULED("SCHEDULED", "已计划", "通知已计划在指定时间发送"),

    /**
     * 发送中
     */
    SENDING("SENDING", "发送中", "通知正在发送"),

    /**
     * 已发送
     */
    SENT("SENT", "已发送", "通知已成功发送"),

    /**
     * 已送达
     */
    DELIVERED("DELIVERED", "已送达", "通知已送达到目标设备"),

    /**
     * 已阅读
     */
    READ("READ", "已阅读", "通知已被用户阅读"),

    /**
     * 发送失败
     */
    FAILED("FAILED", "发送失败", "通知发送失败"),

    /**
     * 已取消
     */
    CANCELLED("CANCELLED", "已取消", "通知发送被取消"),

    /**
     * 已过期
     */
    EXPIRED("EXPIRED", "已过期", "通知已过期"),

    /**
     * 被拒绝
     */
    REJECTED("REJECTED", "被拒绝", "通知被目标系统拒绝");

    private final String code;
    private final String name;
    private final String description;

    /**
     * 根据代码获取通知状态
     */
    public static NotificationStatus getByCode(String code) {
        for (NotificationStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return PENDING; // 默认返回待发送
    }

    /**
     * 是否为进行中状态
     */
    public boolean isInProgress() {
        return this == PENDING || this == SCHEDULED || this == SENDING;
    }

    /**
     * 是否为成功状态
     */
    public boolean isSuccessful() {
        return this == SENT || this == DELIVERED || this == READ;
    }

    /**
     * 是否为失败状态
     */
    public boolean isFailed() {
        return this == FAILED || this == REJECTED;
    }

    /**
     * 是否为最终状态
     */
    public boolean isFinalStatus() {
        return this == SENT || this == DELIVERED || this == READ || 
               this == FAILED || this == CANCELLED || this == EXPIRED || this == REJECTED;
    }

    /**
     * 是否可以重试
     */
    public boolean canRetry() {
        return this == FAILED || this == REJECTED;
    }

    /**
     * 是否可以取消
     */
    public boolean canCancel() {
        return this == PENDING || this == SCHEDULED;
    }

    /**
     * 是否已送达
     */
    public boolean isDelivered() {
        return this == DELIVERED || this == READ;
    }

    /**
     * 是否需要用户交互
     */
    public boolean needsUserInteraction() {
        return this == SENT || this == DELIVERED;
    }

}
