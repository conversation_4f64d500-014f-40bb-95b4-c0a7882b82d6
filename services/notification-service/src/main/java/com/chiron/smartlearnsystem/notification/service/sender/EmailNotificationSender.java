package com.chiron.smartlearnsystem.notification.service.sender;

import com.chiron.smartlearnsystem.notification.entity.NotificationMessage;
import com.chiron.smartlearnsystem.notification.enums.NotificationStatus;
import com.chiron.smartlearnsystem.notification.repository.NotificationMessageRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import jakarta.mail.internet.MimeMessage;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 邮件通知发送器
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class EmailNotificationSender implements NotificationChannelSender {

    private final JavaMailSender mailSender;
    private final NotificationMessageRepository notificationRepository;
    private final TemplateEngine templateEngine;

    @Value("${spring.mail.username:<EMAIL>}")
    private String fromEmail;

    @Value("${smartlearn.notification.email.enabled:true}")
    private boolean emailEnabled;

    @Override
    public boolean send(NotificationMessage notification) {
        log.debug("发送邮件通知: id={}, email={}", notification.getId(), notification.getEmailAddress());

        if (!emailEnabled) {
            log.debug("邮件通知已禁用");
            return false;
        }

        try {
            // 验证通知
            if (!validate(notification)) {
                log.warn("邮件通知验证失败: id={}", notification.getId());
                return false;
            }

            // 创建邮件消息
            MimeMessage mimeMessage = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true, "UTF-8");

            // 设置邮件基本信息
            helper.setFrom(fromEmail);
            helper.setTo(notification.getEmailAddress());
            helper.setSubject(notification.getTitle());

            // 设置邮件内容
            String emailContent = buildEmailContent(notification);
            helper.setText(emailContent, true); // true表示HTML格式

            // 添加附件（如果有）
            addAttachments(helper, notification);

            // 发送邮件
            mailSender.send(mimeMessage);

            // 更新通知状态
            notification.setStatus(NotificationStatus.SENT);
            notification.setSentTime(LocalDateTime.now());
            notificationRepository.save(notification);

            log.debug("邮件通知发送成功: id={}", notification.getId());
            return true;

        } catch (Exception e) {
            log.error("邮件通知发送失败: id={}", notification.getId(), e);
            return false;
        }
    }

    @Override
    public boolean validate(NotificationMessage notification) {
        if (!NotificationChannelSender.super.validate(notification)) {
            return false;
        }

        // 检查邮件地址
        if (notification.getEmailAddress() == null || notification.getEmailAddress().trim().isEmpty()) {
            log.warn("邮件通知缺少邮件地址: id={}", notification.getId());
            return false;
        }

        // 验证邮件地址格式
        if (!isValidEmail(notification.getEmailAddress())) {
            log.warn("邮件地址格式无效: id={}, email={}", 
                    notification.getId(), notification.getEmailAddress());
            return false;
        }

        return true;
    }

    @Override
    public String getSenderName() {
        return "EmailNotificationSender";
    }

    @Override
    public boolean isEnabled() {
        return emailEnabled;
    }

    /**
     * 构建邮件内容
     */
    private String buildEmailContent(NotificationMessage notification) {
        // 如果有富文本内容，直接使用
        if (notification.getRichContent() != null && !notification.getRichContent().trim().isEmpty()) {
            return notification.getRichContent();
        }

        // 如果有模板ID，使用模板引擎渲染
        if (notification.getTemplateId() != null) {
            return renderTemplate(notification);
        }

        // 否则使用默认HTML模板
        return buildDefaultHtmlContent(notification);
    }

    /**
     * 使用模板引擎渲染邮件内容
     */
    private String renderTemplate(NotificationMessage notification) {
        try {
            Context context = new Context();
            context.setVariable("title", notification.getTitle());
            context.setVariable("content", notification.getContent());
            context.setVariable("notificationData", notification.getNotificationData());
            
            // 解析模板参数
            if (notification.getTemplateParams() != null) {
                // TODO: 解析JSON参数并添加到context
            }

            return templateEngine.process(notification.getTemplateId(), context);
        } catch (Exception e) {
            log.warn("模板渲染失败，使用默认内容: templateId={}", notification.getTemplateId(), e);
            return buildDefaultHtmlContent(notification);
        }
    }

    /**
     * 构建默认HTML内容
     */
    private String buildDefaultHtmlContent(NotificationMessage notification) {
        StringBuilder html = new StringBuilder();
        html.append("<!DOCTYPE html>");
        html.append("<html>");
        html.append("<head>");
        html.append("<meta charset=\"UTF-8\">");
        html.append("<title>").append(notification.getTitle()).append("</title>");
        html.append("<style>");
        html.append("body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }");
        html.append(".container { max-width: 600px; margin: 0 auto; padding: 20px; }");
        html.append(".header { background-color: #007bff; color: white; padding: 20px; text-align: center; }");
        html.append(".content { padding: 20px; background-color: #f8f9fa; }");
        html.append(".footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }");
        html.append("</style>");
        html.append("</head>");
        html.append("<body>");
        html.append("<div class=\"container\">");
        html.append("<div class=\"header\">");
        html.append("<h1>").append(notification.getTitle()).append("</h1>");
        html.append("</div>");
        html.append("<div class=\"content\">");
        html.append("<p>").append(notification.getContent().replace("\n", "<br>")).append("</p>");
        html.append("</div>");
        html.append("<div class=\"footer\">");
        html.append("<p>此邮件由SmartLearn智能学习系统自动发送，请勿回复。</p>");
        html.append("</div>");
        html.append("</div>");
        html.append("</body>");
        html.append("</html>");
        
        return html.toString();
    }

    /**
     * 添加附件
     */
    private void addAttachments(MimeMessageHelper helper, NotificationMessage notification) {
        // TODO: 实现附件添加逻辑
        if (notification.getAttachments() != null) {
            log.debug("邮件包含附件: id={}", notification.getId());
            // 解析附件JSON并添加到邮件
        }
    }

    /**
     * 验证邮件地址格式
     */
    private boolean isValidEmail(String email) {
        String emailRegex = "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$";
        return email.matches(emailRegex);
    }

}
