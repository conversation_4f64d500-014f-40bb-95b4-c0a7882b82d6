package com.chiron.smartlearnsystem.notification.service.sender;

import com.chiron.smartlearnsystem.notification.entity.NotificationMessage;

import java.util.List;

/**
 * 通知渠道发送器接口
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
public interface NotificationChannelSender {

    /**
     * 发送单个通知
     *
     * @param notification 通知消息
     * @return 发送是否成功
     */
    boolean send(NotificationMessage notification);

    /**
     * 批量发送通知
     *
     * @param notifications 通知消息列表
     * @return 发送是否成功
     */
    default boolean sendBatch(List<NotificationMessage> notifications) {
        return notifications.stream()
                .allMatch(this::send);
    }

    /**
     * 是否支持批量发送
     *
     * @return 是否支持批量发送
     */
    default boolean supportsBatchSend() {
        return false;
    }

    /**
     * 验证通知消息
     *
     * @param notification 通知消息
     * @return 验证是否通过
     */
    default boolean validate(NotificationMessage notification) {
        return notification != null && 
               notification.getTitle() != null && 
               notification.getContent() != null;
    }

    /**
     * 获取发送器名称
     *
     * @return 发送器名称
     */
    String getSenderName();

    /**
     * 是否启用
     *
     * @return 是否启用
     */
    default boolean isEnabled() {
        return true;
    }

}
