package com.chiron.smartlearnsystem.notification.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 通知渠道枚举
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum NotificationChannel {

    /**
     * 应用内通知
     */
    IN_APP("IN_APP", "应用内通知", "在应用内显示的通知"),

    /**
     * 推送通知
     */
    PUSH("PUSH", "推送通知", "移动设备推送通知"),

    /**
     * 邮件通知
     */
    EMAIL("EMAIL", "邮件通知", "电子邮件通知"),

    /**
     * 短信通知
     */
    SMS("SMS", "短信通知", "手机短信通知"),

    /**
     * WebSocket实时通知
     */
    WEBSOCKET("WEBSOCKET", "实时通知", "WebSocket实时通知"),

    /**
     * 微信通知
     */
    WECHAT("WECHAT", "微信通知", "微信消息通知"),

    /**
     * 钉钉通知
     */
    DINGTALK("DINGTALK", "钉钉通知", "钉钉消息通知"),

    /**
     * 企业微信通知
     */
    WORK_WECHAT("WORK_WECHAT", "企业微信通知", "企业微信消息通知");

    private final String code;
    private final String name;
    private final String description;

    /**
     * 根据代码获取通知渠道
     */
    public static NotificationChannel getByCode(String code) {
        for (NotificationChannel channel : values()) {
            if (channel.getCode().equals(code)) {
                return channel;
            }
        }
        return IN_APP; // 默认返回应用内通知
    }

    /**
     * 是否为实时通知
     */
    public boolean isRealTime() {
        return this == IN_APP || this == WEBSOCKET || this == PUSH;
    }

    /**
     * 是否为异步通知
     */
    public boolean isAsynchronous() {
        return this == EMAIL || this == SMS;
    }

    /**
     * 是否为第三方通知
     */
    public boolean isThirdParty() {
        return this == WECHAT || this == DINGTALK || this == WORK_WECHAT;
    }

    /**
     * 是否支持富文本
     */
    public boolean supportsRichContent() {
        return this == EMAIL || this == IN_APP || this == WECHAT || 
               this == DINGTALK || this == WORK_WECHAT;
    }

    /**
     * 是否支持附件
     */
    public boolean supportsAttachments() {
        return this == EMAIL || this == WECHAT || this == DINGTALK || this == WORK_WECHAT;
    }

    /**
     * 是否支持点击动作
     */
    public boolean supportsClickAction() {
        return this == IN_APP || this == PUSH || this == WEBSOCKET;
    }

    /**
     * 获取最大内容长度
     */
    public int getMaxContentLength() {
        switch (this) {
            case SMS:
                return 70; // 短信70字符
            case PUSH:
                return 200; // 推送通知200字符
            case IN_APP:
            case WEBSOCKET:
                return 1000; // 应用内通知1000字符
            case EMAIL:
            case WECHAT:
            case DINGTALK:
            case WORK_WECHAT:
                return 10000; // 邮件和第三方通知10000字符
            default:
                return 1000;
        }
    }

    /**
     * 获取发送优先级（1-10，10最高）
     */
    public int getSendPriority() {
        switch (this) {
            case WEBSOCKET:
            case IN_APP:
                return 10; // 实时通知最高优先级
            case PUSH:
                return 9; // 推送通知很高优先级
            case SMS:
                return 8; // 短信高优先级
            case EMAIL:
                return 6; // 邮件中等优先级
            case WECHAT:
            case WORK_WECHAT:
                return 5; // 微信类通知中等优先级
            case DINGTALK:
                return 4; // 钉钉较低优先级
            default:
                return 5;
        }
    }

    /**
     * 获取重试间隔（分钟）
     */
    public int getRetryIntervalMinutes() {
        switch (this) {
            case IN_APP:
            case WEBSOCKET:
                return 1; // 实时通知1分钟重试
            case PUSH:
                return 5; // 推送通知5分钟重试
            case SMS:
                return 10; // 短信10分钟重试
            case EMAIL:
                return 30; // 邮件30分钟重试
            case WECHAT:
            case DINGTALK:
            case WORK_WECHAT:
                return 15; // 第三方通知15分钟重试
            default:
                return 15;
        }
    }

    /**
     * 是否需要外部配置
     */
    public boolean requiresExternalConfig() {
        return this == SMS || this == WECHAT || this == DINGTALK || this == WORK_WECHAT;
    }

}
