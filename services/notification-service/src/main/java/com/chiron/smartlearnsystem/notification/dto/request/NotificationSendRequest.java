package com.chiron.smartlearnsystem.notification.dto.request;

import com.chiron.smartlearnsystem.notification.enums.NotificationChannel;
import com.chiron.smartlearnsystem.notification.enums.NotificationPriority;
import com.chiron.smartlearnsystem.notification.enums.NotificationType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.Set;

/**
 * 通知发送请求
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationSendRequest {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 通知类型
     */
    @NotNull(message = "通知类型不能为空")
    private NotificationType notificationType;

    /**
     * 通知渠道
     */
    @NotNull(message = "通知渠道不能为空")
    private NotificationChannel notificationChannel;

    /**
     * 优先级
     */
    private NotificationPriority priority;

    /**
     * 通知标题
     */
    @NotNull(message = "通知标题不能为空")
    @Size(max = 200, message = "通知标题长度不能超过200个字符")
    private String title;

    /**
     * 通知内容
     */
    @NotNull(message = "通知内容不能为空")
    @Size(max = 10000, message = "通知内容长度不能超过10000个字符")
    private String content;

    /**
     * 富文本内容
     */
    private String richContent;

    /**
     * 通知数据（JSON格式）
     */
    private String notificationData;

    /**
     * 模板ID
     */
    @Size(max = 50, message = "模板ID长度不能超过50个字符")
    private String templateId;

    /**
     * 模板参数（JSON格式）
     */
    private String templateParams;

    /**
     * 收件人信息（JSON格式）
     */
    private String recipientInfo;

    /**
     * 发送者ID
     */
    private Long senderId;

    /**
     * 发送者信息
     */
    @Size(max = 200, message = "发送者信息长度不能超过200个字符")
    private String senderInfo;

    /**
     * 计划发送时间
     */
    private LocalDateTime scheduledTime;

    /**
     * 过期时间
     */
    private LocalDateTime expiresAt;

    /**
     * 是否需要确认
     */
    private Boolean requiresConfirmation;

    /**
     * 最大重试次数
     */
    private Integer maxRetries;

    /**
     * 推送令牌（用于推送通知）
     */
    @Size(max = 500, message = "推送令牌长度不能超过500个字符")
    private String pushToken;

    /**
     * 邮件地址（用于邮件通知）
     */
    @Size(max = 100, message = "邮件地址长度不能超过100个字符")
    private String emailAddress;

    /**
     * 手机号码（用于短信通知）
     */
    @Size(max = 20, message = "手机号码长度不能超过20个字符")
    private String phoneNumber;

    /**
     * 通知标签
     */
    @Size(max = 10, message = "通知标签数量不能超过10个")
    private Set<String> tags;

    /**
     * 附件信息（JSON格式）
     */
    private String attachments;

    /**
     * 点击动作（JSON格式）
     */
    private String clickAction;

    /**
     * 验证请求参数
     */
    public boolean isValid() {
        // 基本验证
        if (userId == null || notificationType == null || notificationChannel == null ||
            title == null || title.trim().isEmpty() ||
            content == null || content.trim().isEmpty()) {
            return false;
        }

        // 渠道特定验证
        switch (notificationChannel) {
            case EMAIL:
                return emailAddress != null && !emailAddress.trim().isEmpty();
            case SMS:
                return phoneNumber != null && !phoneNumber.trim().isEmpty();
            case PUSH:
                return pushToken != null && !pushToken.trim().isEmpty();
            case IN_APP:
            case WEBSOCKET:
                return true; // 这些渠道只需要用户ID
            default:
                return true;
        }
    }

    /**
     * 获取内容长度限制
     */
    public int getContentLengthLimit() {
        return notificationChannel != null ? notificationChannel.getMaxContentLength() : 1000;
    }

    /**
     * 检查内容是否超长
     */
    public boolean isContentTooLong() {
        return content != null && content.length() > getContentLengthLimit();
    }

    /**
     * 截断内容到限制长度
     */
    public void truncateContentIfNeeded() {
        if (isContentTooLong()) {
            int limit = getContentLengthLimit();
            this.content = content.substring(0, limit - 3) + "...";
        }
    }

}
