package com.chiron.smartlearnsystem.notification.service.sender;

import com.chiron.smartlearnsystem.notification.entity.NotificationMessage;
import com.chiron.smartlearnsystem.notification.enums.NotificationStatus;
import com.chiron.smartlearnsystem.notification.repository.NotificationMessageRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 短信通知发送器
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SmsNotificationSender implements NotificationChannelSender {

    private final NotificationMessageRepository notificationRepository;

    @Value("${smartlearn.notification.sms.enabled:false}")
    private boolean smsEnabled;

    @Override
    public boolean send(NotificationMessage notification) {
        log.debug("发送短信通知: id={}, phone={}", notification.getId(), 
                notification.getPhoneNumber() != null ? maskPhoneNumber(notification.getPhoneNumber()) : "null");

        if (!smsEnabled) {
            log.debug("短信通知已禁用");
            return false;
        }

        try {
            // 验证通知
            if (!validate(notification)) {
                log.warn("短信通知验证失败: id={}", notification.getId());
                return false;
            }

            // TODO: 集成短信服务提供商（如阿里云、腾讯云等）
            // 这里是模拟实现
            boolean success = sendSmsMessage(notification);

            if (success) {
                // 更新通知状态
                notification.setStatus(NotificationStatus.SENT);
                notification.setSentTime(LocalDateTime.now());
                notificationRepository.save(notification);

                log.debug("短信通知发送成功: id={}", notification.getId());
                return true;
            } else {
                log.warn("短信通知发送失败: id={}", notification.getId());
                return false;
            }

        } catch (Exception e) {
            log.error("短信通知发送异常: id={}", notification.getId(), e);
            return false;
        }
    }

    @Override
    public boolean validate(NotificationMessage notification) {
        if (!NotificationChannelSender.super.validate(notification)) {
            return false;
        }

        // 检查手机号码
        if (notification.getPhoneNumber() == null || notification.getPhoneNumber().trim().isEmpty()) {
            log.warn("短信通知缺少手机号码: id={}", notification.getId());
            return false;
        }

        // 验证手机号码格式
        if (!isValidPhoneNumber(notification.getPhoneNumber())) {
            log.warn("手机号码格式无效: id={}, phone={}", 
                    notification.getId(), maskPhoneNumber(notification.getPhoneNumber()));
            return false;
        }

        // 检查内容长度
        if (notification.getContent().length() > 70) {
            log.warn("短信通知内容过长: id={}, length={}", 
                    notification.getId(), notification.getContent().length());
            return false;
        }

        return true;
    }

    @Override
    public String getSenderName() {
        return "SmsNotificationSender";
    }

    @Override
    public boolean isEnabled() {
        return smsEnabled;
    }

    /**
     * 发送短信的具体实现
     */
    private boolean sendSmsMessage(NotificationMessage notification) {
        // TODO: 实现具体的短信发送逻辑
        // 1. 构建短信内容
        // 2. 调用短信服务商API
        // 3. 处理发送结果
        
        log.debug("模拟发送短信: phone={}, content={}", 
                maskPhoneNumber(notification.getPhoneNumber()), notification.getContent());
        
        // 模拟成功
        return true;
    }

    /**
     * 验证手机号码格式
     */
    private boolean isValidPhoneNumber(String phoneNumber) {
        // 简单的中国手机号验证
        String phoneRegex = "^1[3-9]\\d{9}$";
        return phoneNumber.matches(phoneRegex);
    }

    /**
     * 掩码手机号码
     */
    private String maskPhoneNumber(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.length() < 7) {
            return "***";
        }
        return phoneNumber.substring(0, 3) + "****" + phoneNumber.substring(phoneNumber.length() - 4);
    }

}
