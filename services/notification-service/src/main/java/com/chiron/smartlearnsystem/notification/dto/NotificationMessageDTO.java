package com.chiron.smartlearnsystem.notification.dto;

import com.chiron.smartlearnsystem.notification.entity.NotificationMessage;
import com.chiron.smartlearnsystem.notification.enums.NotificationChannel;
import com.chiron.smartlearnsystem.notification.enums.NotificationPriority;
import com.chiron.smartlearnsystem.notification.enums.NotificationStatus;
import com.chiron.smartlearnsystem.notification.enums.NotificationType;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * 通知消息DTO
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationMessageDTO {

    /**
     * 通知ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 通知类型
     */
    private NotificationType notificationType;

    /**
     * 通知渠道
     */
    private NotificationChannel notificationChannel;

    /**
     * 通知状态
     */
    private NotificationStatus status;

    /**
     * 优先级
     */
    private NotificationPriority priority;

    /**
     * 通知标题
     */
    private String title;

    /**
     * 通知内容
     */
    private String content;

    /**
     * 富文本内容
     */
    private String richContent;

    /**
     * 通知数据
     */
    private String notificationData;

    /**
     * 模板ID
     */
    private String templateId;

    /**
     * 模板参数
     */
    private String templateParams;

    /**
     * 收件人信息
     */
    private String recipientInfo;

    /**
     * 发送者ID
     */
    private Long senderId;

    /**
     * 发送者信息
     */
    private String senderInfo;

    /**
     * 计划发送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime scheduledTime;

    /**
     * 实际发送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime sentTime;

    /**
     * 阅读时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime readTime;

    /**
     * 过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expiresAt;

    /**
     * 是否已读
     */
    private Boolean isRead;

    /**
     * 是否需要确认
     */
    private Boolean requiresConfirmation;

    /**
     * 确认时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime confirmedAt;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 最大重试次数
     */
    private Integer maxRetries;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 外部消息ID
     */
    private String externalMessageId;

    /**
     * 通知标签
     */
    private Set<String> tags;

    /**
     * 附件信息
     */
    private String attachments;

    /**
     * 点击动作
     */
    private String clickAction;

    /**
     * 统计信息
     */
    private String statistics;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 从NotificationMessage实体转换为DTO
     */
    public static NotificationMessageDTO from(NotificationMessage notification) {
        if (notification == null) {
            return null;
        }

        return NotificationMessageDTO.builder()
                .id(notification.getId())
                .userId(notification.getUserId())
                .notificationType(notification.getNotificationType())
                .notificationChannel(notification.getNotificationChannel())
                .status(notification.getStatus())
                .priority(notification.getPriority())
                .title(notification.getTitle())
                .content(notification.getContent())
                .richContent(notification.getRichContent())
                .notificationData(notification.getNotificationData())
                .templateId(notification.getTemplateId())
                .templateParams(notification.getTemplateParams())
                .recipientInfo(notification.getRecipientInfo())
                .senderId(notification.getSenderId())
                .senderInfo(notification.getSenderInfo())
                .scheduledTime(notification.getScheduledTime())
                .sentTime(notification.getSentTime())
                .readTime(notification.getReadTime())
                .expiresAt(notification.getExpiresAt())
                .isRead(notification.getIsRead())
                .requiresConfirmation(notification.getRequiresConfirmation())
                .confirmedAt(notification.getConfirmedAt())
                .retryCount(notification.getRetryCount())
                .maxRetries(notification.getMaxRetries())
                .errorMessage(notification.getErrorMessage())
                .externalMessageId(notification.getExternalMessageId())
                .tags(notification.getTags())
                .attachments(notification.getAttachments())
                .clickAction(notification.getClickAction())
                .statistics(notification.getStatistics())
                .createdAt(notification.getCreatedAt())
                .updatedAt(notification.getUpdatedAt())
                .build();
    }

    /**
     * 获取通知类型描述
     */
    public String getNotificationTypeDescription() {
        return notificationType != null ? notificationType.getDescription() : "";
    }

    /**
     * 获取通知渠道描述
     */
    public String getNotificationChannelDescription() {
        return notificationChannel != null ? notificationChannel.getDescription() : "";
    }

    /**
     * 获取状态描述
     */
    public String getStatusDescription() {
        return status != null ? status.getDescription() : "";
    }

    /**
     * 获取优先级描述
     */
    public String getPriorityDescription() {
        return priority != null ? priority.getDescription() : "";
    }

    /**
     * 检查是否已过期
     */
    public boolean isExpired() {
        return expiresAt != null && LocalDateTime.now().isAfter(expiresAt);
    }

    /**
     * 检查是否为高优先级
     */
    public boolean isHighPriority() {
        return priority != null && priority.isHighPriority();
    }

    /**
     * 检查是否可以重试
     */
    public boolean canRetry() {
        return retryCount != null && maxRetries != null && 
               retryCount < maxRetries && 
               (status == NotificationStatus.FAILED || status == NotificationStatus.PENDING);
    }

    /**
     * 检查是否已发送
     */
    public boolean isSent() {
        return status != null && status.isSuccessful();
    }

    /**
     * 检查是否发送失败
     */
    public boolean isFailed() {
        return status != null && status.isFailed();
    }

    /**
     * 检查是否需要用户交互
     */
    public boolean needsUserInteraction() {
        return status != null && status.needsUserInteraction();
    }

}
