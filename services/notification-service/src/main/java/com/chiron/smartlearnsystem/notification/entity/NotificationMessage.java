package com.chiron.smartlearnsystem.notification.entity;

import com.chiron.smartlearnsystem.notification.enums.NotificationChannel;
import com.chiron.smartlearnsystem.notification.enums.NotificationPriority;
import com.chiron.smartlearnsystem.notification.enums.NotificationStatus;
import com.chiron.smartlearnsystem.notification.enums.NotificationType;
import com.chiron.smartlearnsystem.common.database.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

/**
 * 通知消息实体
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Entity
@Table(name = "notification_messages", indexes = {
    @Index(name = "idx_user_id", columnList = "user_id"),
    @Index(name = "idx_type", columnList = "notification_type"),
    @Index(name = "idx_channel", columnList = "notification_channel"),
    @Index(name = "idx_status", columnList = "status"),
    @Index(name = "idx_priority", columnList = "priority"),
    @Index(name = "idx_scheduled_time", columnList = "scheduled_time"),
    @Index(name = "idx_created_at", columnList = "created_at")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationMessage extends BaseEntity {

    /**
     * 用户ID
     */
    @Column(name = "user_id", nullable = false)
    private Long userId;

    /**
     * 通知类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "notification_type", nullable = false, length = 30)
    private NotificationType notificationType;

    /**
     * 通知渠道
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "notification_channel", nullable = false, length = 20)
    private NotificationChannel notificationChannel;

    /**
     * 通知状态
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private NotificationStatus status = NotificationStatus.PENDING;

    /**
     * 优先级
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 10)
    private NotificationPriority priority = NotificationPriority.NORMAL;

    /**
     * 通知标题
     */
    @Column(nullable = false, length = 200)
    private String title;

    /**
     * 通知内容
     */
    @Column(columnDefinition = "TEXT")
    private String content;

    /**
     * 富文本内容（HTML格式）
     */
    @Column(name = "rich_content", columnDefinition = "TEXT")
    private String richContent;

    /**
     * 通知数据（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "notification_data", columnDefinition = "JSON")
    private String notificationData;

    /**
     * 模板ID
     */
    @Column(name = "template_id", length = 50)
    private String templateId;

    /**
     * 模板参数（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "template_params", columnDefinition = "JSON")
    private String templateParams;

    /**
     * 收件人信息（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "recipient_info", columnDefinition = "JSON")
    private String recipientInfo;

    /**
     * 发送者ID
     */
    @Column(name = "sender_id")
    private Long senderId;

    /**
     * 发送者信息
     */
    @Column(name = "sender_info", length = 200)
    private String senderInfo;

    /**
     * 计划发送时间
     */
    @Column(name = "scheduled_time")
    private LocalDateTime scheduledTime;

    /**
     * 实际发送时间
     */
    @Column(name = "sent_time")
    private LocalDateTime sentTime;

    /**
     * 阅读时间
     */
    @Column(name = "read_time")
    private LocalDateTime readTime;

    /**
     * 过期时间
     */
    @Column(name = "expires_at")
    private LocalDateTime expiresAt;

    /**
     * 是否已读
     */
    @Column(name = "is_read", nullable = false)
    private Boolean isRead = false;

    /**
     * 是否需要确认
     */
    @Column(name = "requires_confirmation", nullable = false)
    private Boolean requiresConfirmation = false;

    /**
     * 确认时间
     */
    @Column(name = "confirmed_at")
    private LocalDateTime confirmedAt;

    /**
     * 重试次数
     */
    @Column(name = "retry_count", nullable = false)
    private Integer retryCount = 0;

    /**
     * 最大重试次数
     */
    @Column(name = "max_retries", nullable = false)
    private Integer maxRetries = 3;

    /**
     * 错误信息
     */
    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;

    /**
     * 外部消息ID
     */
    @Column(name = "external_message_id", length = 100)
    private String externalMessageId;

    /**
     * 推送令牌
     */
    @Column(name = "push_token", length = 500)
    private String pushToken;

    /**
     * 邮件地址
     */
    @Column(name = "email_address", length = 100)
    private String emailAddress;

    /**
     * 手机号码
     */
    @Column(name = "phone_number", length = 20)
    private String phoneNumber;

    /**
     * 通知标签
     */
    @ElementCollection
    @CollectionTable(
        name = "notification_tags",
        joinColumns = @JoinColumn(name = "notification_id")
    )
    @Column(name = "tag")
    private Set<String> tags = new HashSet<>();

    /**
     * 附件信息（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "attachments", columnDefinition = "JSON")
    private String attachments;

    /**
     * 点击动作（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "click_action", columnDefinition = "JSON")
    private String clickAction;

    /**
     * 统计信息（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "statistics", columnDefinition = "JSON")
    private String statistics;

    /**
     * 标记为已读
     */
    public void markAsRead() {
        this.isRead = true;
        this.readTime = LocalDateTime.now();
    }

    /**
     * 标记为已确认
     */
    public void markAsConfirmed() {
        this.confirmedAt = LocalDateTime.now();
    }

    /**
     * 标记为已发送
     */
    public void markAsSent() {
        this.status = NotificationStatus.SENT;
        this.sentTime = LocalDateTime.now();
    }

    /**
     * 标记为发送失败
     */
    public void markAsFailed(String errorMessage) {
        this.status = NotificationStatus.FAILED;
        this.errorMessage = errorMessage;
    }

    /**
     * 增加重试次数
     */
    public void incrementRetryCount() {
        this.retryCount++;
    }

    /**
     * 检查是否可以重试
     */
    public boolean canRetry() {
        return retryCount < maxRetries && 
               (status == NotificationStatus.FAILED || status == NotificationStatus.PENDING);
    }

    /**
     * 检查是否已过期
     */
    public boolean isExpired() {
        return expiresAt != null && LocalDateTime.now().isAfter(expiresAt);
    }

    /**
     * 检查是否为高优先级
     */
    public boolean isHighPriority() {
        return priority == NotificationPriority.HIGH || priority == NotificationPriority.URGENT;
    }

    /**
     * 检查是否需要立即发送
     */
    public boolean shouldSendImmediately() {
        return scheduledTime == null || scheduledTime.isBefore(LocalDateTime.now()) || isHighPriority();
    }

    /**
     * 添加标签
     */
    public void addTag(String tag) {
        this.tags.add(tag);
    }

    /**
     * 设置计划发送时间
     */
    public void scheduleFor(LocalDateTime scheduledTime) {
        this.scheduledTime = scheduledTime;
        this.status = NotificationStatus.SCHEDULED;
    }

    /**
     * 取消发送
     */
    public void cancel() {
        this.status = NotificationStatus.CANCELLED;
    }

}
