-- 学习报告表
CREATE TABLE learning_reports (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    report_type VARCHAR(30) NOT NULL COMMENT '报告类型',
    status VARCHAR(20) NOT NULL DEFAULT 'GENERATING' COMMENT '报告状态',
    title VARCHAR(200) NOT NULL COMMENT '报告标题',
    description TEXT COMMENT '报告描述',
    report_start_date DATE NOT NULL COMMENT '报告开始日期',
    report_end_date DATE NOT NULL COMMENT '报告结束日期',
    generated_at DATETIME COMMENT '报告生成时间',
    subject_code VARCHAR(20) COMMENT '科目代码',
    report_content JSON COMMENT '报告内容',
    report_summary TEXT COMMENT '报告摘要',
    learning_statistics JSON COMMENT '学习统计数据',
    progress_analysis JSON COMMENT '进步分析',
    weakness_analysis JSON COMMENT '薄弱点分析',
    learning_suggestions JSON COMMENT '学习建议',
    total_study_minutes INT NOT NULL DEFAULT 0 COMMENT '总学习时长（分钟）',
    total_questions INT NOT NULL DEFAULT 0 COMMENT '总答题数量',
    correct_questions INT NOT NULL DEFAULT 0 COMMENT '正确答题数量',
    average_accuracy DECIMAL(5,4) COMMENT '平均正确率',
    study_days INT NOT NULL DEFAULT 0 COMMENT '学习天数',
    consecutive_days INT NOT NULL DEFAULT 0 COMMENT '连续学习天数',
    completed_plans INT NOT NULL DEFAULT 0 COMMENT '完成的学习计划数',
    ability_improvement DECIMAL(5,2) COMMENT '能力提升幅度',
    efficiency_score DECIMAL(3,1) COMMENT '学习效率评分',
    focus_score DECIMAL(3,1) COMMENT '专注度评分',
    persistence_score DECIMAL(3,1) COMMENT '坚持度评分',
    report_file_path VARCHAR(500) COMMENT '报告文件路径',
    report_format VARCHAR(10) COMMENT '报告文件格式',
    report_file_size BIGINT COMMENT '报告文件大小（字节）',
    is_viewed BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否已查看',
    viewed_at DATETIME COMMENT '查看时间',
    view_count INT NOT NULL DEFAULT 0 COMMENT '查看次数',
    is_shared BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否已分享',
    share_link VARCHAR(200) COMMENT '分享链接',
    generation_params JSON COMMENT '生成参数',
    error_message TEXT COMMENT '错误信息',
    generation_duration INT COMMENT '生成耗时（秒）',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT NOT NULL DEFAULT 0,
    version BIGINT NOT NULL DEFAULT 0,
    
    INDEX idx_user_type (user_id, report_type),
    INDEX idx_status (status),
    INDEX idx_report_period (report_start_date, report_end_date),
    INDEX idx_generated_at (generated_at),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学习报告表';

-- 报告标签表
CREATE TABLE learning_report_tags (
    report_id BIGINT NOT NULL,
    tag VARCHAR(50) NOT NULL,
    PRIMARY KEY (report_id, tag),
    FOREIGN KEY (report_id) REFERENCES learning_reports(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='报告标签表';

-- 学习统计表
CREATE TABLE learning_statistics (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    statistics_type VARCHAR(30) NOT NULL COMMENT '统计类型',
    statistics_period VARCHAR(20) NOT NULL COMMENT '统计周期',
    statistics_date DATE NOT NULL COMMENT '统计日期',
    subject_code VARCHAR(20) COMMENT '科目代码',
    study_minutes INT NOT NULL DEFAULT 0 COMMENT '学习时长（分钟）',
    effective_minutes INT NOT NULL DEFAULT 0 COMMENT '有效学习时长（分钟）',
    questions_count INT NOT NULL DEFAULT 0 COMMENT '答题数量',
    correct_count INT NOT NULL DEFAULT 0 COMMENT '正确答题数量',
    accuracy_rate DECIMAL(5,4) COMMENT '正确率',
    avg_answer_time DECIMAL(6,2) COMMENT '平均答题时间（秒）',
    session_count INT NOT NULL DEFAULT 0 COMMENT '学习会话数量',
    completed_plans INT NOT NULL DEFAULT 0 COMMENT '完成的学习计划数',
    study_days INT NOT NULL DEFAULT 0 COMMENT '学习天数',
    consecutive_days INT NOT NULL DEFAULT 0 COMMENT '连续学习天数',
    focus_score DECIMAL(3,1) COMMENT '专注度评分',
    efficiency_score DECIMAL(3,1) COMMENT '学习效率评分',
    ability_improvement DECIMAL(5,2) COMMENT '能力提升幅度',
    mastered_knowledge_points INT NOT NULL DEFAULT 0 COMMENT '知识点掌握数量',
    weak_knowledge_points INT NOT NULL DEFAULT 0 COMMENT '薄弱知识点数量',
    error_count INT NOT NULL DEFAULT 0 COMMENT '错误次数',
    interruption_count INT NOT NULL DEFAULT 0 COMMENT '中断次数',
    pause_minutes INT NOT NULL DEFAULT 0 COMMENT '暂停时长（分钟）',
    study_intensity DECIMAL(3,1) COMMENT '学习强度',
    detailed_statistics JSON COMMENT '详细统计数据',
    knowledge_point_stats JSON COMMENT '知识点统计',
    question_type_stats JSON COMMENT '题型统计',
    difficulty_stats JSON COMMENT '难度统计',
    time_distribution JSON COMMENT '时间分布统计',
    study_pattern_stats JSON COMMENT '学习模式统计',
    ranking_info JSON COMMENT '排名信息',
    comparison_data JSON COMMENT '对比数据',
    generated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '统计生成时间',
    data_source VARCHAR(50) COMMENT '数据来源',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT NOT NULL DEFAULT 0,
    version BIGINT NOT NULL DEFAULT 0,
    
    INDEX idx_user_period (user_id, statistics_period, statistics_date),
    INDEX idx_type (statistics_type),
    INDEX idx_subject (subject_code),
    INDEX idx_date (statistics_date),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学习统计表';

-- 数据分析任务表
CREATE TABLE analytics_tasks (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    task_type VARCHAR(30) NOT NULL COMMENT '任务类型',
    task_name VARCHAR(100) NOT NULL COMMENT '任务名称',
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING' COMMENT '任务状态',
    priority INT NOT NULL DEFAULT 5 COMMENT '任务优先级',
    user_id BIGINT COMMENT '关联用户ID',
    subject_code VARCHAR(20) COMMENT '关联科目',
    start_date DATE COMMENT '开始日期',
    end_date DATE COMMENT '结束日期',
    input_params JSON COMMENT '输入参数',
    output_data JSON COMMENT '输出数据',
    progress_percentage DECIMAL(5,2) DEFAULT 0.00 COMMENT '进度百分比',
    start_time DATETIME COMMENT '开始时间',
    end_time DATETIME COMMENT '结束时间',
    duration_seconds INT COMMENT '执行时长（秒）',
    error_message TEXT COMMENT '错误信息',
    retry_count INT NOT NULL DEFAULT 0 COMMENT '重试次数',
    max_retries INT NOT NULL DEFAULT 3 COMMENT '最大重试次数',
    scheduled_time DATETIME COMMENT '计划执行时间',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT NOT NULL DEFAULT 0,
    version BIGINT NOT NULL DEFAULT 0,
    
    INDEX idx_task_type (task_type),
    INDEX idx_status (status),
    INDEX idx_priority (priority),
    INDEX idx_user_id (user_id),
    INDEX idx_scheduled_time (scheduled_time),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据分析任务表';

-- 学习洞察表
CREATE TABLE learning_insights (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    insight_type VARCHAR(30) NOT NULL COMMENT '洞察类型',
    insight_category VARCHAR(20) NOT NULL COMMENT '洞察分类',
    title VARCHAR(200) NOT NULL COMMENT '洞察标题',
    description TEXT COMMENT '洞察描述',
    insight_data JSON COMMENT '洞察数据',
    confidence_score DECIMAL(5,4) COMMENT '置信度评分',
    importance_level INT NOT NULL DEFAULT 5 COMMENT '重要程度（1-10）',
    subject_code VARCHAR(20) COMMENT '关联科目',
    time_period VARCHAR(20) COMMENT '时间周期',
    start_date DATE COMMENT '开始日期',
    end_date DATE COMMENT '结束日期',
    is_actionable BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否可执行',
    action_suggestions JSON COMMENT '行动建议',
    is_viewed BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否已查看',
    viewed_at DATETIME COMMENT '查看时间',
    is_dismissed BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否已忽略',
    dismissed_at DATETIME COMMENT '忽略时间',
    expires_at DATETIME COMMENT '过期时间',
    generated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '生成时间',
    algorithm_version VARCHAR(20) COMMENT '算法版本',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT NOT NULL DEFAULT 0,
    version BIGINT NOT NULL DEFAULT 0,
    
    INDEX idx_user_type (user_id, insight_type),
    INDEX idx_category (insight_category),
    INDEX idx_importance (importance_level),
    INDEX idx_subject (subject_code),
    INDEX idx_generated_at (generated_at),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学习洞察表';

-- 数据导出记录表
CREATE TABLE data_export_records (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    export_type VARCHAR(30) NOT NULL COMMENT '导出类型',
    export_format VARCHAR(10) NOT NULL COMMENT '导出格式',
    file_name VARCHAR(200) NOT NULL COMMENT '文件名',
    file_path VARCHAR(500) COMMENT '文件路径',
    file_size BIGINT COMMENT '文件大小（字节）',
    status VARCHAR(20) NOT NULL DEFAULT 'PROCESSING' COMMENT '导出状态',
    start_time DATETIME NOT NULL COMMENT '开始时间',
    end_time DATETIME COMMENT '结束时间',
    duration_seconds INT COMMENT '导出时长（秒）',
    download_count INT NOT NULL DEFAULT 0 COMMENT '下载次数',
    last_download_time DATETIME COMMENT '最后下载时间',
    expires_at DATETIME COMMENT '过期时间',
    export_params JSON COMMENT '导出参数',
    error_message TEXT COMMENT '错误信息',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT NOT NULL DEFAULT 0,
    version BIGINT NOT NULL DEFAULT 0,
    
    INDEX idx_user_id (user_id),
    INDEX idx_export_type (export_type),
    INDEX idx_status (status),
    INDEX idx_start_time (start_time),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据导出记录表';
