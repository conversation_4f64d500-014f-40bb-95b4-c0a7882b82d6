server:
  port: 8084

spring:
  application:
    name: analytics-service
  
  profiles:
    active: dev
  
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************************************************
    username: root
    password: 123456
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: AnalyticsServiceHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
  
  redis:
    host: localhost
    port: 6379
    password: 
    database: 4
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0
  
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
    validate-on-migrate: true

eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
    register-with-eureka: true
    fetch-registry: true
  instance:
    prefer-ip-address: true
    lease-renewal-interval-in-seconds: 30
    lease-expiration-duration-in-seconds: 90

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true

logging:
  level:
    com.chiron.smartlearnsystem.analytics: INFO
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/analytics-service.log

# 自定义配置
smartlearn:
  analytics:
    # 报告生成配置
    report:
      storage-path: "/data/reports"
      max-file-size: 52428800  # 50MB
      generation-timeout: 600  # 10分钟
      cleanup-interval: 86400  # 24小时
      retention-days: 90       # 报告保留90天
      formats:
        - JSON
        - PDF
        - EXCEL
    
    # 统计计算配置
    statistics:
      real-time-update-interval: 300  # 5分钟
      batch-size: 1000
      cache-expire-minutes: 30
      trend-analysis-days: 30
      ranking-cache-hours: 1
    
    # 数据聚合配置
    aggregation:
      daily-job-time: "02:00"
      weekly-job-time: "03:00"
      monthly-job-time: "04:00"
      max-processing-time: 3600  # 1小时
      parallel-threads: 4
    
    # ClickHouse配置（用于大数据分析）
    clickhouse:
      enabled: false
      url: *****************************************************
      username: default
      password: 
      batch-size: 10000
    
    # 导出配置
    export:
      excel:
        max-rows: 100000
        template-path: "/templates/excel"
      pdf:
        template-path: "/templates/pdf"
        font-path: "/fonts"
      chart:
        width: 800
        height: 600
        format: PNG
