package com.chiron.smartlearnsystem.analytics.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 统计周期枚举
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum StatisticsPeriod {

    /**
     * 实时
     */
    REAL_TIME("REAL_TIME", "实时", "实时统计数据"),

    /**
     * 小时
     */
    HOURLY("HOURLY", "小时", "按小时统计"),

    /**
     * 日
     */
    DAILY("DAILY", "日", "按日统计"),

    /**
     * 周
     */
    WEEKLY("WEEKLY", "周", "按周统计"),

    /**
     * 月
     */
    MONTHLY("MONTHLY", "月", "按月统计"),

    /**
     * 季度
     */
    QUARTERLY("QUARTERLY", "季度", "按季度统计"),

    /**
     * 年
     */
    YEARLY("YEARLY", "年", "按年统计"),

    /**
     * 自定义
     */
    CUSTOM("CUSTOM", "自定义", "自定义时间范围统计");

    private final String code;
    private final String name;
    private final String description;

    /**
     * 根据代码获取统计周期
     */
    public static StatisticsPeriod getByCode(String code) {
        for (StatisticsPeriod period : values()) {
            if (period.getCode().equals(code)) {
                return period;
            }
        }
        return DAILY; // 默认返回日统计
    }

    /**
     * 是否为短期统计
     */
    public boolean isShortTerm() {
        return this == REAL_TIME || this == HOURLY || this == DAILY;
    }

    /**
     * 是否为长期统计
     */
    public boolean isLongTerm() {
        return this == MONTHLY || this == QUARTERLY || this == YEARLY;
    }

    /**
     * 获取统计间隔（分钟）
     */
    public int getIntervalMinutes() {
        switch (this) {
            case REAL_TIME:
                return 1; // 1分钟
            case HOURLY:
                return 60; // 1小时
            case DAILY:
                return 1440; // 24小时
            case WEEKLY:
                return 10080; // 7天
            case MONTHLY:
                return 43200; // 30天
            case QUARTERLY:
                return 129600; // 90天
            case YEARLY:
                return 525600; // 365天
            case CUSTOM:
                return 1440; // 默认24小时
            default:
                return 1440;
        }
    }

    /**
     * 获取数据保留天数
     */
    public int getRetentionDays() {
        switch (this) {
            case REAL_TIME:
                return 1; // 实时数据保留1天
            case HOURLY:
                return 7; // 小时数据保留7天
            case DAILY:
                return 90; // 日数据保留90天
            case WEEKLY:
                return 365; // 周数据保留1年
            case MONTHLY:
                return 1095; // 月数据保留3年
            case QUARTERLY:
                return 1825; // 季度数据保留5年
            case YEARLY:
                return 3650; // 年数据保留10年
            case CUSTOM:
                return 90; // 自定义数据保留90天
            default:
                return 90;
        }
    }

    /**
     * 获取聚合级别
     */
    public int getAggregationLevel() {
        switch (this) {
            case REAL_TIME:
                return 1; // 最细粒度
            case HOURLY:
                return 2;
            case DAILY:
                return 3;
            case WEEKLY:
                return 4;
            case MONTHLY:
                return 5;
            case QUARTERLY:
                return 6;
            case YEARLY:
                return 7; // 最粗粒度
            case CUSTOM:
                return 3; // 默认日级别
            default:
                return 3;
        }
    }

    /**
     * 是否支持实时计算
     */
    public boolean supportsRealTimeCalculation() {
        return this == REAL_TIME || this == HOURLY || this == DAILY;
    }

    /**
     * 获取显示格式
     */
    public String getDisplayFormat() {
        switch (this) {
            case REAL_TIME:
                return "HH:mm";
            case HOURLY:
                return "MM-dd HH:mm";
            case DAILY:
                return "MM-dd";
            case WEEKLY:
                return "yyyy-MM-dd";
            case MONTHLY:
                return "yyyy-MM";
            case QUARTERLY:
                return "yyyy-QQ";
            case YEARLY:
                return "yyyy";
            case CUSTOM:
                return "yyyy-MM-dd";
            default:
                return "yyyy-MM-dd";
        }
    }

}
