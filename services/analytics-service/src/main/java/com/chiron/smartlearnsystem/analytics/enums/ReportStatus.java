package com.chiron.smartlearnsystem.analytics.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 报告状态枚举
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum ReportStatus {

    /**
     * 生成中
     */
    GENERATING("GENERATING", "生成中", "报告正在生成"),

    /**
     * 已完成
     */
    COMPLETED("COMPLETED", "已完成", "报告生成完成"),

    /**
     * 生成失败
     */
    FAILED("FAILED", "生成失败", "报告生成失败"),

    /**
     * 已取消
     */
    CANCELLED("CANCELLED", "已取消", "报告生成被取消"),

    /**
     * 排队中
     */
    QUEUED("QUEUED", "排队中", "报告在生成队列中等待"),

    /**
     * 已过期
     */
    EXPIRED("EXPIRED", "已过期", "报告已过期"),

    /**
     * 已删除
     */
    DELETED("DELETED", "已删除", "报告已被删除");

    private final String code;
    private final String name;
    private final String description;

    /**
     * 根据代码获取报告状态
     */
    public static ReportStatus getByCode(String code) {
        for (ReportStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return GENERATING; // 默认返回生成中
    }

    /**
     * 是否为进行中状态
     */
    public boolean isInProgress() {
        return this == GENERATING || this == QUEUED;
    }

    /**
     * 是否为完成状态
     */
    public boolean isCompleted() {
        return this == COMPLETED;
    }

    /**
     * 是否为失败状态
     */
    public boolean isFailed() {
        return this == FAILED || this == CANCELLED;
    }

    /**
     * 是否为最终状态
     */
    public boolean isFinalStatus() {
        return this == COMPLETED || this == FAILED || this == CANCELLED || 
               this == EXPIRED || this == DELETED;
    }

    /**
     * 是否可以查看
     */
    public boolean canView() {
        return this == COMPLETED;
    }

    /**
     * 是否可以重新生成
     */
    public boolean canRegenerate() {
        return this == FAILED || this == CANCELLED || this == EXPIRED;
    }

    /**
     * 是否可以取消
     */
    public boolean canCancel() {
        return this == GENERATING || this == QUEUED;
    }

    /**
     * 是否可以删除
     */
    public boolean canDelete() {
        return this == COMPLETED || this == FAILED || this == EXPIRED;
    }

    /**
     * 是否可以分享
     */
    public boolean canShare() {
        return this == COMPLETED;
    }

}
