package com.chiron.smartlearnsystem.analytics.service;

import com.chiron.smartlearnsystem.analytics.dto.LearningReportDTO;
import com.chiron.smartlearnsystem.analytics.dto.request.ReportGenerationRequest;
import com.chiron.smartlearnsystem.analytics.entity.LearningReport;
import com.chiron.smartlearnsystem.analytics.enums.ReportStatus;
import com.chiron.smartlearnsystem.analytics.enums.ReportType;
import com.chiron.smartlearnsystem.analytics.repository.LearningReportRepository;
import com.chiron.smartlearnsystem.common.core.domain.PageResult;
import com.chiron.smartlearnsystem.common.core.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 学习报告服务
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class LearningReportService {

    private final LearningReportRepository reportRepository;
    private final LearningStatisticsService statisticsService;
    private final ReportGeneratorService reportGeneratorService;
    private final RedisTemplate<String, Object> redisTemplate;

    private static final String REPORT_CACHE_KEY = "analytics:report:";
    private static final String USER_REPORTS_CACHE_KEY = "analytics:user:reports:";
    private static final int CACHE_EXPIRE_HOURS = 2;

    /**
     * 生成学习报告
     */
    public LearningReportDTO generateReport(ReportGenerationRequest request) {
        log.info("生成学习报告: userId={}, type={}, period={}-{}", 
                request.getUserId(), request.getReportType(), 
                request.getStartDate(), request.getEndDate());

        // 检查是否已有相同的报告
        LearningReport existingReport = reportRepository
                .findByUserIdAndReportTypeAndReportPeriod(
                        request.getUserId(), 
                        request.getReportType(),
                        request.getStartDate(),
                        request.getEndDate())
                .orElse(null);

        if (existingReport != null && existingReport.isCompleted()) {
            log.info("返回已存在的报告: reportId={}", existingReport.getId());
            return LearningReportDTO.from(existingReport);
        }

        // 创建新的报告记录
        LearningReport report = LearningReport.builder()
                .userId(request.getUserId())
                .reportType(request.getReportType())
                .status(ReportStatus.GENERATING)
                .title(generateReportTitle(request))
                .description(generateReportDescription(request))
                .reportStartDate(request.getStartDate())
                .reportEndDate(request.getEndDate())
                .subjectCode(request.getSubjectCode())
                .generationParams(request.toJsonString())
                .build();

        // 保存报告记录
        LearningReport savedReport = reportRepository.save(report);

        // 异步生成报告内容
        generateReportAsync(savedReport.getId(), request);

        // 清除缓存
        clearUserReportsCache(request.getUserId());

        log.info("报告生成任务已启动: reportId={}", savedReport.getId());

        return LearningReportDTO.from(savedReport);
    }

    /**
     * 获取用户报告列表
     */
    @Transactional(readOnly = true)
    public PageResult<LearningReportDTO> getUserReports(Long userId, Integer page, Integer size) {
        log.info("获取用户报告列表: userId={}", userId);

        Sort sort = Sort.by(Sort.Direction.DESC, "generatedAt", "createdAt");
        Pageable pageable = PageRequest.of(page - 1, size, sort);

        Page<LearningReport> reportPage = reportRepository.findByUserId(userId, pageable);

        List<LearningReportDTO> reportDTOs = reportPage.getContent().stream()
                .map(LearningReportDTO::from)
                .collect(Collectors.toList());

        return PageResult.of(reportDTOs, reportPage.getTotalElements(), 
                           (long) page, (long) size);
    }

    /**
     * 获取报告详情
     */
    @Transactional(readOnly = true)
    public LearningReportDTO getReportById(Long reportId) {
        // 先从缓存获取
        String cacheKey = REPORT_CACHE_KEY + reportId;
        LearningReportDTO cachedReport = (LearningReportDTO) redisTemplate.opsForValue().get(cacheKey);
        if (cachedReport != null) {
            return cachedReport;
        }

        // 从数据库获取
        LearningReport report = reportRepository.findById(reportId)
                .orElseThrow(() -> new BusinessException("报告不存在"));

        LearningReportDTO reportDTO = LearningReportDTO.from(report);

        // 缓存结果
        if (report.isCompleted()) {
            redisTemplate.opsForValue().set(cacheKey, reportDTO, CACHE_EXPIRE_HOURS, TimeUnit.HOURS);
        }

        return reportDTO;
    }

    /**
     * 标记报告为已查看
     */
    public void markReportAsViewed(Long reportId, Long userId) {
        log.debug("标记报告已查看: reportId={}, userId={}", reportId, userId);

        LearningReport report = reportRepository.findById(reportId)
                .orElseThrow(() -> new BusinessException("报告不存在"));

        if (!report.getUserId().equals(userId)) {
            throw new BusinessException("无权限访问此报告");
        }

        report.markAsViewed();
        reportRepository.save(report);

        // 清除缓存
        clearReportCache(reportId);
    }

    /**
     * 重新生成报告
     */
    public LearningReportDTO regenerateReport(Long reportId, Long userId) {
        log.info("重新生成报告: reportId={}, userId={}", reportId, userId);

        LearningReport report = reportRepository.findById(reportId)
                .orElseThrow(() -> new BusinessException("报告不存在"));

        if (!report.getUserId().equals(userId)) {
            throw new BusinessException("无权限操作此报告");
        }

        if (!report.getStatus().canRegenerate()) {
            throw new BusinessException("当前状态的报告不能重新生成");
        }

        // 重置报告状态
        report.setStatus(ReportStatus.GENERATING);
        report.setErrorMessage(null);
        report.setGeneratedAt(null);
        report.setGenerationDuration(null);

        LearningReport savedReport = reportRepository.save(report);

        // 重新生成报告内容
        ReportGenerationRequest request = ReportGenerationRequest.fromReport(report);
        generateReportAsync(savedReport.getId(), request);

        // 清除缓存
        clearReportCache(reportId);
        clearUserReportsCache(userId);

        log.info("报告重新生成任务已启动: reportId={}", reportId);

        return LearningReportDTO.from(savedReport);
    }

    /**
     * 删除报告
     */
    public void deleteReport(Long reportId, Long userId) {
        log.info("删除报告: reportId={}, userId={}", reportId, userId);

        LearningReport report = reportRepository.findById(reportId)
                .orElseThrow(() -> new BusinessException("报告不存在"));

        if (!report.getUserId().equals(userId)) {
            throw new BusinessException("无权限删除此报告");
        }

        if (!report.getStatus().canDelete()) {
            throw new BusinessException("当前状态的报告不能删除");
        }

        // 软删除
        report.setStatus(ReportStatus.DELETED);
        reportRepository.save(report);

        // 清除缓存
        clearReportCache(reportId);
        clearUserReportsCache(userId);

        log.info("报告已删除: reportId={}", reportId);
    }

    /**
     * 获取用户最新报告
     */
    @Transactional(readOnly = true)
    public List<LearningReportDTO> getUserLatestReports(Long userId, ReportType reportType, int limit) {
        log.info("获取用户最新报告: userId={}, type={}, limit={}", userId, reportType, limit);

        Pageable pageable = PageRequest.of(0, limit, Sort.by(Sort.Direction.DESC, "generatedAt"));
        
        List<LearningReport> reports;
        if (reportType != null) {
            reports = reportRepository.findByUserIdAndReportTypeAndStatus(userId, reportType, 
                    ReportStatus.COMPLETED, pageable);
        } else {
            reports = reportRepository.findByUserIdAndStatus(userId, ReportStatus.COMPLETED, pageable);
        }

        return reports.stream()
                .map(LearningReportDTO::from)
                .collect(Collectors.toList());
    }

    /**
     * 异步生成报告内容
     */
    @Async
    public CompletableFuture<Void> generateReportAsync(Long reportId, ReportGenerationRequest request) {
        try {
            log.info("开始异步生成报告: reportId={}", reportId);

            // 获取报告记录
            LearningReport report = reportRepository.findById(reportId)
                    .orElseThrow(() -> new BusinessException("报告不存在"));

            // 生成报告内容
            reportGeneratorService.generateReportContent(report, request);

            // 标记完成
            report.completeGeneration();
            reportRepository.save(report);

            // 清除缓存
            clearReportCache(reportId);
            clearUserReportsCache(request.getUserId());

            log.info("报告生成完成: reportId={}, duration={}s", reportId, report.getGenerationDuration());

        } catch (Exception e) {
            log.error("报告生成失败: reportId={}", reportId, e);

            // 标记失败
            LearningReport report = reportRepository.findById(reportId).orElse(null);
            if (report != null) {
                report.markAsFailed(e.getMessage());
                reportRepository.save(report);
            }
        }

        return CompletableFuture.completedFuture(null);
    }

    /**
     * 生成报告标题
     */
    private String generateReportTitle(ReportGenerationRequest request) {
        String typeName = request.getReportType().getName();
        String period = request.getStartDate() + " 至 " + request.getEndDate();
        
        if (request.getSubjectCode() != null) {
            return String.format("%s - %s (%s)", typeName, request.getSubjectCode(), period);
        } else {
            return String.format("%s (%s)", typeName, period);
        }
    }

    /**
     * 生成报告描述
     */
    private String generateReportDescription(ReportGenerationRequest request) {
        return String.format("用户在%s至%s期间的%s", 
                request.getStartDate(), request.getEndDate(), request.getReportType().getDescription());
    }

    /**
     * 清除报告缓存
     */
    private void clearReportCache(Long reportId) {
        String cacheKey = REPORT_CACHE_KEY + reportId;
        redisTemplate.delete(cacheKey);
    }

    /**
     * 清除用户报告列表缓存
     */
    private void clearUserReportsCache(Long userId) {
        String cacheKey = USER_REPORTS_CACHE_KEY + userId;
        redisTemplate.delete(cacheKey);
    }

}
