package com.chiron.smartlearnsystem.analytics.entity;

import com.chiron.smartlearnsystem.analytics.enums.ReportStatus;
import com.chiron.smartlearnsystem.analytics.enums.ReportType;
import com.chiron.smartlearnsystem.common.database.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

/**
 * 学习报告实体
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Entity
@Table(name = "learning_reports", indexes = {
    @Index(name = "idx_user_type", columnList = "user_id, report_type"),
    @Index(name = "idx_status", columnList = "status"),
    @Index(name = "idx_report_period", columnList = "report_start_date, report_end_date"),
    @Index(name = "idx_generated_at", columnList = "generated_at"),
    @Index(name = "idx_created_at", columnList = "created_at")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LearningReport extends BaseEntity {

    /**
     * 用户ID
     */
    @Column(name = "user_id", nullable = false)
    private Long userId;

    /**
     * 报告类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "report_type", nullable = false, length = 30)
    private ReportType reportType;

    /**
     * 报告状态
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private ReportStatus status = ReportStatus.GENERATING;

    /**
     * 报告标题
     */
    @Column(nullable = false, length = 200)
    private String title;

    /**
     * 报告描述
     */
    @Column(columnDefinition = "TEXT")
    private String description;

    /**
     * 报告开始日期
     */
    @Column(name = "report_start_date", nullable = false)
    private LocalDate reportStartDate;

    /**
     * 报告结束日期
     */
    @Column(name = "report_end_date", nullable = false)
    private LocalDate reportEndDate;

    /**
     * 报告生成时间
     */
    @Column(name = "generated_at")
    private LocalDateTime generatedAt;

    /**
     * 科目代码
     */
    @Column(name = "subject_code", length = 20)
    private String subjectCode;

    /**
     * 报告内容（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "report_content", columnDefinition = "JSON")
    private String reportContent;

    /**
     * 报告摘要
     */
    @Column(name = "report_summary", columnDefinition = "TEXT")
    private String reportSummary;

    /**
     * 学习统计数据（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "learning_statistics", columnDefinition = "JSON")
    private String learningStatistics;

    /**
     * 进步分析（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "progress_analysis", columnDefinition = "JSON")
    private String progressAnalysis;

    /**
     * 薄弱点分析（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "weakness_analysis", columnDefinition = "JSON")
    private String weaknessAnalysis;

    /**
     * 学习建议（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "learning_suggestions", columnDefinition = "JSON")
    private String learningSuggestions;

    /**
     * 总学习时长（分钟）
     */
    @Column(name = "total_study_minutes", nullable = false)
    private Integer totalStudyMinutes = 0;

    /**
     * 总答题数量
     */
    @Column(name = "total_questions", nullable = false)
    private Integer totalQuestions = 0;

    /**
     * 正确答题数量
     */
    @Column(name = "correct_questions", nullable = false)
    private Integer correctQuestions = 0;

    /**
     * 平均正确率
     */
    @Column(name = "average_accuracy", precision = 5, scale = 4)
    private BigDecimal averageAccuracy;

    /**
     * 学习天数
     */
    @Column(name = "study_days", nullable = false)
    private Integer studyDays = 0;

    /**
     * 连续学习天数
     */
    @Column(name = "consecutive_days", nullable = false)
    private Integer consecutiveDays = 0;

    /**
     * 完成的学习计划数
     */
    @Column(name = "completed_plans", nullable = false)
    private Integer completedPlans = 0;

    /**
     * 能力提升幅度
     */
    @Column(name = "ability_improvement", precision = 5, scale = 2)
    private BigDecimal abilityImprovement;

    /**
     * 学习效率评分
     */
    @Column(name = "efficiency_score", precision = 3, scale = 1)
    private BigDecimal efficiencyScore;

    /**
     * 专注度评分
     */
    @Column(name = "focus_score", precision = 3, scale = 1)
    private BigDecimal focusScore;

    /**
     * 坚持度评分
     */
    @Column(name = "persistence_score", precision = 3, scale = 1)
    private BigDecimal persistenceScore;

    /**
     * 报告文件路径
     */
    @Column(name = "report_file_path", length = 500)
    private String reportFilePath;

    /**
     * 报告文件格式
     */
    @Column(name = "report_format", length = 10)
    private String reportFormat;

    /**
     * 报告文件大小（字节）
     */
    @Column(name = "report_file_size")
    private Long reportFileSize;

    /**
     * 是否已查看
     */
    @Column(name = "is_viewed", nullable = false)
    private Boolean isViewed = false;

    /**
     * 查看时间
     */
    @Column(name = "viewed_at")
    private LocalDateTime viewedAt;

    /**
     * 查看次数
     */
    @Column(name = "view_count", nullable = false)
    private Integer viewCount = 0;

    /**
     * 是否已分享
     */
    @Column(name = "is_shared", nullable = false)
    private Boolean isShared = false;

    /**
     * 分享链接
     */
    @Column(name = "share_link", length = 200)
    private String shareLink;

    /**
     * 报告标签
     */
    @ElementCollection
    @CollectionTable(
        name = "learning_report_tags",
        joinColumns = @JoinColumn(name = "report_id")
    )
    @Column(name = "tag")
    private Set<String> tags = new HashSet<>();

    /**
     * 生成参数（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "generation_params", columnDefinition = "JSON")
    private String generationParams;

    /**
     * 错误信息
     */
    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;

    /**
     * 生成耗时（秒）
     */
    @Column(name = "generation_duration")
    private Integer generationDuration;

    /**
     * 标记为已查看
     */
    public void markAsViewed() {
        this.isViewed = true;
        this.viewedAt = LocalDateTime.now();
        this.viewCount++;
    }

    /**
     * 完成报告生成
     */
    public void completeGeneration() {
        this.status = ReportStatus.COMPLETED;
        this.generatedAt = LocalDateTime.now();
        
        if (getCreatedAt() != null) {
            this.generationDuration = (int) java.time.Duration.between(getCreatedAt(), this.generatedAt).getSeconds();
        }
    }

    /**
     * 标记生成失败
     */
    public void markAsFailed(String errorMessage) {
        this.status = ReportStatus.FAILED;
        this.errorMessage = errorMessage;
    }

    /**
     * 设置分享链接
     */
    public void setShareLink(String shareLink) {
        this.shareLink = shareLink;
        this.isShared = true;
    }

    /**
     * 计算正确率
     */
    public void calculateAccuracy() {
        if (totalQuestions > 0) {
            this.averageAccuracy = BigDecimal.valueOf(correctQuestions)
                    .divide(BigDecimal.valueOf(totalQuestions), 4, BigDecimal.ROUND_HALF_UP);
        }
    }

    /**
     * 添加标签
     */
    public void addTag(String tag) {
        this.tags.add(tag);
    }

    /**
     * 检查是否成功生成
     */
    public boolean isCompleted() {
        return ReportStatus.COMPLETED.equals(this.status);
    }

    /**
     * 检查是否生成失败
     */
    public boolean isFailed() {
        return ReportStatus.FAILED.equals(this.status);
    }

    /**
     * 检查是否正在生成
     */
    public boolean isGenerating() {
        return ReportStatus.GENERATING.equals(this.status);
    }

    /**
     * 获取报告周期天数
     */
    public long getReportPeriodDays() {
        return java.time.temporal.ChronoUnit.DAYS.between(reportStartDate, reportEndDate) + 1;
    }

}
