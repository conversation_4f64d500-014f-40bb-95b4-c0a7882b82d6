package com.chiron.smartlearnsystem.analytics.repository;

import com.chiron.smartlearnsystem.analytics.entity.LearningStatistics;
import com.chiron.smartlearnsystem.analytics.enums.StatisticsPeriod;
import com.chiron.smartlearnsystem.analytics.enums.StatisticsType;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * 学习统计数据访问层
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Repository
public interface LearningStatisticsRepository extends JpaRepository<LearningStatistics, Long>, 
                                                    JpaSpecificationExecutor<LearningStatistics> {

    /**
     * 根据用户ID查找统计
     */
    List<LearningStatistics> findByUserId(Long userId);

    /**
     * 根据用户ID和统计日期查找统计
     */
    Optional<LearningStatistics> findByUserIdAndStatisticsDateAndSubjectCode(Long userId, 
                                                                            LocalDate statisticsDate, 
                                                                            String subjectCode);

    /**
     * 根据用户ID和日期范围查找统计
     */
    List<LearningStatistics> findByUserIdAndStatisticsDateBetweenAndSubjectCode(Long userId, 
                                                                               LocalDate startDate, 
                                                                               LocalDate endDate, 
                                                                               String subjectCode);

    /**
     * 根据用户ID、统计类型和周期查找统计
     */
    List<LearningStatistics> findByUserIdAndStatisticsTypeAndStatisticsPeriod(Long userId, 
                                                                             StatisticsType statisticsType, 
                                                                             StatisticsPeriod statisticsPeriod);

    /**
     * 根据统计类型查找统计
     */
    List<LearningStatistics> findByStatisticsType(StatisticsType statisticsType);

    /**
     * 根据统计周期查找统计
     */
    List<LearningStatistics> findByStatisticsPeriod(StatisticsPeriod statisticsPeriod);

    /**
     * 查找用户最新的统计
     */
    @Query("SELECT ls FROM LearningStatistics ls WHERE ls.userId = :userId " +
           "ORDER BY ls.statisticsDate DESC, ls.generatedAt DESC")
    List<LearningStatistics> findLatestStatisticsByUser(@Param("userId") Long userId, Pageable pageable);

    /**
     * 查找用户指定科目的统计
     */
    List<LearningStatistics> findByUserIdAndSubjectCode(Long userId, String subjectCode);

    /**
     * 查找用户今日统计
     */
    @Query("SELECT ls FROM LearningStatistics ls WHERE ls.userId = :userId " +
           "AND ls.statisticsDate = :today AND ls.subjectCode = :subjectCode")
    Optional<LearningStatistics> findTodayStatistics(@Param("userId") Long userId, 
                                                    @Param("today") LocalDate today, 
                                                    @Param("subjectCode") String subjectCode);

    /**
     * 查找用户本周统计
     */
    @Query("SELECT ls FROM LearningStatistics ls WHERE ls.userId = :userId " +
           "AND ls.statisticsDate >= :weekStart AND ls.statisticsDate <= :weekEnd " +
           "AND ls.subjectCode = :subjectCode")
    List<LearningStatistics> findWeeklyStatistics(@Param("userId") Long userId,
                                                 @Param("weekStart") LocalDate weekStart,
                                                 @Param("weekEnd") LocalDate weekEnd,
                                                 @Param("subjectCode") String subjectCode);

    /**
     * 查找用户本月统计
     */
    @Query("SELECT ls FROM LearningStatistics ls WHERE ls.userId = :userId " +
           "AND ls.statisticsDate >= :monthStart AND ls.statisticsDate <= :monthEnd " +
           "AND ls.subjectCode = :subjectCode")
    List<LearningStatistics> findMonthlyStatistics(@Param("userId") Long userId,
                                                  @Param("monthStart") LocalDate monthStart,
                                                  @Param("monthEnd") LocalDate monthEnd,
                                                  @Param("subjectCode") String subjectCode);

    /**
     * 统计用户学习天数
     */
    @Query("SELECT COUNT(DISTINCT ls.statisticsDate) FROM LearningStatistics ls " +
           "WHERE ls.userId = :userId AND ls.studyMinutes > 0")
    Long countStudyDaysByUser(@Param("userId") Long userId);

    /**
     * 统计用户总学习时长
     */
    @Query("SELECT SUM(ls.studyMinutes) FROM LearningStatistics ls WHERE ls.userId = :userId")
    Long sumStudyMinutesByUser(@Param("userId") Long userId);

    /**
     * 统计用户总答题数量
     */
    @Query("SELECT SUM(ls.questionsCount) FROM LearningStatistics ls WHERE ls.userId = :userId")
    Long sumQuestionsByUser(@Param("userId") Long userId);

    /**
     * 计算用户平均正确率
     */
    @Query("SELECT AVG(ls.accuracyRate) FROM LearningStatistics ls " +
           "WHERE ls.userId = :userId AND ls.accuracyRate IS NOT NULL")
    BigDecimal avgAccuracyByUser(@Param("userId") Long userId);

    /**
     * 查找高效率用户统计
     */
    @Query("SELECT ls FROM LearningStatistics ls WHERE ls.efficiencyScore >= :minEfficiency " +
           "ORDER BY ls.efficiencyScore DESC")
    List<LearningStatistics> findHighEfficiencyStatistics(@Param("minEfficiency") BigDecimal minEfficiency, 
                                                         Pageable pageable);

    /**
     * 查找高专注度用户统计
     */
    @Query("SELECT ls FROM LearningStatistics ls WHERE ls.focusScore >= :minFocus " +
           "ORDER BY ls.focusScore DESC")
    List<LearningStatistics> findHighFocusStatistics(@Param("minFocus") BigDecimal minFocus, 
                                                    Pageable pageable);

    /**
     * 统计各科目的学习情况
     */
    @Query("SELECT ls.subjectCode, " +
           "COUNT(DISTINCT ls.userId) as userCount, " +
           "SUM(ls.studyMinutes) as totalMinutes, " +
           "SUM(ls.questionsCount) as totalQuestions, " +
           "AVG(ls.accuracyRate) as avgAccuracy " +
           "FROM LearningStatistics ls " +
           "GROUP BY ls.subjectCode")
    List<Object[]> getSubjectStatistics();

    /**
     * 查找学习活跃用户
     */
    @Query("SELECT ls.userId, SUM(ls.studyMinutes) as totalMinutes " +
           "FROM LearningStatistics ls " +
           "WHERE ls.statisticsDate >= :startDate " +
           "GROUP BY ls.userId " +
           "HAVING totalMinutes >= :minMinutes " +
           "ORDER BY totalMinutes DESC")
    List<Object[]> findActiveUsers(@Param("startDate") LocalDate startDate, 
                                 @Param("minMinutes") Integer minMinutes, 
                                 Pageable pageable);

    /**
     * 查找学习趋势数据
     */
    @Query("SELECT ls.statisticsDate, " +
           "SUM(ls.studyMinutes) as dailyMinutes, " +
           "SUM(ls.questionsCount) as dailyQuestions, " +
           "AVG(ls.accuracyRate) as dailyAccuracy " +
           "FROM LearningStatistics ls " +
           "WHERE ls.userId = :userId AND ls.statisticsDate >= :startDate " +
           "GROUP BY ls.statisticsDate " +
           "ORDER BY ls.statisticsDate")
    List<Object[]> getUserTrendData(@Param("userId") Long userId, @Param("startDate") LocalDate startDate);

    /**
     * 查找连续学习天数最长的用户
     */
    @Query("SELECT ls.userId, MAX(ls.consecutiveDays) as maxConsecutiveDays " +
           "FROM LearningStatistics ls " +
           "GROUP BY ls.userId " +
           "ORDER BY maxConsecutiveDays DESC")
    List<Object[]> findTopConsistentUsers(Pageable pageable);

    /**
     * 统计学习模式分布
     */
    @Query("SELECT " +
           "SUM(CASE WHEN ls.studyMinutes BETWEEN 0 AND 30 THEN 1 ELSE 0 END) as short, " +
           "SUM(CASE WHEN ls.studyMinutes BETWEEN 31 AND 60 THEN 1 ELSE 0 END) as medium, " +
           "SUM(CASE WHEN ls.studyMinutes BETWEEN 61 AND 120 THEN 1 ELSE 0 END) as long, " +
           "SUM(CASE WHEN ls.studyMinutes > 120 THEN 1 ELSE 0 END) as veryLong " +
           "FROM LearningStatistics ls WHERE ls.userId = :userId")
    Object[] getStudyPatternDistribution(@Param("userId") Long userId);

    /**
     * 查找需要更新的统计
     */
    @Query("SELECT ls FROM LearningStatistics ls WHERE ls.generatedAt < :updateThreshold")
    List<LearningStatistics> findStatisticsNeedingUpdate(@Param("updateThreshold") LocalDate updateThreshold);

    /**
     * 删除过期的统计数据
     */
    @Query("DELETE FROM LearningStatistics ls WHERE ls.statisticsDate < :cutoffDate " +
           "AND ls.statisticsPeriod = 'REAL_TIME'")
    int deleteExpiredRealTimeStatistics(@Param("cutoffDate") LocalDate cutoffDate);

}
