package com.chiron.smartlearnsystem.analytics.controller;

import com.chiron.smartlearnsystem.analytics.dto.LearningStatisticsDTO;
import com.chiron.smartlearnsystem.analytics.dto.request.StatisticsQueryRequest;
import com.chiron.smartlearnsystem.analytics.service.LearningStatisticsService;
import com.chiron.smartlearnsystem.common.core.domain.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.util.List;
import java.util.Map;

/**
 * 学习统计控制器
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/analytics/statistics")
@RequiredArgsConstructor
@Validated
public class LearningStatisticsController {

    private final LearningStatisticsService learningStatisticsService;

    /**
     * 获取用户学习统计
     */
    @PostMapping("/query")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<LearningStatisticsDTO>>> getUserStatistics(
            @Valid @RequestBody StatisticsQueryRequest request) {
        
        log.info("获取用户学习统计: {}", request);
        
        // 验证日期范围
        if (!request.isValidDateRange()) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.fail("结束日期不能早于开始日期"));
        }
        
        List<LearningStatisticsDTO> statistics = learningStatisticsService.getUserStatistics(request);
        
        return ResponseEntity.ok(ApiResponse.success("获取学习统计成功", statistics));
    }

    /**
     * 获取用户今日统计
     */
    @GetMapping("/user/{userId}/today")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<LearningStatisticsDTO>> getUserTodayStatistics(
            @PathVariable Long userId,
            @RequestParam(required = false) String subjectCode) {
        
        log.info("获取用户今日统计: userId={}, subject={}", userId, subjectCode);
        
        LearningStatisticsDTO statistics = learningStatisticsService.getUserTodayStatistics(userId, subjectCode);
        
        return ResponseEntity.ok(ApiResponse.success("获取今日统计成功", statistics));
    }

    /**
     * 获取用户本周统计
     */
    @GetMapping("/user/{userId}/weekly")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<LearningStatisticsDTO>> getUserWeeklyStatistics(
            @PathVariable Long userId,
            @RequestParam(required = false) String subjectCode) {
        
        log.info("获取用户本周统计: userId={}, subject={}", userId, subjectCode);
        
        LearningStatisticsDTO statistics = learningStatisticsService.getUserWeeklyStatistics(userId, subjectCode);
        
        return ResponseEntity.ok(ApiResponse.success("获取本周统计成功", statistics));
    }

    /**
     * 获取用户本月统计
     */
    @GetMapping("/user/{userId}/monthly")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<LearningStatisticsDTO>> getUserMonthlyStatistics(
            @PathVariable Long userId,
            @RequestParam(required = false) String subjectCode) {
        
        log.info("获取用户本月统计: userId={}, subject={}", userId, subjectCode);
        
        LearningStatisticsDTO statistics = learningStatisticsService.getUserMonthlyStatistics(userId, subjectCode);
        
        return ResponseEntity.ok(ApiResponse.success("获取本月统计成功", statistics));
    }

    /**
     * 更新用户实时统计
     */
    @PostMapping("/user/{userId}/update")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Void>> updateRealTimeStatistics(
            @PathVariable Long userId,
            @RequestParam String subjectCode,
            @RequestParam Integer studyMinutes,
            @RequestParam Integer questionsCount,
            @RequestParam Integer correctCount) {
        
        log.debug("更新用户实时统计: userId={}, subject={}, minutes={}, questions={}, correct={}", 
                userId, subjectCode, studyMinutes, questionsCount, correctCount);
        
        learningStatisticsService.updateRealTimeStatistics(userId, subjectCode, 
                studyMinutes, questionsCount, correctCount);
        
        return ResponseEntity.ok(ApiResponse.<Void>success("统计更新成功", null));
    }

    /**
     * 计算学习趋势
     */
    @GetMapping("/user/{userId}/trend")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> calculateLearningTrend(
            @PathVariable Long userId,
            @RequestParam(required = false) String subjectCode,
            @RequestParam(defaultValue = "30") @Min(7) @Max(365) Integer days) {
        
        log.info("计算学习趋势: userId={}, subject={}, days={}", userId, subjectCode, days);
        
        Map<String, Object> trend = learningStatisticsService.calculateLearningTrend(userId, subjectCode, days);
        
        return ResponseEntity.ok(ApiResponse.success("学习趋势计算成功", trend));
    }

    /**
     * 获取学习排名
     */
    @GetMapping("/user/{userId}/ranking")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getUserRanking(
            @PathVariable Long userId,
            @RequestParam(required = false) String subjectCode,
            @RequestParam(defaultValue = "WEEKLY") String period) {
        
        log.info("获取用户排名: userId={}, subject={}, period={}", userId, subjectCode, period);
        
        // TODO: 解析period参数
        Map<String, Object> ranking = learningStatisticsService.getUserRanking(userId, subjectCode, null);
        
        return ResponseEntity.ok(ApiResponse.success("获取排名成功", ranking));
    }

    /**
     * 生成统计摘要
     */
    @GetMapping("/user/{userId}/summary")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Map<String, Object>>> generateStatisticsSummary(
            @PathVariable Long userId,
            @RequestParam(required = false) String subjectCode,
            @RequestParam String startDate,
            @RequestParam String endDate) {
        
        log.info("生成统计摘要: userId={}, subject={}, period={}-{}", 
                userId, subjectCode, startDate, endDate);
        
        try {
            java.time.LocalDate start = java.time.LocalDate.parse(startDate);
            java.time.LocalDate end = java.time.LocalDate.parse(endDate);
            
            if (end.isBefore(start)) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.fail("结束日期不能早于开始日期"));
            }
            
            Map<String, Object> summary = learningStatisticsService.generateStatisticsSummary(
                    userId, subjectCode, start, end);
            
            return ResponseEntity.ok(ApiResponse.success("统计摘要生成成功", summary));
            
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.fail("日期格式错误，请使用yyyy-MM-dd格式"));
        }
    }

}
