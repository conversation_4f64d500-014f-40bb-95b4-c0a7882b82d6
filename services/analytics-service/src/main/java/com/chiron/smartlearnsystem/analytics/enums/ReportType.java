package com.chiron.smartlearnsystem.analytics.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 报告类型枚举
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum ReportType {

    /**
     * 日报
     */
    DAILY_REPORT("DAILY_REPORT", "日报", "每日学习情况报告"),

    /**
     * 周报
     */
    WEEKLY_REPORT("WEEKLY_REPORT", "周报", "每周学习情况报告"),

    /**
     * 月报
     */
    MONTHLY_REPORT("MONTHLY_REPORT", "月报", "每月学习情况报告"),

    /**
     * 季报
     */
    QUARTERLY_REPORT("QUARTERLY_REPORT", "季报", "每季度学习情况报告"),

    /**
     * 年报
     */
    ANNUAL_REPORT("ANNUAL_REPORT", "年报", "年度学习情况报告"),

    /**
     * 学习计划报告
     */
    STUDY_PLAN_REPORT("STUDY_PLAN_REPORT", "学习计划报告", "特定学习计划的完成情况报告"),

    /**
     * 能力评估报告
     */
    ABILITY_ASSESSMENT_REPORT("ABILITY_ASSESSMENT_REPORT", "能力评估报告", "用户能力评估详细报告"),

    /**
     * 进步分析报告
     */
    PROGRESS_ANALYSIS_REPORT("PROGRESS_ANALYSIS_REPORT", "进步分析报告", "学习进步趋势分析报告"),

    /**
     * 薄弱点分析报告
     */
    WEAKNESS_ANALYSIS_REPORT("WEAKNESS_ANALYSIS_REPORT", "薄弱点分析报告", "学习薄弱环节分析报告"),

    /**
     * 知识点掌握报告
     */
    KNOWLEDGE_MASTERY_REPORT("KNOWLEDGE_MASTERY_REPORT", "知识点掌握报告", "各知识点掌握情况报告"),

    /**
     * 学习效率报告
     */
    EFFICIENCY_REPORT("EFFICIENCY_REPORT", "学习效率报告", "学习效率分析报告"),

    /**
     * 错题分析报告
     */
    ERROR_ANALYSIS_REPORT("ERROR_ANALYSIS_REPORT", "错题分析报告", "错题模式和原因分析报告"),

    /**
     * 学习习惯报告
     */
    STUDY_HABIT_REPORT("STUDY_HABIT_REPORT", "学习习惯报告", "学习习惯和时间分布报告"),

    /**
     * 考试准备报告
     */
    EXAM_PREPARATION_REPORT("EXAM_PREPARATION_REPORT", "考试准备报告", "考试准备情况评估报告"),

    /**
     * 自定义报告
     */
    CUSTOM_REPORT("CUSTOM_REPORT", "自定义报告", "用户自定义的报告");

    private final String code;
    private final String name;
    private final String description;

    /**
     * 根据代码获取报告类型
     */
    public static ReportType getByCode(String code) {
        for (ReportType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return WEEKLY_REPORT; // 默认返回周报
    }

    /**
     * 是否为定期报告
     */
    public boolean isPeriodicReport() {
        return this == DAILY_REPORT || this == WEEKLY_REPORT || this == MONTHLY_REPORT || 
               this == QUARTERLY_REPORT || this == ANNUAL_REPORT;
    }

    /**
     * 是否为分析报告
     */
    public boolean isAnalysisReport() {
        return this == PROGRESS_ANALYSIS_REPORT || this == WEAKNESS_ANALYSIS_REPORT || 
               this == ERROR_ANALYSIS_REPORT || this == EFFICIENCY_REPORT;
    }

    /**
     * 是否为专项报告
     */
    public boolean isSpecializedReport() {
        return this == STUDY_PLAN_REPORT || this == ABILITY_ASSESSMENT_REPORT || 
               this == KNOWLEDGE_MASTERY_REPORT || this == EXAM_PREPARATION_REPORT;
    }

    /**
     * 获取默认报告周期（天）
     */
    public int getDefaultPeriodDays() {
        switch (this) {
            case DAILY_REPORT:
                return 1;
            case WEEKLY_REPORT:
                return 7;
            case MONTHLY_REPORT:
                return 30;
            case QUARTERLY_REPORT:
                return 90;
            case ANNUAL_REPORT:
                return 365;
            case STUDY_PLAN_REPORT:
            case ABILITY_ASSESSMENT_REPORT:
            case PROGRESS_ANALYSIS_REPORT:
            case WEAKNESS_ANALYSIS_REPORT:
            case KNOWLEDGE_MASTERY_REPORT:
            case EFFICIENCY_REPORT:
            case ERROR_ANALYSIS_REPORT:
            case STUDY_HABIT_REPORT:
            case EXAM_PREPARATION_REPORT:
                return 30; // 默认30天
            case CUSTOM_REPORT:
                return 7; // 默认7天
            default:
                return 7;
        }
    }

    /**
     * 获取生成优先级（1-10，10最高）
     */
    public int getGenerationPriority() {
        switch (this) {
            case EXAM_PREPARATION_REPORT:
                return 10; // 考试准备报告最高优先级
            case ABILITY_ASSESSMENT_REPORT:
                return 9; // 能力评估报告很高优先级
            case WEAKNESS_ANALYSIS_REPORT:
                return 8; // 薄弱点分析高优先级
            case PROGRESS_ANALYSIS_REPORT:
                return 7; // 进步分析较高优先级
            case WEEKLY_REPORT:
                return 6; // 周报中等优先级
            case MONTHLY_REPORT:
                return 6; // 月报中等优先级
            case STUDY_PLAN_REPORT:
                return 5; // 学习计划报告中等优先级
            case ERROR_ANALYSIS_REPORT:
                return 5; // 错题分析中等优先级
            case EFFICIENCY_REPORT:
                return 4; // 效率报告较低优先级
            case KNOWLEDGE_MASTERY_REPORT:
                return 4; // 知识点掌握较低优先级
            case DAILY_REPORT:
                return 3; // 日报较低优先级
            case STUDY_HABIT_REPORT:
                return 3; // 学习习惯较低优先级
            case QUARTERLY_REPORT:
                return 2; // 季报低优先级
            case ANNUAL_REPORT:
                return 2; // 年报低优先级
            case CUSTOM_REPORT:
                return 1; // 自定义报告最低优先级
            default:
                return 5;
        }
    }

    /**
     * 获取预估生成时间（秒）
     */
    public int getEstimatedGenerationTime() {
        switch (this) {
            case DAILY_REPORT:
                return 30; // 30秒
            case WEEKLY_REPORT:
                return 60; // 1分钟
            case MONTHLY_REPORT:
                return 180; // 3分钟
            case QUARTERLY_REPORT:
                return 300; // 5分钟
            case ANNUAL_REPORT:
                return 600; // 10分钟
            case STUDY_PLAN_REPORT:
                return 90; // 1.5分钟
            case ABILITY_ASSESSMENT_REPORT:
                return 120; // 2分钟
            case PROGRESS_ANALYSIS_REPORT:
                return 150; // 2.5分钟
            case WEAKNESS_ANALYSIS_REPORT:
                return 120; // 2分钟
            case KNOWLEDGE_MASTERY_REPORT:
                return 90; // 1.5分钟
            case EFFICIENCY_REPORT:
                return 90; // 1.5分钟
            case ERROR_ANALYSIS_REPORT:
                return 120; // 2分钟
            case STUDY_HABIT_REPORT:
                return 60; // 1分钟
            case EXAM_PREPARATION_REPORT:
                return 180; // 3分钟
            case CUSTOM_REPORT:
                return 120; // 2分钟
            default:
                return 120;
        }
    }

    /**
     * 是否支持自动生成
     */
    public boolean supportsAutoGeneration() {
        return isPeriodicReport() || this == STUDY_PLAN_REPORT || this == ABILITY_ASSESSMENT_REPORT;
    }

}
