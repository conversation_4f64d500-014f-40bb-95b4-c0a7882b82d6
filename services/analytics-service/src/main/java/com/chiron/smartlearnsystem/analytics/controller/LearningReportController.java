package com.chiron.smartlearnsystem.analytics.controller;

import com.chiron.smartlearnsystem.analytics.dto.LearningReportDTO;
import com.chiron.smartlearnsystem.analytics.dto.request.ReportGenerationRequest;
import com.chiron.smartlearnsystem.analytics.enums.ReportType;
import com.chiron.smartlearnsystem.analytics.service.LearningReportService;
import com.chiron.smartlearnsystem.common.core.domain.ApiResponse;
import com.chiron.smartlearnsystem.common.core.domain.PageResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.util.List;

/**
 * 学习报告控制器
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/analytics/reports")
@RequiredArgsConstructor
@Validated
public class LearningReportController {

    private final LearningReportService learningReportService;

    /**
     * 生成学习报告
     */
    @PostMapping("/generate")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<LearningReportDTO>> generateReport(
            @Valid @RequestBody ReportGenerationRequest request) {
        
        log.info("生成学习报告请求: {}", request);
        
        // 验证日期范围
        if (!request.isValidDateRange()) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.fail("结束日期不能早于开始日期"));
        }
        
        LearningReportDTO report = learningReportService.generateReport(request);
        
        return ResponseEntity.ok(ApiResponse.success("报告生成任务已启动", report));
    }

    /**
     * 获取用户报告列表
     */
    @GetMapping("/user/{userId}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<PageResult<LearningReportDTO>>> getUserReports(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "1") @Min(1) Integer page,
            @RequestParam(defaultValue = "20") @Min(1) @Max(100) Integer size) {
        
        log.info("获取用户报告列表: userId={}", userId);
        
        PageResult<LearningReportDTO> result = learningReportService.getUserReports(userId, page, size);
        
        return ResponseEntity.ok(ApiResponse.success("获取报告列表成功", result));
    }

    /**
     * 获取报告详情
     */
    @GetMapping("/{reportId}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<LearningReportDTO>> getReportById(@PathVariable Long reportId) {
        
        log.info("获取报告详情: reportId={}", reportId);
        
        LearningReportDTO report = learningReportService.getReportById(reportId);
        
        return ResponseEntity.ok(ApiResponse.success("获取报告详情成功", report));
    }

    /**
     * 标记报告为已查看
     */
    @PostMapping("/{reportId}/view")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Void>> markReportAsViewed(
            @PathVariable Long reportId,
            @RequestParam Long userId) {
        
        log.debug("标记报告已查看: reportId={}, userId={}", reportId, userId);
        
        learningReportService.markReportAsViewed(reportId, userId);
        
        return ResponseEntity.ok(ApiResponse.<Void>success("已标记为查看", null));
    }

    /**
     * 重新生成报告
     */
    @PostMapping("/{reportId}/regenerate")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<LearningReportDTO>> regenerateReport(
            @PathVariable Long reportId,
            @RequestParam Long userId) {
        
        log.info("重新生成报告: reportId={}, userId={}", reportId, userId);
        
        LearningReportDTO report = learningReportService.regenerateReport(reportId, userId);
        
        return ResponseEntity.ok(ApiResponse.success("报告重新生成任务已启动", report));
    }

    /**
     * 删除报告
     */
    @DeleteMapping("/{reportId}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Void>> deleteReport(
            @PathVariable Long reportId,
            @RequestParam Long userId) {
        
        log.info("删除报告: reportId={}, userId={}", reportId, userId);
        
        learningReportService.deleteReport(reportId, userId);
        
        return ResponseEntity.ok(ApiResponse.<Void>success("报告删除成功", null));
    }

    /**
     * 获取用户最新报告
     */
    @GetMapping("/user/{userId}/latest")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<LearningReportDTO>>> getUserLatestReports(
            @PathVariable Long userId,
            @RequestParam(required = false) ReportType reportType,
            @RequestParam(defaultValue = "5") @Min(1) @Max(20) Integer limit) {
        
        log.info("获取用户最新报告: userId={}, type={}, limit={}", userId, reportType, limit);
        
        List<LearningReportDTO> reports = learningReportService.getUserLatestReports(userId, reportType, limit);
        
        return ResponseEntity.ok(ApiResponse.success("获取最新报告成功", reports));
    }

}
