package com.chiron.smartlearnsystem.analytics.dto;

import com.chiron.smartlearnsystem.analytics.entity.LearningReport;
import com.chiron.smartlearnsystem.analytics.enums.ReportStatus;
import com.chiron.smartlearnsystem.analytics.enums.ReportType;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Set;

/**
 * 学习报告DTO
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LearningReportDTO {

    /**
     * 报告ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 报告类型
     */
    private ReportType reportType;

    /**
     * 报告状态
     */
    private ReportStatus status;

    /**
     * 报告标题
     */
    private String title;

    /**
     * 报告描述
     */
    private String description;

    /**
     * 报告开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate reportStartDate;

    /**
     * 报告结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate reportEndDate;

    /**
     * 报告生成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime generatedAt;

    /**
     * 科目代码
     */
    private String subjectCode;

    /**
     * 报告内容
     */
    private String reportContent;

    /**
     * 报告摘要
     */
    private String reportSummary;

    /**
     * 学习统计数据
     */
    private String learningStatistics;

    /**
     * 进步分析
     */
    private String progressAnalysis;

    /**
     * 薄弱点分析
     */
    private String weaknessAnalysis;

    /**
     * 学习建议
     */
    private String learningSuggestions;

    /**
     * 总学习时长（分钟）
     */
    private Integer totalStudyMinutes;

    /**
     * 总答题数量
     */
    private Integer totalQuestions;

    /**
     * 正确答题数量
     */
    private Integer correctQuestions;

    /**
     * 平均正确率
     */
    private BigDecimal averageAccuracy;

    /**
     * 学习天数
     */
    private Integer studyDays;

    /**
     * 连续学习天数
     */
    private Integer consecutiveDays;

    /**
     * 完成的学习计划数
     */
    private Integer completedPlans;

    /**
     * 能力提升幅度
     */
    private BigDecimal abilityImprovement;

    /**
     * 学习效率评分
     */
    private BigDecimal efficiencyScore;

    /**
     * 专注度评分
     */
    private BigDecimal focusScore;

    /**
     * 坚持度评分
     */
    private BigDecimal persistenceScore;

    /**
     * 报告文件路径
     */
    private String reportFilePath;

    /**
     * 报告文件格式
     */
    private String reportFormat;

    /**
     * 报告文件大小
     */
    private Long reportFileSize;

    /**
     * 是否已查看
     */
    private Boolean isViewed;

    /**
     * 查看时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime viewedAt;

    /**
     * 查看次数
     */
    private Integer viewCount;

    /**
     * 是否已分享
     */
    private Boolean isShared;

    /**
     * 分享链接
     */
    private String shareLink;

    /**
     * 报告标签
     */
    private Set<String> tags;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 生成耗时（秒）
     */
    private Integer generationDuration;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 从LearningReport实体转换为DTO
     */
    public static LearningReportDTO from(LearningReport report) {
        if (report == null) {
            return null;
        }

        return LearningReportDTO.builder()
                .id(report.getId())
                .userId(report.getUserId())
                .reportType(report.getReportType())
                .status(report.getStatus())
                .title(report.getTitle())
                .description(report.getDescription())
                .reportStartDate(report.getReportStartDate())
                .reportEndDate(report.getReportEndDate())
                .generatedAt(report.getGeneratedAt())
                .subjectCode(report.getSubjectCode())
                .reportContent(report.getReportContent())
                .reportSummary(report.getReportSummary())
                .learningStatistics(report.getLearningStatistics())
                .progressAnalysis(report.getProgressAnalysis())
                .weaknessAnalysis(report.getWeaknessAnalysis())
                .learningSuggestions(report.getLearningSuggestions())
                .totalStudyMinutes(report.getTotalStudyMinutes())
                .totalQuestions(report.getTotalQuestions())
                .correctQuestions(report.getCorrectQuestions())
                .averageAccuracy(report.getAverageAccuracy())
                .studyDays(report.getStudyDays())
                .consecutiveDays(report.getConsecutiveDays())
                .completedPlans(report.getCompletedPlans())
                .abilityImprovement(report.getAbilityImprovement())
                .efficiencyScore(report.getEfficiencyScore())
                .focusScore(report.getFocusScore())
                .persistenceScore(report.getPersistenceScore())
                .reportFilePath(report.getReportFilePath())
                .reportFormat(report.getReportFormat())
                .reportFileSize(report.getReportFileSize())
                .isViewed(report.getIsViewed())
                .viewedAt(report.getViewedAt())
                .viewCount(report.getViewCount())
                .isShared(report.getIsShared())
                .shareLink(report.getShareLink())
                .tags(report.getTags())
                .errorMessage(report.getErrorMessage())
                .generationDuration(report.getGenerationDuration())
                .createdAt(report.getCreatedAt())
                .updatedAt(report.getUpdatedAt())
                .build();
    }

    /**
     * 获取报告类型描述
     */
    public String getReportTypeDescription() {
        return reportType != null ? reportType.getDescription() : "";
    }

    /**
     * 获取状态描述
     */
    public String getStatusDescription() {
        return status != null ? status.getDescription() : "";
    }

    /**
     * 获取正确率百分比
     */
    public String getAccuracyPercentage() {
        if (averageAccuracy == null) {
            return "0%";
        }
        return averageAccuracy.multiply(BigDecimal.valueOf(100))
                .setScale(1, BigDecimal.ROUND_HALF_UP) + "%";
    }

    /**
     * 获取报告周期天数
     */
    public long getReportPeriodDays() {
        if (reportStartDate == null || reportEndDate == null) {
            return 0;
        }
        return java.time.temporal.ChronoUnit.DAYS.between(reportStartDate, reportEndDate) + 1;
    }

    /**
     * 检查是否已完成
     */
    public boolean isCompleted() {
        return status != null && status.isCompleted();
    }

    /**
     * 检查是否生成失败
     */
    public boolean isFailed() {
        return status != null && status.isFailed();
    }

    /**
     * 检查是否正在生成
     */
    public boolean isGenerating() {
        return status != null && status.isInProgress();
    }

    /**
     * 检查是否可以查看
     */
    public boolean canView() {
        return status != null && status.canView();
    }

    /**
     * 检查是否可以重新生成
     */
    public boolean canRegenerate() {
        return status != null && status.canRegenerate();
    }

    /**
     * 检查是否可以分享
     */
    public boolean canShare() {
        return status != null && status.canShare();
    }

}
