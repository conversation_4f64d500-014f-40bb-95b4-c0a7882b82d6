package com.chiron.smartlearnsystem.analytics.repository;

import com.chiron.smartlearnsystem.analytics.entity.LearningReport;
import com.chiron.smartlearnsystem.analytics.enums.ReportStatus;
import com.chiron.smartlearnsystem.analytics.enums.ReportType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 学习报告数据访问层
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Repository
public interface LearningReportRepository extends JpaRepository<LearningReport, Long>, 
                                                JpaSpecificationExecutor<LearningReport> {

    /**
     * 根据用户ID查找报告
     */
    Page<LearningReport> findByUserId(Long userId, Pageable pageable);

    /**
     * 根据用户ID和报告类型查找报告
     */
    List<LearningReport> findByUserIdAndReportType(Long userId, ReportType reportType);

    /**
     * 根据用户ID和状态查找报告
     */
    List<LearningReport> findByUserIdAndStatus(Long userId, ReportStatus status, Pageable pageable);

    /**
     * 根据用户ID、报告类型和状态查找报告
     */
    List<LearningReport> findByUserIdAndReportTypeAndStatus(Long userId, ReportType reportType, 
                                                           ReportStatus status, Pageable pageable);

    /**
     * 根据用户ID、报告类型和报告周期查找报告
     */
    @Query("SELECT lr FROM LearningReport lr WHERE lr.userId = :userId AND lr.reportType = :reportType " +
           "AND lr.reportStartDate = :startDate AND lr.reportEndDate = :endDate")
    Optional<LearningReport> findByUserIdAndReportTypeAndReportPeriod(@Param("userId") Long userId,
                                                                     @Param("reportType") ReportType reportType,
                                                                     @Param("startDate") LocalDate startDate,
                                                                     @Param("endDate") LocalDate endDate);

    /**
     * 查找用户最新的报告
     */
    @Query("SELECT lr FROM LearningReport lr WHERE lr.userId = :userId AND lr.status = 'COMPLETED' " +
           "ORDER BY lr.generatedAt DESC")
    List<LearningReport> findLatestReportsByUser(@Param("userId") Long userId, Pageable pageable);

    /**
     * 查找指定时间范围内的报告
     */
    @Query("SELECT lr FROM LearningReport lr WHERE lr.userId = :userId " +
           "AND lr.reportStartDate >= :startDate AND lr.reportEndDate <= :endDate")
    List<LearningReport> findReportsByDateRange(@Param("userId") Long userId,
                                              @Param("startDate") LocalDate startDate,
                                              @Param("endDate") LocalDate endDate);

    /**
     * 查找正在生成的报告
     */
    @Query("SELECT lr FROM LearningReport lr WHERE lr.status IN ('GENERATING', 'QUEUED')")
    List<LearningReport> findGeneratingReports();

    /**
     * 查找生成失败的报告
     */
    List<LearningReport> findByStatus(ReportStatus status);

    /**
     * 查找过期的报告
     */
    @Query("SELECT lr FROM LearningReport lr WHERE lr.generatedAt < :cutoffTime AND lr.status = 'COMPLETED'")
    List<LearningReport> findExpiredReports(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 统计用户各类型报告数量
     */
    @Query("SELECT lr.reportType, COUNT(lr) FROM LearningReport lr " +
           "WHERE lr.userId = :userId GROUP BY lr.reportType")
    List<Object[]> countReportsByType(@Param("userId") Long userId);

    /**
     * 统计用户各状态报告数量
     */
    @Query("SELECT lr.status, COUNT(lr) FROM LearningReport lr " +
           "WHERE lr.userId = :userId GROUP BY lr.status")
    List<Object[]> countReportsByStatus(@Param("userId") Long userId);

    /**
     * 获取用户报告统计
     */
    @Query("SELECT " +
           "COUNT(lr) as totalCount, " +
           "SUM(CASE WHEN lr.status = 'COMPLETED' THEN 1 ELSE 0 END) as completedCount, " +
           "SUM(CASE WHEN lr.status = 'FAILED' THEN 1 ELSE 0 END) as failedCount, " +
           "SUM(CASE WHEN lr.isViewed = true THEN 1 ELSE 0 END) as viewedCount, " +
           "AVG(lr.generationDuration) as avgGenerationTime " +
           "FROM LearningReport lr WHERE lr.userId = :userId")
    Object[] getUserReportStatistics(@Param("userId") Long userId);

    /**
     * 查找科目相关的报告
     */
    List<LearningReport> findByUserIdAndSubjectCode(Long userId, String subjectCode);

    /**
     * 查找已分享的报告
     */
    @Query("SELECT lr FROM LearningReport lr WHERE lr.userId = :userId AND lr.isShared = true")
    List<LearningReport> findSharedReports(@Param("userId") Long userId);

    /**
     * 查找未查看的报告
     */
    @Query("SELECT lr FROM LearningReport lr WHERE lr.userId = :userId AND lr.isViewed = false " +
           "AND lr.status = 'COMPLETED' ORDER BY lr.generatedAt DESC")
    List<LearningReport> findUnviewedReports(@Param("userId") Long userId);

    /**
     * 批量更新报告状态
     */
    @Modifying
    @Query("UPDATE LearningReport lr SET lr.status = :status WHERE lr.id IN :reportIds")
    int batchUpdateStatus(@Param("reportIds") List<Long> reportIds, @Param("status") ReportStatus status);

    /**
     * 删除过期的报告
     */
    @Modifying
    @Query("DELETE FROM LearningReport lr WHERE lr.generatedAt < :cutoffTime AND lr.status = 'COMPLETED'")
    int deleteExpiredReports(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 查找生成时间超过阈值的报告
     */
    @Query("SELECT lr FROM LearningReport lr WHERE lr.status = 'GENERATING' " +
           "AND lr.createdAt < :timeoutThreshold")
    List<LearningReport> findTimeoutReports(@Param("timeoutThreshold") LocalDateTime timeoutThreshold);

    /**
     * 查找特定日期生成的报告
     */
    @Query("SELECT lr FROM LearningReport lr WHERE DATE(lr.generatedAt) = :date")
    List<LearningReport> findReportsByGeneratedDate(@Param("date") LocalDate date);

    /**
     * 查找大文件报告
     */
    @Query("SELECT lr FROM LearningReport lr WHERE lr.reportFileSize > :sizeThreshold")
    List<LearningReport> findLargeReports(@Param("sizeThreshold") Long sizeThreshold);

    /**
     * 统计报告生成性能
     */
    @Query("SELECT " +
           "lr.reportType, " +
           "AVG(lr.generationDuration) as avgDuration, " +
           "MIN(lr.generationDuration) as minDuration, " +
           "MAX(lr.generationDuration) as maxDuration, " +
           "COUNT(lr) as reportCount " +
           "FROM LearningReport lr WHERE lr.status = 'COMPLETED' " +
           "GROUP BY lr.reportType")
    List<Object[]> getReportGenerationPerformance();

    /**
     * 查找最近生成的报告
     */
    @Query("SELECT lr FROM LearningReport lr WHERE lr.generatedAt >= :since " +
           "ORDER BY lr.generatedAt DESC")
    List<LearningReport> findRecentReports(@Param("since") LocalDateTime since, Pageable pageable);

}
