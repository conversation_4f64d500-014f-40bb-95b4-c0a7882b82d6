package com.chiron.smartlearnsystem.analytics.entity;

import com.chiron.smartlearnsystem.analytics.enums.StatisticsPeriod;
import com.chiron.smartlearnsystem.analytics.enums.StatisticsType;
import com.chiron.smartlearnsystem.common.database.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 学习统计实体
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Entity
@Table(name = "learning_statistics", indexes = {
    @Index(name = "idx_user_period", columnList = "user_id, statistics_period, statistics_date"),
    @Index(name = "idx_type", columnList = "statistics_type"),
    @Index(name = "idx_subject", columnList = "subject_code"),
    @Index(name = "idx_date", columnList = "statistics_date"),
    @Index(name = "idx_created_at", columnList = "created_at")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LearningStatistics extends BaseEntity {

    /**
     * 用户ID
     */
    @Column(name = "user_id", nullable = false)
    private Long userId;

    /**
     * 统计类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "statistics_type", nullable = false, length = 30)
    private StatisticsType statisticsType;

    /**
     * 统计周期
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "statistics_period", nullable = false, length = 20)
    private StatisticsPeriod statisticsPeriod;

    /**
     * 统计日期
     */
    @Column(name = "statistics_date", nullable = false)
    private LocalDate statisticsDate;

    /**
     * 科目代码
     */
    @Column(name = "subject_code", length = 20)
    private String subjectCode;

    /**
     * 学习时长（分钟）
     */
    @Column(name = "study_minutes", nullable = false)
    private Integer studyMinutes = 0;

    /**
     * 有效学习时长（分钟）
     */
    @Column(name = "effective_minutes", nullable = false)
    private Integer effectiveMinutes = 0;

    /**
     * 答题数量
     */
    @Column(name = "questions_count", nullable = false)
    private Integer questionsCount = 0;

    /**
     * 正确答题数量
     */
    @Column(name = "correct_count", nullable = false)
    private Integer correctCount = 0;

    /**
     * 正确率
     */
    @Column(name = "accuracy_rate", precision = 5, scale = 4)
    private BigDecimal accuracyRate;

    /**
     * 平均答题时间（秒）
     */
    @Column(name = "avg_answer_time", precision = 6, scale = 2)
    private BigDecimal avgAnswerTime;

    /**
     * 学习会话数量
     */
    @Column(name = "session_count", nullable = false)
    private Integer sessionCount = 0;

    /**
     * 完成的学习计划数
     */
    @Column(name = "completed_plans", nullable = false)
    private Integer completedPlans = 0;

    /**
     * 学习天数
     */
    @Column(name = "study_days", nullable = false)
    private Integer studyDays = 0;

    /**
     * 连续学习天数
     */
    @Column(name = "consecutive_days", nullable = false)
    private Integer consecutiveDays = 0;

    /**
     * 专注度评分
     */
    @Column(name = "focus_score", precision = 3, scale = 1)
    private BigDecimal focusScore;

    /**
     * 学习效率评分
     */
    @Column(name = "efficiency_score", precision = 3, scale = 1)
    private BigDecimal efficiencyScore;

    /**
     * 能力提升幅度
     */
    @Column(name = "ability_improvement", precision = 5, scale = 2)
    private BigDecimal abilityImprovement;

    /**
     * 知识点掌握数量
     */
    @Column(name = "mastered_knowledge_points", nullable = false)
    private Integer masteredKnowledgePoints = 0;

    /**
     * 薄弱知识点数量
     */
    @Column(name = "weak_knowledge_points", nullable = false)
    private Integer weakKnowledgePoints = 0;

    /**
     * 错误次数
     */
    @Column(name = "error_count", nullable = false)
    private Integer errorCount = 0;

    /**
     * 中断次数
     */
    @Column(name = "interruption_count", nullable = false)
    private Integer interruptionCount = 0;

    /**
     * 暂停时长（分钟）
     */
    @Column(name = "pause_minutes", nullable = false)
    private Integer pauseMinutes = 0;

    /**
     * 学习强度
     */
    @Column(name = "study_intensity", precision = 3, scale = 1)
    private BigDecimal studyIntensity;

    /**
     * 详细统计数据（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "detailed_statistics", columnDefinition = "JSON")
    private String detailedStatistics;

    /**
     * 知识点统计（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "knowledge_point_stats", columnDefinition = "JSON")
    private String knowledgePointStats;

    /**
     * 题型统计（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "question_type_stats", columnDefinition = "JSON")
    private String questionTypeStats;

    /**
     * 难度统计（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "difficulty_stats", columnDefinition = "JSON")
    private String difficultyStats;

    /**
     * 时间分布统计（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "time_distribution", columnDefinition = "JSON")
    private String timeDistribution;

    /**
     * 学习模式统计（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "study_pattern_stats", columnDefinition = "JSON")
    private String studyPatternStats;

    /**
     * 排名信息（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "ranking_info", columnDefinition = "JSON")
    private String rankingInfo;

    /**
     * 对比数据（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "comparison_data", columnDefinition = "JSON")
    private String comparisonData;

    /**
     * 统计生成时间
     */
    @Column(name = "generated_at", nullable = false)
    private LocalDateTime generatedAt = LocalDateTime.now();

    /**
     * 数据来源
     */
    @Column(name = "data_source", length = 50)
    private String dataSource;

    /**
     * 计算正确率
     */
    public void calculateAccuracyRate() {
        if (questionsCount > 0) {
            this.accuracyRate = BigDecimal.valueOf(correctCount)
                    .divide(BigDecimal.valueOf(questionsCount), 4, BigDecimal.ROUND_HALF_UP);
        }
    }

    /**
     * 计算学习效率
     */
    public void calculateEfficiency() {
        if (studyMinutes > 0 && questionsCount > 0) {
            // 简单的效率计算：题目数/学习时长
            double efficiency = (double) questionsCount / studyMinutes * 60; // 每小时完成题目数
            this.efficiencyScore = BigDecimal.valueOf(Math.min(10.0, efficiency))
                    .setScale(1, BigDecimal.ROUND_HALF_UP);
        }
    }

    /**
     * 计算学习强度
     */
    public void calculateStudyIntensity() {
        if (studyDays > 0) {
            // 基于学习时长和天数计算强度
            double avgMinutesPerDay = (double) studyMinutes / studyDays;
            double intensity = avgMinutesPerDay / 60.0; // 转换为小时
            this.studyIntensity = BigDecimal.valueOf(Math.min(10.0, intensity))
                    .setScale(1, BigDecimal.ROUND_HALF_UP);
        }
    }

    /**
     * 更新统计数据
     */
    public void updateStatistics() {
        calculateAccuracyRate();
        calculateEfficiency();
        calculateStudyIntensity();
        this.generatedAt = LocalDateTime.now();
    }

    /**
     * 检查是否为今日统计
     */
    public boolean isToday() {
        return LocalDate.now().equals(this.statisticsDate);
    }

    /**
     * 检查是否为本周统计
     */
    public boolean isThisWeek() {
        LocalDate now = LocalDate.now();
        LocalDate weekStart = now.minusDays(now.getDayOfWeek().getValue() - 1);
        return !this.statisticsDate.isBefore(weekStart) && !this.statisticsDate.isAfter(now);
    }

    /**
     * 检查是否为本月统计
     */
    public boolean isThisMonth() {
        LocalDate now = LocalDate.now();
        return this.statisticsDate.getYear() == now.getYear() && 
               this.statisticsDate.getMonth() == now.getMonth();
    }

    /**
     * 获取学习效率等级
     */
    public String getEfficiencyLevel() {
        if (efficiencyScore == null) {
            return "未知";
        }
        
        double score = efficiencyScore.doubleValue();
        if (score >= 8.0) {
            return "优秀";
        } else if (score >= 6.0) {
            return "良好";
        } else if (score >= 4.0) {
            return "一般";
        } else {
            return "待提高";
        }
    }

}
