package com.chiron.smartlearnsystem.analytics.dto.request;

import com.chiron.smartlearnsystem.analytics.entity.LearningReport;
import com.chiron.smartlearnsystem.analytics.enums.ReportType;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.LocalDate;
import java.util.Map;

/**
 * 报告生成请求
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReportGenerationRequest {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 报告类型
     */
    @NotNull(message = "报告类型不能为空")
    private ReportType reportType;

    /**
     * 开始日期
     */
    @NotNull(message = "开始日期不能为空")
    private LocalDate startDate;

    /**
     * 结束日期
     */
    @NotNull(message = "结束日期不能为空")
    private LocalDate endDate;

    /**
     * 科目代码
     */
    @Size(max = 20, message = "科目代码长度不能超过20个字符")
    private String subjectCode;

    /**
     * 报告格式
     */
    private String reportFormat = "JSON";

    /**
     * 是否包含详细数据
     */
    private Boolean includeDetailedData = true;

    /**
     * 是否包含图表
     */
    private Boolean includeCharts = true;

    /**
     * 是否包含建议
     */
    private Boolean includeSuggestions = true;

    /**
     * 自定义参数
     */
    private Map<String, Object> customParams;

    /**
     * 生成优先级
     */
    private Integer priority = 5;

    /**
     * 从LearningReport实体创建请求
     */
    public static ReportGenerationRequest fromReport(LearningReport report) {
        return ReportGenerationRequest.builder()
                .userId(report.getUserId())
                .reportType(report.getReportType())
                .startDate(report.getReportStartDate())
                .endDate(report.getReportEndDate())
                .subjectCode(report.getSubjectCode())
                .build();
    }

    /**
     * 转换为JSON字符串
     */
    public String toJsonString() {
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.writeValueAsString(this);
        } catch (JsonProcessingException e) {
            return "{}";
        }
    }

    /**
     * 验证日期范围
     */
    public boolean isValidDateRange() {
        return startDate != null && endDate != null && !endDate.isBefore(startDate);
    }

    /**
     * 获取报告周期天数
     */
    public long getReportPeriodDays() {
        if (startDate == null || endDate == null) {
            return 0;
        }
        return java.time.temporal.ChronoUnit.DAYS.between(startDate, endDate) + 1;
    }

}
