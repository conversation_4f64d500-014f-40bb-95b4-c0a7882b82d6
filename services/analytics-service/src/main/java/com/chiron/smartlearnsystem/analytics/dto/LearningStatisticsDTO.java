package com.chiron.smartlearnsystem.analytics.dto;

import com.chiron.smartlearnsystem.analytics.entity.LearningStatistics;
import com.chiron.smartlearnsystem.analytics.enums.StatisticsPeriod;
import com.chiron.smartlearnsystem.analytics.enums.StatisticsType;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 学习统计DTO
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LearningStatisticsDTO {

    /**
     * 统计ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 统计类型
     */
    private StatisticsType statisticsType;

    /**
     * 统计周期
     */
    private StatisticsPeriod statisticsPeriod;

    /**
     * 统计日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate statisticsDate;

    /**
     * 科目代码
     */
    private String subjectCode;

    /**
     * 学习时长（分钟）
     */
    private Integer studyMinutes;

    /**
     * 有效学习时长（分钟）
     */
    private Integer effectiveMinutes;

    /**
     * 答题数量
     */
    private Integer questionsCount;

    /**
     * 正确答题数量
     */
    private Integer correctCount;

    /**
     * 正确率
     */
    private BigDecimal accuracyRate;

    /**
     * 平均答题时间（秒）
     */
    private BigDecimal avgAnswerTime;

    /**
     * 学习会话数量
     */
    private Integer sessionCount;

    /**
     * 完成的学习计划数
     */
    private Integer completedPlans;

    /**
     * 学习天数
     */
    private Integer studyDays;

    /**
     * 连续学习天数
     */
    private Integer consecutiveDays;

    /**
     * 专注度评分
     */
    private BigDecimal focusScore;

    /**
     * 学习效率评分
     */
    private BigDecimal efficiencyScore;

    /**
     * 能力提升幅度
     */
    private BigDecimal abilityImprovement;

    /**
     * 知识点掌握数量
     */
    private Integer masteredKnowledgePoints;

    /**
     * 薄弱知识点数量
     */
    private Integer weakKnowledgePoints;

    /**
     * 错误次数
     */
    private Integer errorCount;

    /**
     * 中断次数
     */
    private Integer interruptionCount;

    /**
     * 暂停时长（分钟）
     */
    private Integer pauseMinutes;

    /**
     * 学习强度
     */
    private BigDecimal studyIntensity;

    /**
     * 详细统计数据
     */
    private String detailedStatistics;

    /**
     * 知识点统计
     */
    private String knowledgePointStats;

    /**
     * 题型统计
     */
    private String questionTypeStats;

    /**
     * 难度统计
     */
    private String difficultyStats;

    /**
     * 时间分布统计
     */
    private String timeDistribution;

    /**
     * 学习模式统计
     */
    private String studyPatternStats;

    /**
     * 排名信息
     */
    private String rankingInfo;

    /**
     * 对比数据
     */
    private String comparisonData;

    /**
     * 统计生成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime generatedAt;

    /**
     * 数据来源
     */
    private String dataSource;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 从LearningStatistics实体转换为DTO
     */
    public static LearningStatisticsDTO from(LearningStatistics statistics) {
        if (statistics == null) {
            return null;
        }

        return LearningStatisticsDTO.builder()
                .id(statistics.getId())
                .userId(statistics.getUserId())
                .statisticsType(statistics.getStatisticsType())
                .statisticsPeriod(statistics.getStatisticsPeriod())
                .statisticsDate(statistics.getStatisticsDate())
                .subjectCode(statistics.getSubjectCode())
                .studyMinutes(statistics.getStudyMinutes())
                .effectiveMinutes(statistics.getEffectiveMinutes())
                .questionsCount(statistics.getQuestionsCount())
                .correctCount(statistics.getCorrectCount())
                .accuracyRate(statistics.getAccuracyRate())
                .avgAnswerTime(statistics.getAvgAnswerTime())
                .sessionCount(statistics.getSessionCount())
                .completedPlans(statistics.getCompletedPlans())
                .studyDays(statistics.getStudyDays())
                .consecutiveDays(statistics.getConsecutiveDays())
                .focusScore(statistics.getFocusScore())
                .efficiencyScore(statistics.getEfficiencyScore())
                .abilityImprovement(statistics.getAbilityImprovement())
                .masteredKnowledgePoints(statistics.getMasteredKnowledgePoints())
                .weakKnowledgePoints(statistics.getWeakKnowledgePoints())
                .errorCount(statistics.getErrorCount())
                .interruptionCount(statistics.getInterruptionCount())
                .pauseMinutes(statistics.getPauseMinutes())
                .studyIntensity(statistics.getStudyIntensity())
                .detailedStatistics(statistics.getDetailedStatistics())
                .knowledgePointStats(statistics.getKnowledgePointStats())
                .questionTypeStats(statistics.getQuestionTypeStats())
                .difficultyStats(statistics.getDifficultyStats())
                .timeDistribution(statistics.getTimeDistribution())
                .studyPatternStats(statistics.getStudyPatternStats())
                .rankingInfo(statistics.getRankingInfo())
                .comparisonData(statistics.getComparisonData())
                .generatedAt(statistics.getGeneratedAt())
                .dataSource(statistics.getDataSource())
                .createdAt(statistics.getCreatedAt())
                .updatedAt(statistics.getUpdatedAt())
                .build();
    }

    /**
     * 获取统计类型描述
     */
    public String getStatisticsTypeDescription() {
        return statisticsType != null ? statisticsType.getDescription() : "";
    }

    /**
     * 获取统计周期描述
     */
    public String getStatisticsPeriodDescription() {
        return statisticsPeriod != null ? statisticsPeriod.getDescription() : "";
    }

    /**
     * 获取正确率百分比
     */
    public String getAccuracyPercentage() {
        if (accuracyRate == null) {
            return "0%";
        }
        return accuracyRate.multiply(BigDecimal.valueOf(100))
                .setScale(1, BigDecimal.ROUND_HALF_UP) + "%";
    }

    /**
     * 获取学习效率等级
     */
    public String getEfficiencyLevel() {
        if (efficiencyScore == null) {
            return "未知";
        }
        
        double score = efficiencyScore.doubleValue();
        if (score >= 8.0) {
            return "优秀";
        } else if (score >= 6.0) {
            return "良好";
        } else if (score >= 4.0) {
            return "一般";
        } else {
            return "待提高";
        }
    }

    /**
     * 获取专注度等级
     */
    public String getFocusLevel() {
        if (focusScore == null) {
            return "未知";
        }
        
        double score = focusScore.doubleValue();
        if (score >= 8.0) {
            return "高度专注";
        } else if (score >= 6.0) {
            return "较为专注";
        } else if (score >= 4.0) {
            return "一般专注";
        } else {
            return "容易分心";
        }
    }

    /**
     * 获取学习强度等级
     */
    public String getStudyIntensityLevel() {
        if (studyIntensity == null) {
            return "未知";
        }
        
        double intensity = studyIntensity.doubleValue();
        if (intensity >= 8.0) {
            return "高强度";
        } else if (intensity >= 6.0) {
            return "中高强度";
        } else if (intensity >= 4.0) {
            return "中等强度";
        } else if (intensity >= 2.0) {
            return "低强度";
        } else {
            return "很低强度";
        }
    }

    /**
     * 计算学习效率（题目数/小时）
     */
    public double getQuestionsPerHour() {
        if (studyMinutes == null || studyMinutes == 0 || questionsCount == null) {
            return 0.0;
        }
        return (double) questionsCount / studyMinutes * 60;
    }

    /**
     * 计算平均每日学习时长
     */
    public double getAverageStudyTimePerDay() {
        if (studyDays == null || studyDays == 0 || studyMinutes == null) {
            return 0.0;
        }
        return (double) studyMinutes / studyDays;
    }

    /**
     * 检查是否为今日统计
     */
    public boolean isToday() {
        return LocalDate.now().equals(this.statisticsDate);
    }

    /**
     * 检查是否为本周统计
     */
    public boolean isThisWeek() {
        LocalDate now = LocalDate.now();
        LocalDate weekStart = now.minusDays(now.getDayOfWeek().getValue() - 1);
        return !this.statisticsDate.isBefore(weekStart) && !this.statisticsDate.isAfter(now);
    }

    /**
     * 检查是否为本月统计
     */
    public boolean isThisMonth() {
        LocalDate now = LocalDate.now();
        return this.statisticsDate.getYear() == now.getYear() && 
               this.statisticsDate.getMonth() == now.getMonth();
    }

}
