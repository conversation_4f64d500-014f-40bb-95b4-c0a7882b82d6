package com.chiron.smartlearnsystem.analytics.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 统计类型枚举
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum StatisticsType {

    /**
     * 学习时长统计
     */
    STUDY_TIME_STATS("STUDY_TIME_STATS", "学习时长统计", "统计用户的学习时长数据"),

    /**
     * 答题统计
     */
    QUESTION_STATS("QUESTION_STATS", "答题统计", "统计用户的答题情况"),

    /**
     * 正确率统计
     */
    ACCURACY_STATS("ACCURACY_STATS", "正确率统计", "统计用户的答题正确率"),

    /**
     * 学习进度统计
     */
    PROGRESS_STATS("PROGRESS_STATS", "学习进度统计", "统计用户的学习进度"),

    /**
     * 能力提升统计
     */
    ABILITY_IMPROVEMENT_STATS("ABILITY_IMPROVEMENT_STATS", "能力提升统计", "统计用户的能力提升情况"),

    /**
     * 知识点掌握统计
     */
    KNOWLEDGE_MASTERY_STATS("KNOWLEDGE_MASTERY_STATS", "知识点掌握统计", "统计用户对各知识点的掌握情况"),

    /**
     * 学习效率统计
     */
    EFFICIENCY_STATS("EFFICIENCY_STATS", "学习效率统计", "统计用户的学习效率"),

    /**
     * 学习习惯统计
     */
    STUDY_HABIT_STATS("STUDY_HABIT_STATS", "学习习惯统计", "统计用户的学习习惯和模式"),

    /**
     * 错误模式统计
     */
    ERROR_PATTERN_STATS("ERROR_PATTERN_STATS", "错误模式统计", "统计用户的错误模式和类型"),

    /**
     * 学习计划统计
     */
    STUDY_PLAN_STATS("STUDY_PLAN_STATS", "学习计划统计", "统计用户的学习计划执行情况"),

    /**
     * 专注度统计
     */
    FOCUS_STATS("FOCUS_STATS", "专注度统计", "统计用户的学习专注度"),

    /**
     * 坚持度统计
     */
    PERSISTENCE_STATS("PERSISTENCE_STATS", "坚持度统计", "统计用户的学习坚持度"),

    /**
     * 综合统计
     */
    COMPREHENSIVE_STATS("COMPREHENSIVE_STATS", "综合统计", "用户学习情况的综合统计");

    private final String code;
    private final String name;
    private final String description;

    /**
     * 根据代码获取统计类型
     */
    public static StatisticsType getByCode(String code) {
        for (StatisticsType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return COMPREHENSIVE_STATS; // 默认返回综合统计
    }

    /**
     * 是否为基础统计
     */
    public boolean isBasicStats() {
        return this == STUDY_TIME_STATS || this == QUESTION_STATS || 
               this == ACCURACY_STATS || this == PROGRESS_STATS;
    }

    /**
     * 是否为高级统计
     */
    public boolean isAdvancedStats() {
        return this == ABILITY_IMPROVEMENT_STATS || this == KNOWLEDGE_MASTERY_STATS || 
               this == EFFICIENCY_STATS || this == ERROR_PATTERN_STATS;
    }

    /**
     * 是否为行为统计
     */
    public boolean isBehaviorStats() {
        return this == STUDY_HABIT_STATS || this == FOCUS_STATS || 
               this == PERSISTENCE_STATS;
    }

    /**
     * 获取统计优先级（1-10，10最高）
     */
    public int getStatisticsPriority() {
        switch (this) {
            case COMPREHENSIVE_STATS:
                return 10; // 综合统计最高优先级
            case PROGRESS_STATS:
                return 9; // 进度统计很高优先级
            case ABILITY_IMPROVEMENT_STATS:
                return 8; // 能力提升高优先级
            case ACCURACY_STATS:
                return 7; // 正确率较高优先级
            case STUDY_TIME_STATS:
                return 6; // 学习时长中等优先级
            case QUESTION_STATS:
                return 6; // 答题统计中等优先级
            case KNOWLEDGE_MASTERY_STATS:
                return 5; // 知识点掌握中等优先级
            case EFFICIENCY_STATS:
                return 5; // 学习效率中等优先级
            case STUDY_PLAN_STATS:
                return 4; // 学习计划较低优先级
            case ERROR_PATTERN_STATS:
                return 4; // 错误模式较低优先级
            case STUDY_HABIT_STATS:
                return 3; // 学习习惯较低优先级
            case FOCUS_STATS:
                return 3; // 专注度较低优先级
            case PERSISTENCE_STATS:
                return 2; // 坚持度低优先级
            default:
                return 5;
        }
    }

    /**
     * 获取计算复杂度（1-5）
     */
    public int getComputationComplexity() {
        switch (this) {
            case COMPREHENSIVE_STATS:
                return 5; // 最高复杂度
            case ABILITY_IMPROVEMENT_STATS:
            case ERROR_PATTERN_STATS:
                return 4; // 高复杂度
            case KNOWLEDGE_MASTERY_STATS:
            case EFFICIENCY_STATS:
            case STUDY_HABIT_STATS:
                return 3; // 中等复杂度
            case PROGRESS_STATS:
            case FOCUS_STATS:
            case PERSISTENCE_STATS:
            case STUDY_PLAN_STATS:
                return 2; // 较低复杂度
            case STUDY_TIME_STATS:
            case QUESTION_STATS:
            case ACCURACY_STATS:
                return 1; // 最低复杂度
            default:
                return 3;
        }
    }

    /**
     * 是否需要实时更新
     */
    public boolean needsRealTimeUpdate() {
        return this == STUDY_TIME_STATS || this == QUESTION_STATS || 
               this == ACCURACY_STATS || this == PROGRESS_STATS;
    }

    /**
     * 获取更新频率（分钟）
     */
    public int getUpdateFrequency() {
        switch (this) {
            case STUDY_TIME_STATS:
            case QUESTION_STATS:
            case ACCURACY_STATS:
                return 5; // 5分钟更新一次
            case PROGRESS_STATS:
                return 15; // 15分钟更新一次
            case EFFICIENCY_STATS:
            case FOCUS_STATS:
                return 30; // 30分钟更新一次
            case ABILITY_IMPROVEMENT_STATS:
            case KNOWLEDGE_MASTERY_STATS:
                return 60; // 1小时更新一次
            case STUDY_HABIT_STATS:
            case ERROR_PATTERN_STATS:
            case PERSISTENCE_STATS:
                return 360; // 6小时更新一次
            case STUDY_PLAN_STATS:
                return 720; // 12小时更新一次
            case COMPREHENSIVE_STATS:
                return 1440; // 24小时更新一次
            default:
                return 60; // 默认1小时
        }
    }

}
