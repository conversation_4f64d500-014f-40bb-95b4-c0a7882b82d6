package com.chiron.smartlearnsystem.analytics.dto.request;

import com.chiron.smartlearnsystem.analytics.enums.StatisticsPeriod;
import com.chiron.smartlearnsystem.analytics.enums.StatisticsType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDate;

/**
 * 统计查询请求
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StatisticsQueryRequest {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 统计类型
     */
    private StatisticsType statisticsType = StatisticsType.COMPREHENSIVE_STATS;

    /**
     * 统计周期
     */
    private StatisticsPeriod statisticsPeriod = StatisticsPeriod.DAILY;

    /**
     * 开始日期
     */
    private LocalDate startDate;

    /**
     * 结束日期
     */
    private LocalDate endDate;

    /**
     * 科目代码
     */
    @Size(max = 20, message = "科目代码长度不能超过20个字符")
    private String subjectCode;

    /**
     * 是否包含详细数据
     */
    private Boolean includeDetails = false;

    /**
     * 是否包含趋势分析
     */
    private Boolean includeTrend = false;

    /**
     * 是否包含排名信息
     */
    private Boolean includeRanking = false;

    /**
     * 验证日期范围
     */
    public boolean isValidDateRange() {
        return startDate == null || endDate == null || !endDate.isBefore(startDate);
    }

    /**
     * 获取查询周期天数
     */
    public long getQueryPeriodDays() {
        if (startDate == null || endDate == null) {
            return statisticsPeriod.getIntervalMinutes() / 1440; // 转换为天数
        }
        return java.time.temporal.ChronoUnit.DAYS.between(startDate, endDate) + 1;
    }

}
