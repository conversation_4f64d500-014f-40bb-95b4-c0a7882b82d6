package com.chiron.smartlearnsystem.analytics.service;

import com.chiron.smartlearnsystem.analytics.dto.LearningStatisticsDTO;
import com.chiron.smartlearnsystem.analytics.dto.request.StatisticsQueryRequest;
import com.chiron.smartlearnsystem.analytics.entity.LearningStatistics;
import com.chiron.smartlearnsystem.analytics.enums.StatisticsPeriod;
import com.chiron.smartlearnsystem.analytics.enums.StatisticsType;
import com.chiron.smartlearnsystem.analytics.repository.LearningStatisticsRepository;
import com.chiron.smartlearnsystem.common.core.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 学习统计服务
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class LearningStatisticsService {

    private final LearningStatisticsRepository statisticsRepository;
    private final RedisTemplate<String, Object> redisTemplate;

    private static final String STATISTICS_CACHE_KEY = "analytics:statistics:";
    private static final String USER_STATS_CACHE_KEY = "analytics:user:stats:";
    private static final int CACHE_EXPIRE_MINUTES = 30;

    /**
     * 获取用户学习统计
     */
    @Transactional(readOnly = true)
    public List<LearningStatisticsDTO> getUserStatistics(StatisticsQueryRequest request) {
        log.info("获取用户学习统计: userId={}, type={}, period={}", 
                request.getUserId(), request.getStatisticsType(), request.getStatisticsPeriod());

        // 检查缓存
        String cacheKey = generateCacheKey(request);
        List<LearningStatisticsDTO> cachedStats = 
                (List<LearningStatisticsDTO>) redisTemplate.opsForValue().get(cacheKey);
        if (cachedStats != null) {
            return cachedStats;
        }

        // 从数据库查询
        List<LearningStatistics> statistics = queryStatistics(request);

        // 转换为DTO
        List<LearningStatisticsDTO> statisticsDTOs = statistics.stream()
                .map(LearningStatisticsDTO::from)
                .collect(Collectors.toList());

        // 缓存结果
        redisTemplate.opsForValue().set(cacheKey, statisticsDTOs, CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);

        return statisticsDTOs;
    }

    /**
     * 获取用户今日统计
     */
    @Transactional(readOnly = true)
    public LearningStatisticsDTO getUserTodayStatistics(Long userId, String subjectCode) {
        log.debug("获取用户今日统计: userId={}, subject={}", userId, subjectCode);

        LearningStatistics todayStats = statisticsRepository
                .findByUserIdAndStatisticsDateAndSubjectCode(userId, LocalDate.now(), subjectCode)
                .orElse(null);

        if (todayStats == null) {
            // 创建空的今日统计
            todayStats = createEmptyStatistics(userId, subjectCode, LocalDate.now(), 
                    StatisticsType.COMPREHENSIVE_STATS, StatisticsPeriod.DAILY);
        }

        return LearningStatisticsDTO.from(todayStats);
    }

    /**
     * 获取用户本周统计
     */
    @Transactional(readOnly = true)
    public LearningStatisticsDTO getUserWeeklyStatistics(Long userId, String subjectCode) {
        log.debug("获取用户本周统计: userId={}, subject={}", userId, subjectCode);

        LocalDate weekStart = LocalDate.now().minusDays(LocalDate.now().getDayOfWeek().getValue() - 1);
        
        List<LearningStatistics> weeklyStats = statisticsRepository
                .findByUserIdAndStatisticsDateBetweenAndSubjectCode(
                        userId, weekStart, LocalDate.now(), subjectCode);

        return aggregateStatistics(weeklyStats, StatisticsPeriod.WEEKLY);
    }

    /**
     * 获取用户本月统计
     */
    @Transactional(readOnly = true)
    public LearningStatisticsDTO getUserMonthlyStatistics(Long userId, String subjectCode) {
        log.debug("获取用户本月统计: userId={}, subject={}", userId, subjectCode);

        LocalDate monthStart = LocalDate.now().withDayOfMonth(1);
        
        List<LearningStatistics> monthlyStats = statisticsRepository
                .findByUserIdAndStatisticsDateBetweenAndSubjectCode(
                        userId, monthStart, LocalDate.now(), subjectCode);

        return aggregateStatistics(monthlyStats, StatisticsPeriod.MONTHLY);
    }

    /**
     * 更新用户实时统计
     */
    public void updateRealTimeStatistics(Long userId, String subjectCode, 
                                       Integer studyMinutes, Integer questionsCount, 
                                       Integer correctCount) {
        log.debug("更新用户实时统计: userId={}, subject={}, minutes={}, questions={}, correct={}", 
                userId, subjectCode, studyMinutes, questionsCount, correctCount);

        LocalDate today = LocalDate.now();
        
        // 获取或创建今日统计
        LearningStatistics todayStats = statisticsRepository
                .findByUserIdAndStatisticsDateAndSubjectCode(userId, today, subjectCode)
                .orElse(createEmptyStatistics(userId, subjectCode, today, 
                        StatisticsType.COMPREHENSIVE_STATS, StatisticsPeriod.DAILY));

        // 更新统计数据
        todayStats.setStudyMinutes(todayStats.getStudyMinutes() + studyMinutes);
        todayStats.setQuestionsCount(todayStats.getQuestionsCount() + questionsCount);
        todayStats.setCorrectCount(todayStats.getCorrectCount() + correctCount);
        todayStats.setSessionCount(todayStats.getSessionCount() + 1);

        // 重新计算统计指标
        todayStats.updateStatistics();

        // 保存统计
        statisticsRepository.save(todayStats);

        // 清除相关缓存
        clearUserStatisticsCache(userId);
    }

    /**
     * 计算学习趋势
     */
    @Transactional(readOnly = true)
    public Map<String, Object> calculateLearningTrend(Long userId, String subjectCode, int days) {
        log.info("计算学习趋势: userId={}, subject={}, days={}", userId, subjectCode, days);

        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusDays(days - 1);

        List<LearningStatistics> statistics = statisticsRepository
                .findByUserIdAndStatisticsDateBetweenAndSubjectCode(userId, startDate, endDate, subjectCode);

        return analyzeTrend(statistics);
    }

    /**
     * 获取学习排名
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getUserRanking(Long userId, String subjectCode, StatisticsPeriod period) {
        log.info("获取用户排名: userId={}, subject={}, period={}", userId, subjectCode, period);

        // TODO: 实现排名计算逻辑
        // 1. 获取同期其他用户的统计数据
        // 2. 计算用户在各项指标上的排名
        // 3. 返回排名信息

        return Map.of(
                "studyTimeRank", 1,
                "accuracyRank", 1,
                "efficiencyRank", 1,
                "totalUsers", 100
        );
    }

    /**
     * 生成统计摘要
     */
    @Transactional(readOnly = true)
    public Map<String, Object> generateStatisticsSummary(Long userId, String subjectCode, 
                                                        LocalDate startDate, LocalDate endDate) {
        log.info("生成统计摘要: userId={}, subject={}, period={}-{}", 
                userId, subjectCode, startDate, endDate);

        List<LearningStatistics> statistics = statisticsRepository
                .findByUserIdAndStatisticsDateBetweenAndSubjectCode(userId, startDate, endDate, subjectCode);

        if (statistics.isEmpty()) {
            return createEmptySummary();
        }

        // 计算汇总数据
        int totalStudyMinutes = statistics.stream().mapToInt(LearningStatistics::getStudyMinutes).sum();
        int totalQuestions = statistics.stream().mapToInt(LearningStatistics::getQuestionsCount).sum();
        int totalCorrect = statistics.stream().mapToInt(LearningStatistics::getCorrectCount).sum();
        int totalSessions = statistics.stream().mapToInt(LearningStatistics::getSessionCount).sum();
        int studyDays = (int) statistics.stream().filter(s -> s.getStudyMinutes() > 0).count();

        BigDecimal avgAccuracy = totalQuestions > 0 ? 
                BigDecimal.valueOf(totalCorrect).divide(BigDecimal.valueOf(totalQuestions), 4, BigDecimal.ROUND_HALF_UP) :
                BigDecimal.ZERO;

        return Map.of(
                "totalStudyMinutes", totalStudyMinutes,
                "totalQuestions", totalQuestions,
                "totalCorrect", totalCorrect,
                "totalSessions", totalSessions,
                "studyDays", studyDays,
                "averageAccuracy", avgAccuracy,
                "averageStudyTimePerDay", studyDays > 0 ? totalStudyMinutes / studyDays : 0,
                "averageQuestionsPerDay", studyDays > 0 ? totalQuestions / studyDays : 0
        );
    }

    /**
     * 查询统计数据
     */
    private List<LearningStatistics> queryStatistics(StatisticsQueryRequest request) {
        if (request.getStartDate() != null && request.getEndDate() != null) {
            return statisticsRepository.findByUserIdAndStatisticsDateBetweenAndSubjectCode(
                    request.getUserId(), request.getStartDate(), request.getEndDate(), request.getSubjectCode());
        } else {
            return statisticsRepository.findByUserIdAndStatisticsTypeAndStatisticsPeriod(
                    request.getUserId(), request.getStatisticsType(), request.getStatisticsPeriod());
        }
    }

    /**
     * 聚合统计数据
     */
    private LearningStatisticsDTO aggregateStatistics(List<LearningStatistics> statistics, StatisticsPeriod period) {
        if (statistics.isEmpty()) {
            return LearningStatisticsDTO.from(createEmptyStatistics(null, null, LocalDate.now(), 
                    StatisticsType.COMPREHENSIVE_STATS, period));
        }

        // 聚合计算
        LearningStatistics first = statistics.get(0);
        LearningStatistics aggregated = LearningStatistics.builder()
                .userId(first.getUserId())
                .subjectCode(first.getSubjectCode())
                .statisticsType(StatisticsType.COMPREHENSIVE_STATS)
                .statisticsPeriod(period)
                .statisticsDate(LocalDate.now())
                .studyMinutes(statistics.stream().mapToInt(LearningStatistics::getStudyMinutes).sum())
                .questionsCount(statistics.stream().mapToInt(LearningStatistics::getQuestionsCount).sum())
                .correctCount(statistics.stream().mapToInt(LearningStatistics::getCorrectCount).sum())
                .sessionCount(statistics.stream().mapToInt(LearningStatistics::getSessionCount).sum())
                .studyDays((int) statistics.stream().filter(s -> s.getStudyMinutes() > 0).count())
                .build();

        aggregated.updateStatistics();

        return LearningStatisticsDTO.from(aggregated);
    }

    /**
     * 分析趋势
     */
    private Map<String, Object> analyzeTrend(List<LearningStatistics> statistics) {
        if (statistics.size() < 2) {
            return Map.of("trend", "insufficient_data");
        }

        // 计算趋势
        List<Integer> studyMinutes = statistics.stream()
                .map(LearningStatistics::getStudyMinutes)
                .collect(Collectors.toList());

        List<BigDecimal> accuracyRates = statistics.stream()
                .map(s -> s.getAccuracyRate() != null ? s.getAccuracyRate() : BigDecimal.ZERO)
                .collect(Collectors.toList());

        return Map.of(
                "studyTimeTrend", calculateTrend(studyMinutes),
                "accuracyTrend", calculateTrend(accuracyRates.stream()
                        .map(BigDecimal::doubleValue)
                        .collect(Collectors.toList())),
                "totalDays", statistics.size(),
                "activeDays", statistics.stream().mapToInt(s -> s.getStudyMinutes() > 0 ? 1 : 0).sum()
        );
    }

    /**
     * 计算数值趋势
     */
    private String calculateTrend(List<? extends Number> values) {
        if (values.size() < 2) return "stable";

        double first = values.get(0).doubleValue();
        double last = values.get(values.size() - 1).doubleValue();

        if (last > first * 1.1) return "increasing";
        if (last < first * 0.9) return "decreasing";
        return "stable";
    }

    /**
     * 创建空统计记录
     */
    private LearningStatistics createEmptyStatistics(Long userId, String subjectCode, LocalDate date,
                                                    StatisticsType type, StatisticsPeriod period) {
        return LearningStatistics.builder()
                .userId(userId)
                .subjectCode(subjectCode)
                .statisticsType(type)
                .statisticsPeriod(period)
                .statisticsDate(date)
                .studyMinutes(0)
                .questionsCount(0)
                .correctCount(0)
                .sessionCount(0)
                .studyDays(0)
                .consecutiveDays(0)
                .generatedAt(LocalDateTime.now())
                .build();
    }

    /**
     * 创建空摘要
     */
    private Map<String, Object> createEmptySummary() {
        return Map.of(
                "totalStudyMinutes", 0,
                "totalQuestions", 0,
                "totalCorrect", 0,
                "totalSessions", 0,
                "studyDays", 0,
                "averageAccuracy", BigDecimal.ZERO,
                "averageStudyTimePerDay", 0,
                "averageQuestionsPerDay", 0
        );
    }

    /**
     * 生成缓存键
     */
    private String generateCacheKey(StatisticsQueryRequest request) {
        return USER_STATS_CACHE_KEY + request.getUserId() + ":" + 
               request.getStatisticsType() + ":" + request.getStatisticsPeriod() + ":" +
               request.getSubjectCode() + ":" + request.getStartDate() + ":" + request.getEndDate();
    }

    /**
     * 清除用户统计缓存
     */
    private void clearUserStatisticsCache(Long userId) {
        String pattern = USER_STATS_CACHE_KEY + userId + ":*";
        redisTemplate.delete(redisTemplate.keys(pattern));
    }

}
