package com.chiron.smartlearnsystem.study.repository;

import com.chiron.smartlearnsystem.study.entity.StudyPlan;
import com.chiron.smartlearnsystem.study.enums.PlanStatus;
import com.chiron.smartlearnsystem.study.enums.PlanType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 学习计划数据访问层
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Repository
public interface StudyPlanRepository extends JpaRepository<StudyPlan, Long>, JpaSpecificationExecutor<StudyPlan> {

    /**
     * 根据用户ID查找学习计划
     */
    List<StudyPlan> findByUserId(Long userId);

    /**
     * 根据用户ID和状态查找学习计划
     */
    List<StudyPlan> findByUserIdAndStatus(Long userId, PlanStatus status);

    /**
     * 根据用户ID和科目查找学习计划
     */
    List<StudyPlan> findByUserIdAndSubjectCode(Long userId, String subjectCode);

    /**
     * 根据用户ID、科目和状态查找学习计划
     */
    Optional<StudyPlan> findByUserIdAndSubjectCodeAndStatus(Long userId, String subjectCode, PlanStatus status);

    /**
     * 根据计划类型查找学习计划
     */
    List<StudyPlan> findByPlanType(PlanType planType);

    /**
     * 根据用户ID分页查找学习计划
     */
    Page<StudyPlan> findByUserId(Long userId, Pageable pageable);

    /**
     * 查找活跃的学习计划
     */
    @Query("SELECT sp FROM StudyPlan sp WHERE sp.userId = :userId AND sp.status = 'ACTIVE' AND sp.deleted = 0")
    List<StudyPlan> findActiveStudyPlans(@Param("userId") Long userId);

    /**
     * 查找即将到期的学习计划
     */
    @Query("SELECT sp FROM StudyPlan sp WHERE sp.status = 'ACTIVE' AND sp.endDate BETWEEN :startDate AND :endDate")
    List<StudyPlan> findExpiringPlans(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * 查找已过期但状态仍为活跃的计划
     */
    @Query("SELECT sp FROM StudyPlan sp WHERE sp.status = 'ACTIVE' AND sp.endDate < :currentDate")
    List<StudyPlan> findExpiredActivePlans(@Param("currentDate") LocalDate currentDate);

    /**
     * 根据目标考试日期查找计划
     */
    List<StudyPlan> findByTargetExamDateBetween(LocalDate startDate, LocalDate endDate);

    /**
     * 查找长时间未学习的计划
     */
    @Query("SELECT sp FROM StudyPlan sp WHERE sp.status = 'ACTIVE' AND " +
           "(sp.lastStudyTime IS NULL OR sp.lastStudyTime < :threshold)")
    List<StudyPlan> findInactivePlans(@Param("threshold") LocalDateTime threshold);

    /**
     * 根据进度范围查找计划
     */
    @Query("SELECT sp FROM StudyPlan sp WHERE sp.userId = :userId AND " +
           "sp.progressPercentage BETWEEN :minProgress AND :maxProgress")
    List<StudyPlan> findByProgressRange(@Param("userId") Long userId,
                                       @Param("minProgress") BigDecimal minProgress,
                                       @Param("maxProgress") BigDecimal maxProgress);

    /**
     * 统计用户各状态的计划数量
     */
    @Query("SELECT sp.status, COUNT(sp) FROM StudyPlan sp WHERE sp.userId = :userId GROUP BY sp.status")
    List<Object[]> countPlansByStatus(@Param("userId") Long userId);

    /**
     * 统计用户各科目的计划数量
     */
    @Query("SELECT sp.subjectCode, COUNT(sp) FROM StudyPlan sp WHERE sp.userId = :userId GROUP BY sp.subjectCode")
    List<Object[]> countPlansBySubject(@Param("userId") Long userId);

    /**
     * 获取用户学习统计
     */
    @Query("SELECT " +
           "SUM(sp.completedMinutes) as totalMinutes, " +
           "SUM(sp.completedQuestions) as totalQuestions, " +
           "AVG(sp.averageAccuracy) as avgAccuracy, " +
           "COUNT(sp) as totalPlans " +
           "FROM StudyPlan sp WHERE sp.userId = :userId")
    Object[] getUserStudyStatistics(@Param("userId") Long userId);

    /**
     * 查找需要智能调整的计划
     */
    @Query("SELECT sp FROM StudyPlan sp WHERE sp.autoAdjustEnabled = true AND sp.status = 'ACTIVE' AND " +
           "sp.lastStudyTime < :threshold")
    List<StudyPlan> findPlansNeedingAdjustment(@Param("threshold") LocalDateTime threshold);

    /**
     * 批量更新计划状态
     */
    @Modifying
    @Query("UPDATE StudyPlan sp SET sp.status = :newStatus WHERE sp.id IN :planIds")
    int batchUpdateStatus(@Param("planIds") List<Long> planIds, @Param("newStatus") PlanStatus newStatus);

    /**
     * 更新计划进度
     */
    @Modifying
    @Query("UPDATE StudyPlan sp SET sp.completedMinutes = sp.completedMinutes + :minutes, " +
           "sp.lastStudyTime = :studyTime WHERE sp.id = :planId")
    int updateStudyProgress(@Param("planId") Long planId,
                           @Param("minutes") Integer minutes,
                           @Param("studyTime") LocalDateTime studyTime);

    /**
     * 更新计划题目完成数
     */
    @Modifying
    @Query("UPDATE StudyPlan sp SET sp.completedQuestions = sp.completedQuestions + :count WHERE sp.id = :planId")
    int updateCompletedQuestions(@Param("planId") Long planId, @Param("count") Integer count);

    /**
     * 搜索学习计划
     */
    @Query("SELECT sp FROM StudyPlan sp WHERE sp.userId = :userId AND " +
           "(sp.name LIKE %:keyword% OR sp.description LIKE %:keyword%)")
    Page<StudyPlan> searchPlans(@Param("userId") Long userId,
                               @Param("keyword") String keyword,
                               Pageable pageable);

    /**
     * 查找用户最近的学习计划
     */
    @Query("SELECT sp FROM StudyPlan sp WHERE sp.userId = :userId ORDER BY sp.lastStudyTime DESC")
    List<StudyPlan> findRecentStudyPlans(@Param("userId") Long userId, Pageable pageable);

    /**
     * 检查用户是否有同科目的活跃计划
     */
    @Query("SELECT COUNT(sp) > 0 FROM StudyPlan sp WHERE sp.userId = :userId AND " +
           "sp.subjectCode = :subjectCode AND sp.status = 'ACTIVE'")
    boolean hasActiveSubjectPlan(@Param("userId") Long userId, @Param("subjectCode") String subjectCode);

    /**
     * 获取用户连续学习天数最多的计划
     */
    @Query("SELECT sp FROM StudyPlan sp WHERE sp.userId = :userId ORDER BY sp.consecutiveDays DESC")
    List<StudyPlan> findTopConsecutivePlans(@Param("userId") Long userId, Pageable pageable);

    /**
     * 查找高效学习计划（正确率高且进度快）
     */
    @Query("SELECT sp FROM StudyPlan sp WHERE sp.userId = :userId AND " +
           "sp.averageAccuracy >= :minAccuracy AND sp.progressPercentage >= :minProgress")
    List<StudyPlan> findEfficientPlans(@Param("userId") Long userId,
                                      @Param("minAccuracy") BigDecimal minAccuracy,
                                      @Param("minProgress") BigDecimal minProgress);

}
