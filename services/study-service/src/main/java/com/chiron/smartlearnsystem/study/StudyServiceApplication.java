package com.chiron.smartlearnsystem.study;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 学习服务启动类
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@SpringBootApplication(scanBasePackages = {
    "com.chiron.smartlearnsystem.study",
    "com.chiron.smartlearnsystem.common"
})
@EnableEurekaClient
@EnableFeignClients
@EnableJpaAuditing
@EnableAsync
@EnableScheduling
public class StudyServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(StudyServiceApplication.class, args);
    }

}
