-- 学习计划表
CREATE TABLE study_plans (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    name VARCHAR(200) NOT NULL COMMENT '计划名称',
    description TEXT COMMENT '计划描述',
    subject_code VARCHAR(20) NOT NULL COMMENT '科目代码',
    plan_type VARCHAR(20) NOT NULL COMMENT '计划类型',
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '计划状态',
    target_exam_date DATE COMMENT '目标考试日期',
    start_date DATE NOT NULL COMMENT '计划开始日期',
    end_date DATE COMMENT '计划结束日期',
    daily_study_minutes INT NOT NULL DEFAULT 60 COMMENT '每日学习时长目标（分钟）',
    weekly_study_days INT NOT NULL DEFAULT 5 COMMENT '每周学习天数目标',
    target_difficulty VARCHAR(20) NOT NULL DEFAULT 'MEDIUM' COMMENT '目标难度',
    study_focus JSON COMMENT '学习重点配置',
    knowledge_weights JSON COMMENT '知识点权重配置',
    progress_percentage DECIMAL(5,2) DEFAULT 0.00 COMMENT '总进度百分比',
    completed_minutes INT NOT NULL DEFAULT 0 COMMENT '已完成学习时长（分钟）',
    target_minutes INT COMMENT '目标学习时长（分钟）',
    completed_questions INT NOT NULL DEFAULT 0 COMMENT '已完成题目数量',
    target_questions INT COMMENT '目标题目数量',
    average_accuracy DECIMAL(5,4) COMMENT '平均正确率',
    last_study_time DATETIME COMMENT '最后学习时间',
    consecutive_days INT NOT NULL DEFAULT 0 COMMENT '连续学习天数',
    auto_adjust_enabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用智能调整',
    reminder_settings JSON COMMENT '提醒设置',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT NOT NULL DEFAULT 0,
    version BIGINT NOT NULL DEFAULT 0,
    
    INDEX idx_user_subject (user_id, subject_code),
    INDEX idx_status (status),
    INDEX idx_target_date (target_exam_date),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学习计划表';

-- 学习计划知识点表
CREATE TABLE study_plan_knowledge_points (
    plan_id BIGINT NOT NULL,
    knowledge_point VARCHAR(200) NOT NULL,
    PRIMARY KEY (plan_id, knowledge_point),
    FOREIGN KEY (plan_id) REFERENCES study_plans(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学习计划知识点表';

-- 学习阶段表
CREATE TABLE study_stages (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    plan_id BIGINT NOT NULL COMMENT '学习计划ID',
    name VARCHAR(200) NOT NULL COMMENT '阶段名称',
    description TEXT COMMENT '阶段描述',
    stage_type VARCHAR(20) NOT NULL COMMENT '阶段类型',
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING' COMMENT '阶段状态',
    stage_order INT NOT NULL COMMENT '阶段顺序',
    start_date DATE COMMENT '开始日期',
    end_date DATE COMMENT '结束日期',
    estimated_minutes INT COMMENT '预计学习时长（分钟）',
    actual_minutes INT NOT NULL DEFAULT 0 COMMENT '实际学习时长（分钟）',
    target_questions INT COMMENT '目标题目数量',
    completed_questions INT NOT NULL DEFAULT 0 COMMENT '已完成题目数量',
    target_accuracy DECIMAL(5,4) COMMENT '目标正确率',
    actual_accuracy DECIMAL(5,4) COMMENT '实际正确率',
    progress_percentage DECIMAL(5,2) DEFAULT 0.00 COMMENT '进度百分比',
    stage_config JSON COMMENT '阶段配置',
    prerequisites JSON COMMENT '前置条件',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT NOT NULL DEFAULT 0,
    version BIGINT NOT NULL DEFAULT 0,
    
    INDEX idx_plan_order (plan_id, stage_order),
    INDEX idx_status (status),
    INDEX idx_dates (start_date, end_date),
    FOREIGN KEY (plan_id) REFERENCES study_plans(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学习阶段表';

-- 学习阶段重点表
CREATE TABLE study_stage_focus_points (
    stage_id BIGINT NOT NULL,
    focus_point VARCHAR(200) NOT NULL,
    PRIMARY KEY (stage_id, focus_point),
    FOREIGN KEY (stage_id) REFERENCES study_stages(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学习阶段重点表';

-- 学习会话表
CREATE TABLE study_sessions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    plan_id BIGINT COMMENT '学习计划ID',
    stage_id BIGINT COMMENT '学习阶段ID',
    session_type VARCHAR(20) NOT NULL COMMENT '会话类型',
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '会话状态',
    start_time DATETIME NOT NULL COMMENT '开始时间',
    end_time DATETIME COMMENT '结束时间',
    planned_minutes INT COMMENT '计划学习时长（分钟）',
    actual_minutes INT COMMENT '实际学习时长（分钟）',
    effective_minutes INT COMMENT '有效学习时长（分钟）',
    subject_code VARCHAR(20) COMMENT '科目代码',
    study_content JSON COMMENT '学习内容',
    questions_completed INT NOT NULL DEFAULT 0 COMMENT '完成题目数量',
    questions_correct INT NOT NULL DEFAULT 0 COMMENT '正确题目数量',
    accuracy_rate DECIMAL(5,4) COMMENT '正确率',
    focus_score DECIMAL(3,1) COMMENT '专注度评分（1-10）',
    efficiency_score DECIMAL(3,1) COMMENT '学习效率评分（1-10）',
    interruption_count INT NOT NULL DEFAULT 0 COMMENT '中断次数',
    pause_minutes INT NOT NULL DEFAULT 0 COMMENT '暂停总时长（分钟）',
    environment_info TEXT COMMENT '学习环境信息',
    device_info TEXT COMMENT '设备信息',
    ip_address VARCHAR(50) COMMENT 'IP地址',
    study_notes TEXT COMMENT '学习笔记',
    session_summary TEXT COMMENT '会话总结',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT NOT NULL DEFAULT 0,
    version BIGINT NOT NULL DEFAULT 0,
    
    INDEX idx_user_date (user_id, start_time),
    INDEX idx_plan_stage (plan_id, stage_id),
    INDEX idx_status (status),
    INDEX idx_session_type (session_type),
    FOREIGN KEY (plan_id) REFERENCES study_plans(id) ON DELETE SET NULL,
    FOREIGN KEY (stage_id) REFERENCES study_stages(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学习会话表';

-- 学习会话知识点表
CREATE TABLE study_session_knowledge_points (
    session_id BIGINT NOT NULL,
    knowledge_point VARCHAR(200) NOT NULL,
    PRIMARY KEY (session_id, knowledge_point),
    FOREIGN KEY (session_id) REFERENCES study_sessions(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学习会话知识点表';

-- 学习进度表
CREATE TABLE learning_progress (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    subject_code VARCHAR(20) NOT NULL COMMENT '科目代码',
    knowledge_point VARCHAR(200) NOT NULL COMMENT '知识点',
    mastery_level DECIMAL(5,4) NOT NULL DEFAULT 0.0000 COMMENT '掌握程度（0-1）',
    study_count INT NOT NULL DEFAULT 0 COMMENT '学习次数',
    total_study_minutes INT NOT NULL DEFAULT 0 COMMENT '总学习时长（分钟）',
    total_questions INT NOT NULL DEFAULT 0 COMMENT '答题总数',
    correct_questions INT NOT NULL DEFAULT 0 COMMENT '正确答题数',
    current_accuracy DECIMAL(5,4) DEFAULT 0.0000 COMMENT '当前正确率',
    best_accuracy DECIMAL(5,4) DEFAULT 0.0000 COMMENT '历史最高正确率',
    consecutive_correct INT NOT NULL DEFAULT 0 COMMENT '连续正确次数',
    max_consecutive_correct INT NOT NULL DEFAULT 0 COMMENT '最大连续正确次数',
    error_count INT NOT NULL DEFAULT 0 COMMENT '错误次数',
    last_error_time DATETIME COMMENT '最近错误时间',
    first_study_time DATETIME COMMENT '首次学习时间',
    last_study_time DATETIME COMMENT '最后学习时间',
    progress_date DATE NOT NULL COMMENT '进度日期',
    study_intensity DECIMAL(3,1) DEFAULT 5.0 COMMENT '学习强度（1-10）',
    forgetting_curve JSON COMMENT '遗忘曲线参数',
    next_review_time DATETIME COMMENT '下次复习时间',
    review_interval INT NOT NULL DEFAULT 1 COMMENT '复习间隔（天）',
    review_count INT NOT NULL DEFAULT 0 COMMENT '复习次数',
    efficiency_score DECIMAL(3,1) COMMENT '学习效率评分（1-10）',
    progress_trend JSON COMMENT '进度变化趋势',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT NOT NULL DEFAULT 0,
    version BIGINT NOT NULL DEFAULT 0,
    
    UNIQUE KEY uk_user_subject_knowledge (user_id, subject_code, knowledge_point),
    INDEX idx_user_subject (user_id, subject_code),
    INDEX idx_user_knowledge (user_id, knowledge_point),
    INDEX idx_progress_date (progress_date),
    INDEX idx_mastery_level (mastery_level)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学习进度表';
