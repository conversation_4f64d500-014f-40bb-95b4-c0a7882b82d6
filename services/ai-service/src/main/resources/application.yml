server:
  port: 8083

spring:
  application:
    name: ai-service
  
  profiles:
    active: dev
  
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *******************************************************************************************************************************************************
    username: root
    password: 123456
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: AiServiceHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
  
  redis:
    host: localhost
    port: 6379
    password: 
    database: 3
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0
  
  data:
    mongodb:
      host: localhost
      port: 27017
      database: smartlearn_ai_models
      username: 
      password: 
  
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
    validate-on-migrate: true

eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
    register-with-eureka: true
    fetch-registry: true
  instance:
    prefer-ip-address: true
    lease-renewal-interval-in-seconds: 30
    lease-expiration-duration-in-seconds: 90

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true

logging:
  level:
    com.chiron.smartlearnsystem.ai: INFO
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/ai-service.log

# 自定义配置
smartlearn:
  ai:
    # 推荐系统配置
    recommendation:
      default-count: 5
      max-count: 20
      cache-expire-hours: 1
      min-confidence: 0.5
      algorithm-version: "1.0"
    
    # 能力评估配置
    assessment:
      irt-theta-min: -3.0
      irt-theta-max: 3.0
      irt-theta-default: 0.0
      min-sample-size: 10
      confidence-threshold: 0.7
      cache-expire-hours: 6
    
    # 机器学习模型配置
    model:
      storage-path: "/data/ai-models"
      max-model-size: 1073741824  # 1GB
      training-timeout: 3600      # 1小时
      prediction-timeout: 30      # 30秒
      model-versions-to-keep: 3
    
    # 算法配置
    algorithm:
      collaborative-filtering:
        similarity-threshold: 0.3
        neighbor-count: 50
      content-based:
        feature-weight-threshold: 0.1
        max-features: 1000
      irt:
        max-iterations: 100
        convergence-threshold: 0.001
        learning-rate: 0.1
    
    # 数据处理配置
    data:
      batch-size: 1000
      max-processing-time: 300  # 5分钟
      cleanup-interval: 86400   # 24小时
      retention-days: 90        # 数据保留90天
