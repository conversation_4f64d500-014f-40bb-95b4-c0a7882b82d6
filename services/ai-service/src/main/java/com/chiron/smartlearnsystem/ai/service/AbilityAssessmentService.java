package com.chiron.smartlearnsystem.ai.service;

import com.chiron.smartlearnsystem.ai.dto.UserAbilityAssessmentDTO;
import com.chiron.smartlearnsystem.ai.dto.request.AbilityAssessmentRequest;
import com.chiron.smartlearnsystem.ai.entity.UserAbilityAssessment;
import com.chiron.smartlearnsystem.ai.enums.AssessmentType;
import com.chiron.smartlearnsystem.ai.repository.UserAbilityAssessmentRepository;
import com.chiron.smartlearnsystem.common.core.enums.Difficulty;
import com.chiron.smartlearnsystem.common.core.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.math3.distribution.NormalDistribution;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 能力评估服务
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class AbilityAssessmentService {

    private final UserAbilityAssessmentRepository assessmentRepository;
    private final RedisTemplate<String, Object> redisTemplate;

    private static final String ASSESSMENT_CACHE_KEY = "ai:assessment:";
    private static final String USER_ABILITY_CACHE_KEY = "ai:user:ability:";
    private static final int CACHE_EXPIRE_HOURS = 6;

    // IRT模型参数
    private static final double DEFAULT_THETA = 0.0; // 默认能力参数
    private static final double THETA_MIN = -3.0;    // 能力参数最小值
    private static final double THETA_MAX = 3.0;     // 能力参数最大值

    /**
     * 进行能力评估
     */
    public UserAbilityAssessmentDTO assessUserAbility(AbilityAssessmentRequest request) {
        log.info("进行能力评估: userId={}, subject={}, type={}", 
                request.getUserId(), request.getSubjectCode(), request.getAssessmentType());

        // 检查是否需要评估
        if (!needsAssessment(request.getUserId(), request.getSubjectCode(), request.getAssessmentType())) {
            // 返回最新的评估结果
            UserAbilityAssessment latestAssessment = getLatestAssessment(request.getUserId(), request.getSubjectCode());
            if (latestAssessment != null) {
                return UserAbilityAssessmentDTO.from(latestAssessment);
            }
        }

        // 执行评估
        UserAbilityAssessment assessment = performAssessment(request);

        // 保存评估结果
        UserAbilityAssessment savedAssessment = assessmentRepository.save(assessment);

        // 清除缓存
        clearUserAbilityCache(request.getUserId(), request.getSubjectCode());

        log.info("能力评估完成: userId={}, score={}", request.getUserId(), assessment.getOverallAbilityScore());

        return UserAbilityAssessmentDTO.from(savedAssessment);
    }

    /**
     * 获取用户最新能力评估
     */
    @Transactional(readOnly = true)
    public UserAbilityAssessmentDTO getUserLatestAbility(Long userId, String subjectCode) {
        // 先从缓存获取
        String cacheKey = USER_ABILITY_CACHE_KEY + userId + ":" + subjectCode;
        UserAbilityAssessmentDTO cachedAssessment = 
                (UserAbilityAssessmentDTO) redisTemplate.opsForValue().get(cacheKey);
        if (cachedAssessment != null) {
            return cachedAssessment;
        }

        // 从数据库获取
        UserAbilityAssessment assessment = getLatestAssessment(userId, subjectCode);
        if (assessment == null) {
            return null;
        }

        UserAbilityAssessmentDTO assessmentDTO = UserAbilityAssessmentDTO.from(assessment);

        // 缓存结果
        redisTemplate.opsForValue().set(cacheKey, assessmentDTO, CACHE_EXPIRE_HOURS, TimeUnit.HOURS);

        return assessmentDTO;
    }

    /**
     * 获取用户能力历史
     */
    @Transactional(readOnly = true)
    public List<UserAbilityAssessmentDTO> getUserAbilityHistory(Long userId, String subjectCode) {
        log.info("获取用户能力历史: userId={}, subject={}", userId, subjectCode);

        List<UserAbilityAssessment> assessments = 
                assessmentRepository.findByUserIdAndSubjectCodeOrderByAssessmentDateDesc(userId, subjectCode);

        return assessments.stream()
                .map(UserAbilityAssessmentDTO::from)
                .collect(Collectors.toList());
    }

    /**
     * 实时更新用户能力
     */
    public void updateAbilityRealTime(Long userId, String subjectCode, boolean isCorrect, 
                                    String questionDifficulty, Integer timeSpent) {
        log.debug("实时更新用户能力: userId={}, subject={}, correct={}", userId, subjectCode, isCorrect);

        // 获取当前能力评估
        UserAbilityAssessment currentAssessment = getLatestAssessment(userId, subjectCode);
        
        if (currentAssessment == null) {
            // 如果没有评估记录，创建初始评估
            currentAssessment = createInitialAssessment(userId, subjectCode);
        }

        // 使用IRT模型更新能力参数
        updateIRTTheta(currentAssessment, isCorrect, questionDifficulty);

        // 更新其他统计信息
        updateAssessmentStatistics(currentAssessment, isCorrect, timeSpent);

        // 保存更新
        assessmentRepository.save(currentAssessment);

        // 清除缓存
        clearUserAbilityCache(userId, subjectCode);
    }

    /**
     * 执行能力评估
     */
    private UserAbilityAssessment performAssessment(AbilityAssessmentRequest request) {
        // 获取历史评估
        UserAbilityAssessment previousAssessment = getLatestAssessment(request.getUserId(), request.getSubjectCode());

        // 创建新的评估记录
        UserAbilityAssessment assessment = UserAbilityAssessment.builder()
                .userId(request.getUserId())
                .subjectCode(request.getSubjectCode())
                .assessmentType(request.getAssessmentType())
                .assessmentDate(LocalDateTime.now())
                .algorithmVersion("1.0")
                .sampleSize(request.getSampleSize())
                .build();

        // 根据评估类型执行不同的评估算法
        switch (request.getAssessmentType()) {
            case INITIAL_ASSESSMENT:
                performInitialAssessment(assessment, request);
                break;
            case REAL_TIME_ASSESSMENT:
                performRealTimeAssessment(assessment, request, previousAssessment);
                break;
            case PERIODIC_ASSESSMENT:
                performPeriodicAssessment(assessment, request, previousAssessment);
                break;
            case COMPREHENSIVE_ASSESSMENT:
                performComprehensiveAssessment(assessment, request);
                break;
            default:
                performDefaultAssessment(assessment, request);
                break;
        }

        // 设置下次评估时间
        assessment.updateNextAssessmentTime();

        return assessment;
    }

    /**
     * 执行初始评估
     */
    private void performInitialAssessment(UserAbilityAssessment assessment, AbilityAssessmentRequest request) {
        // 基于初始答题数据进行评估
        BigDecimal initialScore = calculateInitialAbilityScore(request);
        
        assessment.setOverallAbilityScore(initialScore);
        assessment.setIrtTheta(BigDecimal.valueOf(DEFAULT_THETA));
        assessment.setIrtThetaSe(BigDecimal.valueOf(0.5));
        assessment.setCurrentDifficulty(Difficulty.EASY);
        assessment.setRecommendedDifficulty(determineRecommendedDifficulty(initialScore));
        assessment.setConfidenceLevel(BigDecimal.valueOf(0.6));
        assessment.setAccuracyRate(request.getAccuracyRate());
        assessment.setAnswerSpeed(request.getAverageAnswerTime());
    }

    /**
     * 执行实时评估
     */
    private void performRealTimeAssessment(UserAbilityAssessment assessment, 
                                         AbilityAssessmentRequest request,
                                         UserAbilityAssessment previousAssessment) {
        if (previousAssessment != null) {
            // 基于前一次评估进行增量更新
            BigDecimal updatedScore = updateAbilityScore(previousAssessment.getOverallAbilityScore(), request);
            assessment.setOverallAbilityScore(updatedScore);
            assessment.setIrtTheta(previousAssessment.getIrtTheta());
            assessment.setCurrentDifficulty(previousAssessment.getCurrentDifficulty());
        } else {
            performInitialAssessment(assessment, request);
        }
        
        assessment.setRecommendedDifficulty(determineRecommendedDifficulty(assessment.getOverallAbilityScore()));
        assessment.setConfidenceLevel(BigDecimal.valueOf(0.8));
        assessment.setAccuracyRate(request.getAccuracyRate());
        assessment.setAnswerSpeed(request.getAverageAnswerTime());
    }

    /**
     * 执行定期评估
     */
    private void performPeriodicAssessment(UserAbilityAssessment assessment, 
                                         AbilityAssessmentRequest request,
                                         UserAbilityAssessment previousAssessment) {
        // 综合分析一段时间内的学习数据
        BigDecimal periodicScore = calculatePeriodicAbilityScore(request, previousAssessment);
        
        assessment.setOverallAbilityScore(periodicScore);
        assessment.setRecommendedDifficulty(determineRecommendedDifficulty(periodicScore));
        assessment.setConfidenceLevel(BigDecimal.valueOf(0.9));
        assessment.setAccuracyRate(request.getAccuracyRate());
        assessment.setAnswerSpeed(request.getAverageAnswerTime());
        
        if (previousAssessment != null) {
            assessment.setIrtTheta(previousAssessment.getIrtTheta());
            assessment.setCurrentDifficulty(previousAssessment.getCurrentDifficulty());
        }
    }

    /**
     * 执行综合评估
     */
    private void performComprehensiveAssessment(UserAbilityAssessment assessment, AbilityAssessmentRequest request) {
        // 全面分析用户能力
        BigDecimal comprehensiveScore = calculateComprehensiveAbilityScore(request);
        
        assessment.setOverallAbilityScore(comprehensiveScore);
        assessment.setRecommendedDifficulty(determineRecommendedDifficulty(comprehensiveScore));
        assessment.setConfidenceLevel(BigDecimal.valueOf(0.95));
        assessment.setAccuracyRate(request.getAccuracyRate());
        assessment.setAnswerSpeed(request.getAverageAnswerTime());
        
        // 设置详细的能力分析
        assessment.setLearningSpeed(calculateLearningSpeed(request));
        assessment.setFocusScore(calculateFocusScore(request));
        assessment.setPersistenceScore(calculatePersistenceScore(request));
        assessment.setEfficiencyScore(calculateEfficiencyScore(request));
    }

    /**
     * 执行默认评估
     */
    private void performDefaultAssessment(UserAbilityAssessment assessment, AbilityAssessmentRequest request) {
        performInitialAssessment(assessment, request);
    }

    /**
     * 计算初始能力分数
     */
    private BigDecimal calculateInitialAbilityScore(AbilityAssessmentRequest request) {
        if (request.getAccuracyRate() == null) {
            return BigDecimal.valueOf(50); // 默认中等水平
        }
        
        // 基于正确率计算初始分数
        double score = request.getAccuracyRate().doubleValue() * 100;
        
        // 考虑答题速度的影响
        if (request.getAverageAnswerTime() != null) {
            double speedFactor = calculateSpeedFactor(request.getAverageAnswerTime());
            score = score * speedFactor;
        }
        
        return BigDecimal.valueOf(Math.max(0, Math.min(100, score)))
                .setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 更新能力分数
     */
    private BigDecimal updateAbilityScore(BigDecimal currentScore, AbilityAssessmentRequest request) {
        if (request.getAccuracyRate() == null) {
            return currentScore;
        }
        
        // 使用加权平均更新分数
        double newScore = request.getAccuracyRate().doubleValue() * 100;
        double updatedScore = currentScore.doubleValue() * 0.7 + newScore * 0.3;
        
        return BigDecimal.valueOf(Math.max(0, Math.min(100, updatedScore)))
                .setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 计算定期能力分数
     */
    private BigDecimal calculatePeriodicAbilityScore(AbilityAssessmentRequest request, 
                                                   UserAbilityAssessment previousAssessment) {
        // TODO: 实现更复杂的定期评估算法
        return calculateInitialAbilityScore(request);
    }

    /**
     * 计算综合能力分数
     */
    private BigDecimal calculateComprehensiveAbilityScore(AbilityAssessmentRequest request) {
        // TODO: 实现综合评估算法，考虑多个维度
        return calculateInitialAbilityScore(request);
    }

    /**
     * 使用IRT模型更新能力参数θ
     */
    private void updateIRTTheta(UserAbilityAssessment assessment, boolean isCorrect, String questionDifficulty) {
        double currentTheta = assessment.getIrtTheta() != null ? 
                assessment.getIrtTheta().doubleValue() : DEFAULT_THETA;
        
        // 题目难度参数（简化处理）
        double itemDifficulty = getDifficultyParameter(questionDifficulty);
        
        // 使用最大似然估计更新θ
        double newTheta = updateThetaWithMLE(currentTheta, isCorrect, itemDifficulty);
        
        // 限制θ的范围
        newTheta = Math.max(THETA_MIN, Math.min(THETA_MAX, newTheta));
        
        assessment.setIrtTheta(BigDecimal.valueOf(newTheta).setScale(4, RoundingMode.HALF_UP));
        
        // 更新总体能力分数（θ转换为0-100分）
        double abilityScore = (newTheta + 3) / 6 * 100;
        assessment.setOverallAbilityScore(BigDecimal.valueOf(abilityScore).setScale(2, RoundingMode.HALF_UP));
    }

    /**
     * 使用最大似然估计更新θ
     */
    private double updateThetaWithMLE(double currentTheta, boolean isCorrect, double itemDifficulty) {
        // 简化的MLE更新（实际应用中需要更复杂的算法）
        double learningRate = 0.1;
        double probability = calculateIRTProbability(currentTheta, itemDifficulty);
        
        double gradient = isCorrect ? (1 - probability) : -probability;
        return currentTheta + learningRate * gradient;
    }

    /**
     * 计算IRT概率
     */
    private double calculateIRTProbability(double theta, double difficulty) {
        return 1.0 / (1.0 + Math.exp(-(theta - difficulty)));
    }

    /**
     * 获取难度参数
     */
    private double getDifficultyParameter(String difficulty) {
        if (difficulty == null) return 0.0;
        
        switch (difficulty.toUpperCase()) {
            case "EASY": return -1.0;
            case "MEDIUM": return 0.0;
            case "HARD": return 1.0;
            case "VERY_HARD": return 2.0;
            default: return 0.0;
        }
    }

    /**
     * 其他辅助计算方法
     */
    private double calculateSpeedFactor(BigDecimal averageTime) {
        // 基于答题时间计算速度因子
        double timeSeconds = averageTime.doubleValue();
        if (timeSeconds <= 30) return 1.1;
        if (timeSeconds <= 60) return 1.0;
        if (timeSeconds <= 120) return 0.9;
        return 0.8;
    }

    private BigDecimal calculateLearningSpeed(AbilityAssessmentRequest request) {
        // TODO: 实现学习速度计算
        return BigDecimal.valueOf(5.0);
    }

    private BigDecimal calculateFocusScore(AbilityAssessmentRequest request) {
        // TODO: 实现专注度评分计算
        return BigDecimal.valueOf(7.0);
    }

    private BigDecimal calculatePersistenceScore(AbilityAssessmentRequest request) {
        // TODO: 实现坚持度评分计算
        return BigDecimal.valueOf(6.0);
    }

    private BigDecimal calculateEfficiencyScore(AbilityAssessmentRequest request) {
        // TODO: 实现效率评分计算
        return BigDecimal.valueOf(7.5);
    }

    private Difficulty determineRecommendedDifficulty(BigDecimal abilityScore) {
        double score = abilityScore.doubleValue();
        if (score >= 80) return Difficulty.HARD;
        if (score >= 60) return Difficulty.MEDIUM;
        return Difficulty.EASY;
    }

    private boolean needsAssessment(Long userId, String subjectCode, AssessmentType assessmentType) {
        UserAbilityAssessment latest = getLatestAssessment(userId, subjectCode);
        if (latest == null) return true;
        
        return latest.needsReassessment() || 
               assessmentType == AssessmentType.REAL_TIME_ASSESSMENT;
    }

    private UserAbilityAssessment getLatestAssessment(Long userId, String subjectCode) {
        return assessmentRepository.findTopByUserIdAndSubjectCodeOrderByAssessmentDateDesc(userId, subjectCode)
                .orElse(null);
    }

    private UserAbilityAssessment createInitialAssessment(Long userId, String subjectCode) {
        return UserAbilityAssessment.builder()
                .userId(userId)
                .subjectCode(subjectCode)
                .assessmentType(AssessmentType.INITIAL_ASSESSMENT)
                .assessmentDate(LocalDateTime.now())
                .overallAbilityScore(BigDecimal.valueOf(50))
                .irtTheta(BigDecimal.valueOf(DEFAULT_THETA))
                .currentDifficulty(Difficulty.EASY)
                .recommendedDifficulty(Difficulty.EASY)
                .confidenceLevel(BigDecimal.valueOf(0.5))
                .sampleSize(0)
                .algorithmVersion("1.0")
                .build();
    }

    private void updateAssessmentStatistics(UserAbilityAssessment assessment, boolean isCorrect, Integer timeSpent) {
        // 更新样本数量
        assessment.setSampleSize(assessment.getSampleSize() + 1);
        
        // 更新正确率（简化计算）
        if (assessment.getAccuracyRate() == null) {
            assessment.setAccuracyRate(BigDecimal.valueOf(isCorrect ? 1.0 : 0.0));
        } else {
            double currentRate = assessment.getAccuracyRate().doubleValue();
            int sampleSize = assessment.getSampleSize();
            double newRate = (currentRate * (sampleSize - 1) + (isCorrect ? 1.0 : 0.0)) / sampleSize;
            assessment.setAccuracyRate(BigDecimal.valueOf(newRate).setScale(4, RoundingMode.HALF_UP));
        }
        
        // 更新答题速度
        if (timeSpent != null) {
            if (assessment.getAnswerSpeed() == null) {
                assessment.setAnswerSpeed(BigDecimal.valueOf(timeSpent));
            } else {
                double currentSpeed = assessment.getAnswerSpeed().doubleValue();
                int sampleSize = assessment.getSampleSize();
                double newSpeed = (currentSpeed * (sampleSize - 1) + timeSpent) / sampleSize;
                assessment.setAnswerSpeed(BigDecimal.valueOf(newSpeed).setScale(2, RoundingMode.HALF_UP));
            }
        }
    }

    private void clearUserAbilityCache(Long userId, String subjectCode) {
        String cacheKey = USER_ABILITY_CACHE_KEY + userId + ":" + subjectCode;
        redisTemplate.delete(cacheKey);
    }

}
