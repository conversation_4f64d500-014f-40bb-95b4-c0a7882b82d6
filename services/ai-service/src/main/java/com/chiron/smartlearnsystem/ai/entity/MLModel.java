package com.chiron.smartlearnsystem.ai.entity;

import com.chiron.smartlearnsystem.ai.enums.ModelStatus;
import com.chiron.smartlearnsystem.ai.enums.ModelType;
import com.chiron.smartlearnsystem.common.database.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

/**
 * 机器学习模型实体
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Entity
@Table(name = "ml_models", indexes = {
    @Index(name = "idx_model_type", columnList = "model_type"),
    @Index(name = "idx_status", columnList = "status"),
    @Index(name = "idx_accuracy", columnList = "accuracy"),
    @Index(name = "idx_version", columnList = "version"),
    @Index(name = "idx_created_at", columnList = "created_at")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MLModel extends BaseEntity {

    /**
     * 模型名称
     */
    @Column(nullable = false, length = 100)
    private String name;

    /**
     * 模型描述
     */
    @Column(columnDefinition = "TEXT")
    private String description;

    /**
     * 模型类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "model_type", nullable = false, length = 30)
    private ModelType modelType;

    /**
     * 模型状态
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private ModelStatus status = ModelStatus.TRAINING;

    /**
     * 模型版本
     */
    @Column(nullable = false, length = 20)
    private String version;

    /**
     * 算法名称
     */
    @Column(name = "algorithm_name", nullable = false, length = 50)
    private String algorithmName;

    /**
     * 模型参数（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "model_parameters", columnDefinition = "JSON")
    private String modelParameters;

    /**
     * 超参数配置（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "hyperparameters", columnDefinition = "JSON")
    private String hyperparameters;

    /**
     * 训练数据集信息（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "training_dataset", columnDefinition = "JSON")
    private String trainingDataset;

    /**
     * 验证数据集信息（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "validation_dataset", columnDefinition = "JSON")
    private String validationDataset;

    /**
     * 测试数据集信息（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "test_dataset", columnDefinition = "JSON")
    private String testDataset;

    /**
     * 模型准确率
     */
    @Column(precision = 5, scale = 4)
    private BigDecimal accuracy;

    /**
     * 模型精确率
     */
    @Column(precision = 5, scale = 4)
    private BigDecimal precision;

    /**
     * 模型召回率
     */
    @Column(precision = 5, scale = 4)
    private BigDecimal recall;

    /**
     * F1分数
     */
    @Column(name = "f1_score", precision = 5, scale = 4)
    private BigDecimal f1Score;

    /**
     * AUC分数
     */
    @Column(name = "auc_score", precision = 5, scale = 4)
    private BigDecimal aucScore;

    /**
     * 损失值
     */
    @Column(name = "loss_value", precision = 8, scale = 6)
    private BigDecimal lossValue;

    /**
     * 训练开始时间
     */
    @Column(name = "training_start_time")
    private LocalDateTime trainingStartTime;

    /**
     * 训练结束时间
     */
    @Column(name = "training_end_time")
    private LocalDateTime trainingEndTime;

    /**
     * 训练轮数
     */
    @Column(name = "training_epochs")
    private Integer trainingEpochs;

    /**
     * 训练样本数量
     */
    @Column(name = "training_samples")
    private Long trainingSamples;

    /**
     * 模型大小（字节）
     */
    @Column(name = "model_size")
    private Long modelSize;

    /**
     * 模型文件路径
     */
    @Column(name = "model_file_path", length = 500)
    private String modelFilePath;

    /**
     * 模型配置文件路径
     */
    @Column(name = "config_file_path", length = 500)
    private String configFilePath;

    /**
     * 特征重要性（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "feature_importance", columnDefinition = "JSON")
    private String featureImportance;

    /**
     * 混淆矩阵（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "confusion_matrix", columnDefinition = "JSON")
    private String confusionMatrix;

    /**
     * 学习曲线数据（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "learning_curve", columnDefinition = "JSON")
    private String learningCurve;

    /**
     * 适用科目
     */
    @ElementCollection
    @CollectionTable(
        name = "ml_model_subjects",
        joinColumns = @JoinColumn(name = "model_id")
    )
    @Column(name = "subject_code")
    private Set<String> applicableSubjects = new HashSet<>();

    /**
     * 模型标签
     */
    @ElementCollection
    @CollectionTable(
        name = "ml_model_tags",
        joinColumns = @JoinColumn(name = "model_id")
    )
    @Column(name = "tag")
    private Set<String> tags = new HashSet<>();

    /**
     * 部署环境
     */
    @Column(name = "deployment_environment", length = 50)
    private String deploymentEnvironment;

    /**
     * API端点
     */
    @Column(name = "api_endpoint", length = 200)
    private String apiEndpoint;

    /**
     * 预测次数
     */
    @Column(name = "prediction_count", nullable = false)
    private Long predictionCount = 0L;

    /**
     * 最后预测时间
     */
    @Column(name = "last_prediction_time")
    private LocalDateTime lastPredictionTime;

    /**
     * 模型创建者
     */
    @Column(name = "created_by")
    private Long createdBy;

    /**
     * 模型备注
     */
    @Column(columnDefinition = "TEXT")
    private String notes;

    /**
     * 检查模型是否可用
     */
    public boolean isAvailable() {
        return status == ModelStatus.DEPLOYED || status == ModelStatus.ACTIVE;
    }

    /**
     * 检查模型是否为高质量
     */
    public boolean isHighQuality() {
        return accuracy != null && accuracy.compareTo(BigDecimal.valueOf(0.85)) >= 0;
    }

    /**
     * 增加预测次数
     */
    public void incrementPredictionCount() {
        this.predictionCount++;
        this.lastPredictionTime = LocalDateTime.now();
    }

    /**
     * 计算训练时长（分钟）
     */
    public Long getTrainingDurationMinutes() {
        if (trainingStartTime != null && trainingEndTime != null) {
            return java.time.Duration.between(trainingStartTime, trainingEndTime).toMinutes();
        }
        return null;
    }

    /**
     * 检查是否需要重新训练
     */
    public boolean needsRetraining() {
        // 如果准确率低于阈值或者很久没有更新
        if (accuracy != null && accuracy.compareTo(BigDecimal.valueOf(0.7)) < 0) {
            return true;
        }
        
        // 如果超过30天没有更新
        return getUpdatedAt().isBefore(LocalDateTime.now().minusDays(30));
    }

    /**
     * 添加适用科目
     */
    public void addApplicableSubject(String subjectCode) {
        this.applicableSubjects.add(subjectCode);
    }

    /**
     * 添加标签
     */
    public void addTag(String tag) {
        this.tags.add(tag);
    }

    /**
     * 开始训练
     */
    public void startTraining() {
        this.status = ModelStatus.TRAINING;
        this.trainingStartTime = LocalDateTime.now();
    }

    /**
     * 完成训练
     */
    public void completeTraining() {
        this.status = ModelStatus.TRAINED;
        this.trainingEndTime = LocalDateTime.now();
    }

    /**
     * 部署模型
     */
    public void deploy() {
        this.status = ModelStatus.DEPLOYED;
    }

    /**
     * 激活模型
     */
    public void activate() {
        this.status = ModelStatus.ACTIVE;
    }

}
