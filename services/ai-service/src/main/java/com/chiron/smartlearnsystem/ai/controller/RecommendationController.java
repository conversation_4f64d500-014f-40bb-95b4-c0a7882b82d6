package com.chiron.smartlearnsystem.ai.controller;

import com.chiron.smartlearnsystem.ai.dto.LearningRecommendationDTO;
import com.chiron.smartlearnsystem.ai.dto.request.RecommendationRequest;
import com.chiron.smartlearnsystem.ai.service.RecommendationService;
import com.chiron.smartlearnsystem.common.core.domain.ApiResponse;
import com.chiron.smartlearnsystem.common.core.domain.PageResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.util.List;

/**
 * 推荐控制器
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/ai/recommendations")
@RequiredArgsConstructor
@Validated
public class RecommendationController {

    private final RecommendationService recommendationService;

    /**
     * 生成学习推荐
     */
    @PostMapping("/generate")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<LearningRecommendationDTO>>> generateRecommendations(
            @Valid @RequestBody RecommendationRequest request) {
        
        log.info("生成学习推荐请求: {}", request);
        
        List<LearningRecommendationDTO> recommendations = 
                recommendationService.generateRecommendations(request);
        
        return ResponseEntity.ok(ApiResponse.success("推荐生成成功", recommendations));
    }

    /**
     * 获取用户推荐列表
     */
    @GetMapping("/user/{userId}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<PageResult<LearningRecommendationDTO>>> getUserRecommendations(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "1") @Min(1) Integer page,
            @RequestParam(defaultValue = "20") @Min(1) @Max(100) Integer size) {
        
        log.info("获取用户推荐列表: userId={}", userId);
        
        PageResult<LearningRecommendationDTO> result = 
                recommendationService.getUserRecommendations(userId, page, size);
        
        return ResponseEntity.ok(ApiResponse.success("获取推荐列表成功", result));
    }

    /**
     * 接受推荐
     */
    @PostMapping("/{recommendationId}/accept")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Void>> acceptRecommendation(
            @PathVariable Long recommendationId,
            @RequestParam Long userId) {
        
        log.info("接受推荐: recommendationId={}, userId={}", recommendationId, userId);
        
        recommendationService.acceptRecommendation(recommendationId, userId);
        
        return ResponseEntity.ok(ApiResponse.<Void>success("推荐已接受", null));
    }

    /**
     * 拒绝推荐
     */
    @PostMapping("/{recommendationId}/reject")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Void>> rejectRecommendation(
            @PathVariable Long recommendationId,
            @RequestParam Long userId,
            @RequestParam(required = false) String feedback) {
        
        log.info("拒绝推荐: recommendationId={}, userId={}", recommendationId, userId);
        
        recommendationService.rejectRecommendation(recommendationId, userId, feedback);
        
        return ResponseEntity.ok(ApiResponse.<Void>success("推荐已拒绝", null));
    }

    /**
     * 标记推荐为已查看
     */
    @PostMapping("/{recommendationId}/view")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Void>> markRecommendationAsViewed(
            @PathVariable Long recommendationId,
            @RequestParam Long userId) {
        
        log.debug("标记推荐已查看: recommendationId={}, userId={}", recommendationId, userId);
        
        recommendationService.markRecommendationAsViewed(recommendationId, userId);
        
        return ResponseEntity.ok(ApiResponse.<Void>success("已标记为查看", null));
    }

}
