package com.chiron.smartlearnsystem.ai.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 评估类型枚举
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum AssessmentType {

    /**
     * 初始能力评估
     */
    INITIAL_ASSESSMENT("INITIAL_ASSESSMENT", "初始评估", "用户首次使用系统时的能力评估"),

    /**
     * 定期能力评估
     */
    PERIODIC_ASSESSMENT("PERIODIC_ASSESSMENT", "定期评估", "定期进行的能力水平评估"),

    /**
     * 实时能力评估
     */
    REAL_TIME_ASSESSMENT("REAL_TIME_ASSESSMENT", "实时评估", "基于用户答题实时更新的能力评估"),

    /**
     * 阶段性评估
     */
    MILESTONE_ASSESSMENT("MILESTONE_ASSESSMENT", "阶段评估", "学习阶段完成后的能力评估"),

    /**
     * 考前评估
     */
    PRE_EXAM_ASSESSMENT("PRE_EXAM_ASSESSMENT", "考前评估", "考试前的能力水平评估"),

    /**
     * 考后评估
     */
    POST_EXAM_ASSESSMENT("POST_EXAM_ASSESSMENT", "考后评估", "考试后的能力变化评估"),

    /**
     * 专项能力评估
     */
    SPECIALIZED_ASSESSMENT("SPECIALIZED_ASSESSMENT", "专项评估", "针对特定知识点或技能的评估"),

    /**
     * 综合能力评估
     */
    COMPREHENSIVE_ASSESSMENT("COMPREHENSIVE_ASSESSMENT", "综合评估", "全面的综合能力评估"),

    /**
     * 自适应评估
     */
    ADAPTIVE_ASSESSMENT("ADAPTIVE_ASSESSMENT", "自适应评估", "根据答题情况动态调整的评估"),

    /**
     * 诊断性评估
     */
    DIAGNOSTIC_ASSESSMENT("DIAGNOSTIC_ASSESSMENT", "诊断评估", "诊断学习问题和薄弱点的评估");

    private final String code;
    private final String name;
    private final String description;

    /**
     * 根据代码获取评估类型
     */
    public static AssessmentType getByCode(String code) {
        for (AssessmentType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return REAL_TIME_ASSESSMENT; // 默认返回实时评估
    }

    /**
     * 是否为主动评估
     */
    public boolean isActiveAssessment() {
        return this == INITIAL_ASSESSMENT || this == PERIODIC_ASSESSMENT || 
               this == MILESTONE_ASSESSMENT || this == PRE_EXAM_ASSESSMENT || 
               this == COMPREHENSIVE_ASSESSMENT;
    }

    /**
     * 是否为被动评估
     */
    public boolean isPassiveAssessment() {
        return this == REAL_TIME_ASSESSMENT || this == POST_EXAM_ASSESSMENT;
    }

    /**
     * 是否为专项评估
     */
    public boolean isSpecializedAssessment() {
        return this == SPECIALIZED_ASSESSMENT || this == DIAGNOSTIC_ASSESSMENT;
    }

    /**
     * 是否为自适应评估
     */
    public boolean isAdaptiveAssessment() {
        return this == ADAPTIVE_ASSESSMENT || this == REAL_TIME_ASSESSMENT;
    }

    /**
     * 获取评估频率（天）
     */
    public int getAssessmentFrequency() {
        switch (this) {
            case REAL_TIME_ASSESSMENT:
                return 0; // 实时评估
            case DIAGNOSTIC_ASSESSMENT:
                return 1; // 每天可进行诊断评估
            case SPECIALIZED_ASSESSMENT:
                return 3; // 专项评估3天一次
            case PERIODIC_ASSESSMENT:
                return 7; // 定期评估1周一次
            case MILESTONE_ASSESSMENT:
                return 14; // 阶段评估2周一次
            case PRE_EXAM_ASSESSMENT:
            case POST_EXAM_ASSESSMENT:
                return 30; // 考试相关评估1月一次
            case COMPREHENSIVE_ASSESSMENT:
                return 30; // 综合评估1月一次
            case ADAPTIVE_ASSESSMENT:
                return 7; // 自适应评估1周一次
            case INITIAL_ASSESSMENT:
                return -1; // 仅一次
            default:
                return 7; // 默认1周一次
        }
    }

    /**
     * 获取评估所需最小样本数
     */
    public int getMinimumSampleSize() {
        switch (this) {
            case INITIAL_ASSESSMENT:
                return 10; // 初始评估至少10题
            case REAL_TIME_ASSESSMENT:
                return 1; // 实时评估每题更新
            case SPECIALIZED_ASSESSMENT:
                return 5; // 专项评估至少5题
            case DIAGNOSTIC_ASSESSMENT:
                return 8; // 诊断评估至少8题
            case PERIODIC_ASSESSMENT:
                return 15; // 定期评估至少15题
            case MILESTONE_ASSESSMENT:
                return 20; // 阶段评估至少20题
            case PRE_EXAM_ASSESSMENT:
            case POST_EXAM_ASSESSMENT:
                return 25; // 考试评估至少25题
            case COMPREHENSIVE_ASSESSMENT:
                return 50; // 综合评估至少50题
            case ADAPTIVE_ASSESSMENT:
                return 10; // 自适应评估至少10题
            default:
                return 10; // 默认10题
        }
    }

    /**
     * 获取评估权重
     */
    public double getAssessmentWeight() {
        switch (this) {
            case COMPREHENSIVE_ASSESSMENT:
                return 1.0; // 综合评估权重最高
            case MILESTONE_ASSESSMENT:
                return 0.9; // 阶段评估权重很高
            case POST_EXAM_ASSESSMENT:
                return 0.8; // 考后评估权重高
            case PERIODIC_ASSESSMENT:
                return 0.7; // 定期评估权重较高
            case PRE_EXAM_ASSESSMENT:
                return 0.6; // 考前评估权重中等
            case ADAPTIVE_ASSESSMENT:
                return 0.5; // 自适应评估权重中等
            case SPECIALIZED_ASSESSMENT:
                return 0.4; // 专项评估权重较低
            case DIAGNOSTIC_ASSESSMENT:
                return 0.4; // 诊断评估权重较低
            case REAL_TIME_ASSESSMENT:
                return 0.3; // 实时评估权重低
            case INITIAL_ASSESSMENT:
                return 0.8; // 初始评估权重高
            default:
                return 0.5; // 默认权重
        }
    }

}
