package com.chiron.smartlearnsystem.ai.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 机器学习模型状态枚举
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum ModelStatus {

    /**
     * 训练中
     */
    TRAINING("TRAINING", "训练中", "模型正在训练"),

    /**
     * 已训练
     */
    TRAINED("TRAINED", "已训练", "模型训练完成"),

    /**
     * 验证中
     */
    VALIDATING("VALIDATING", "验证中", "模型正在验证"),

    /**
     * 已验证
     */
    VALIDATED("VALIDATED", "已验证", "模型验证通过"),

    /**
     * 测试中
     */
    TESTING("TESTING", "测试中", "模型正在测试"),

    /**
     * 已测试
     */
    TESTED("TESTED", "已测试", "模型测试完成"),

    /**
     * 部署中
     */
    DEPLOYING("DEPLOYING", "部署中", "模型正在部署"),

    /**
     * 已部署
     */
    DEPLOYED("DEPLOYED", "已部署", "模型已部署到生产环境"),

    /**
     * 激活状态
     */
    ACTIVE("ACTIVE", "激活", "模型正在生产环境中使用"),

    /**
     * 暂停状态
     */
    PAUSED("PAUSED", "暂停", "模型暂时停用"),

    /**
     * 已停用
     */
    INACTIVE("INACTIVE", "已停用", "模型已停用"),

    /**
     * 已废弃
     */
    DEPRECATED("DEPRECATED", "已废弃", "模型已被新版本替代"),

    /**
     * 训练失败
     */
    TRAINING_FAILED("TRAINING_FAILED", "训练失败", "模型训练失败"),

    /**
     * 验证失败
     */
    VALIDATION_FAILED("VALIDATION_FAILED", "验证失败", "模型验证失败"),

    /**
     * 部署失败
     */
    DEPLOYMENT_FAILED("DEPLOYMENT_FAILED", "部署失败", "模型部署失败"),

    /**
     * 错误状态
     */
    ERROR("ERROR", "错误", "模型出现错误");

    private final String code;
    private final String name;
    private final String description;

    /**
     * 根据代码获取模型状态
     */
    public static ModelStatus getByCode(String code) {
        for (ModelStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return TRAINING; // 默认返回训练中
    }

    /**
     * 是否为进行中状态
     */
    public boolean isInProgress() {
        return this == TRAINING || this == VALIDATING || this == TESTING || this == DEPLOYING;
    }

    /**
     * 是否为成功状态
     */
    public boolean isSuccessful() {
        return this == TRAINED || this == VALIDATED || this == TESTED || 
               this == DEPLOYED || this == ACTIVE;
    }

    /**
     * 是否为失败状态
     */
    public boolean isFailed() {
        return this == TRAINING_FAILED || this == VALIDATION_FAILED || 
               this == DEPLOYMENT_FAILED || this == ERROR;
    }

    /**
     * 是否为最终状态
     */
    public boolean isFinalStatus() {
        return this == ACTIVE || this == DEPRECATED || this == INACTIVE || isFailed();
    }

    /**
     * 是否可用于预测
     */
    public boolean isAvailableForPrediction() {
        return this == DEPLOYED || this == ACTIVE;
    }

    /**
     * 是否可以部署
     */
    public boolean canDeploy() {
        return this == TESTED || this == VALIDATED;
    }

    /**
     * 是否可以激活
     */
    public boolean canActivate() {
        return this == DEPLOYED;
    }

    /**
     * 是否可以暂停
     */
    public boolean canPause() {
        return this == ACTIVE;
    }

    /**
     * 是否可以恢复
     */
    public boolean canResume() {
        return this == PAUSED;
    }

    /**
     * 是否可以停用
     */
    public boolean canDeactivate() {
        return this == ACTIVE || this == PAUSED;
    }

    /**
     * 是否可以废弃
     */
    public boolean canDeprecate() {
        return this == ACTIVE || this == INACTIVE || this == PAUSED;
    }

    /**
     * 是否需要重新训练
     */
    public boolean needsRetraining() {
        return isFailed() || this == DEPRECATED;
    }

}
