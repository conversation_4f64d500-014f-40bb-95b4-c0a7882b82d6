package com.chiron.smartlearnsystem.ai.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 机器学习模型类型枚举
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum ModelType {

    /**
     * IRT模型（项目反应理论）
     */
    IRT_MODEL("IRT_MODEL", "IRT模型", "基于项目反应理论的能力评估模型"),

    /**
     * 协同过滤推荐模型
     */
    COLLABORATIVE_FILTERING("COLLABORATIVE_FILTERING", "协同过滤", "基于用户行为的协同过滤推荐模型"),

    /**
     * 内容推荐模型
     */
    CONTENT_BASED_FILTERING("CONTENT_BASED_FILTERING", "内容推荐", "基于内容特征的推荐模型"),

    /**
     * 混合推荐模型
     */
    HYBRID_RECOMMENDATION("HYBRID_RECOMMENDATION", "混合推荐", "结合多种推荐算法的混合模型"),

    /**
     * 知识追踪模型
     */
    KNOWLEDGE_TRACING("KNOWLEDGE_TRACING", "知识追踪", "追踪学习者知识状态变化的模型"),

    /**
     * 深度知识追踪模型
     */
    DEEP_KNOWLEDGE_TRACING("DEEP_KNOWLEDGE_TRACING", "深度知识追踪", "基于深度学习的知识追踪模型"),

    /**
     * 学习路径推荐模型
     */
    LEARNING_PATH_RECOMMENDATION("LEARNING_PATH_RECOMMENDATION", "学习路径推荐", "推荐最优学习路径的模型"),

    /**
     * 难度预测模型
     */
    DIFFICULTY_PREDICTION("DIFFICULTY_PREDICTION", "难度预测", "预测题目难度的模型"),

    /**
     * 学习效果预测模型
     */
    LEARNING_OUTCOME_PREDICTION("LEARNING_OUTCOME_PREDICTION", "学习效果预测", "预测学习效果的模型"),

    /**
     * 遗忘曲线模型
     */
    FORGETTING_CURVE("FORGETTING_CURVE", "遗忘曲线", "基于遗忘曲线的复习时间预测模型"),

    /**
     * 学习风格识别模型
     */
    LEARNING_STYLE_RECOGNITION("LEARNING_STYLE_RECOGNITION", "学习风格识别", "识别用户学习风格的模型"),

    /**
     * 情感分析模型
     */
    SENTIMENT_ANALYSIS("SENTIMENT_ANALYSIS", "情感分析", "分析学习者情感状态的模型"),

    /**
     * 注意力预测模型
     */
    ATTENTION_PREDICTION("ATTENTION_PREDICTION", "注意力预测", "预测学习者注意力状态的模型"),

    /**
     * 自然语言处理模型
     */
    NLP_MODEL("NLP_MODEL", "自然语言处理", "处理文本内容的NLP模型"),

    /**
     * 聚类分析模型
     */
    CLUSTERING_MODEL("CLUSTERING_MODEL", "聚类分析", "用户或内容聚类分析模型"),

    /**
     * 异常检测模型
     */
    ANOMALY_DETECTION("ANOMALY_DETECTION", "异常检测", "检测异常学习行为的模型");

    private final String code;
    private final String name;
    private final String description;

    /**
     * 根据代码获取模型类型
     */
    public static ModelType getByCode(String code) {
        for (ModelType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return IRT_MODEL; // 默认返回IRT模型
    }

    /**
     * 是否为推荐模型
     */
    public boolean isRecommendationModel() {
        return this == COLLABORATIVE_FILTERING || this == CONTENT_BASED_FILTERING || 
               this == HYBRID_RECOMMENDATION || this == LEARNING_PATH_RECOMMENDATION;
    }

    /**
     * 是否为评估模型
     */
    public boolean isAssessmentModel() {
        return this == IRT_MODEL || this == KNOWLEDGE_TRACING || 
               this == DEEP_KNOWLEDGE_TRACING || this == LEARNING_STYLE_RECOGNITION;
    }

    /**
     * 是否为预测模型
     */
    public boolean isPredictionModel() {
        return this == DIFFICULTY_PREDICTION || this == LEARNING_OUTCOME_PREDICTION || 
               this == ATTENTION_PREDICTION || this == FORGETTING_CURVE;
    }

    /**
     * 是否为分析模型
     */
    public boolean isAnalysisModel() {
        return this == SENTIMENT_ANALYSIS || this == CLUSTERING_MODEL || 
               this == ANOMALY_DETECTION || this == NLP_MODEL;
    }

    /**
     * 获取模型复杂度等级（1-5）
     */
    public int getComplexityLevel() {
        switch (this) {
            case DEEP_KNOWLEDGE_TRACING:
            case NLP_MODEL:
                return 5; // 最高复杂度
            case HYBRID_RECOMMENDATION:
            case LEARNING_OUTCOME_PREDICTION:
            case SENTIMENT_ANALYSIS:
                return 4; // 高复杂度
            case IRT_MODEL:
            case KNOWLEDGE_TRACING:
            case LEARNING_PATH_RECOMMENDATION:
            case ATTENTION_PREDICTION:
                return 3; // 中等复杂度
            case COLLABORATIVE_FILTERING:
            case CONTENT_BASED_FILTERING:
            case DIFFICULTY_PREDICTION:
            case LEARNING_STYLE_RECOGNITION:
            case CLUSTERING_MODEL:
                return 2; // 较低复杂度
            case FORGETTING_CURVE:
            case ANOMALY_DETECTION:
                return 1; // 最低复杂度
            default:
                return 3; // 默认中等复杂度
        }
    }

    /**
     * 获取推荐训练样本数量
     */
    public int getRecommendedSampleSize() {
        switch (this) {
            case DEEP_KNOWLEDGE_TRACING:
            case NLP_MODEL:
                return 100000; // 深度学习模型需要大量样本
            case HYBRID_RECOMMENDATION:
            case SENTIMENT_ANALYSIS:
                return 50000; // 复杂模型需要较多样本
            case IRT_MODEL:
            case KNOWLEDGE_TRACING:
            case LEARNING_OUTCOME_PREDICTION:
                return 20000; // 中等样本量
            case COLLABORATIVE_FILTERING:
            case CONTENT_BASED_FILTERING:
            case LEARNING_PATH_RECOMMENDATION:
                return 10000; // 较少样本量
            case DIFFICULTY_PREDICTION:
            case LEARNING_STYLE_RECOGNITION:
            case ATTENTION_PREDICTION:
            case CLUSTERING_MODEL:
                return 5000; // 少量样本
            case FORGETTING_CURVE:
            case ANOMALY_DETECTION:
                return 1000; // 最少样本
            default:
                return 10000; // 默认样本量
        }
    }

    /**
     * 获取模型更新频率（天）
     */
    public int getUpdateFrequency() {
        switch (this) {
            case IRT_MODEL:
            case KNOWLEDGE_TRACING:
            case DEEP_KNOWLEDGE_TRACING:
                return 1; // 每天更新
            case COLLABORATIVE_FILTERING:
            case HYBRID_RECOMMENDATION:
            case LEARNING_OUTCOME_PREDICTION:
                return 3; // 3天更新
            case CONTENT_BASED_FILTERING:
            case DIFFICULTY_PREDICTION:
            case ATTENTION_PREDICTION:
                return 7; // 1周更新
            case LEARNING_PATH_RECOMMENDATION:
            case SENTIMENT_ANALYSIS:
            case CLUSTERING_MODEL:
                return 14; // 2周更新
            case LEARNING_STYLE_RECOGNITION:
            case FORGETTING_CURVE:
            case ANOMALY_DETECTION:
            case NLP_MODEL:
                return 30; // 1月更新
            default:
                return 7; // 默认1周更新
        }
    }

}
