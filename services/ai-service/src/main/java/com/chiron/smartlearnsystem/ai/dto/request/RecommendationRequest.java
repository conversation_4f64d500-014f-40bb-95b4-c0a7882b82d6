package com.chiron.smartlearnsystem.ai.dto.request;

import com.chiron.smartlearnsystem.ai.enums.RecommendationType;
import com.chiron.smartlearnsystem.common.core.enums.Difficulty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Set;

/**
 * 推荐请求
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RecommendationRequest {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 推荐类型
     */
    @NotNull(message = "推荐类型不能为空")
    private RecommendationType recommendationType;

    /**
     * 科目代码
     */
    @Size(max = 20, message = "科目代码长度不能超过20个字符")
    private String subjectCode;

    /**
     * 目标难度
     */
    private Difficulty targetDifficulty;

    /**
     * 推荐数量
     */
    @Min(value = 1, message = "推荐数量不能少于1")
    @Max(value = 20, message = "推荐数量不能超过20")
    private Integer recommendationCount = 5;

    /**
     * 目标知识点
     */
    @Size(max = 10, message = "目标知识点数量不能超过10个")
    private Set<String> targetKnowledgePoints;

    /**
     * 学习时长限制（分钟）
     */
    @Min(value = 5, message = "学习时长不能少于5分钟")
    @Max(value = 480, message = "学习时长不能超过8小时")
    private Integer timeLimitMinutes;

    /**
     * 题目数量限制
     */
    @Min(value = 1, message = "题目数量不能少于1")
    @Max(value = 100, message = "题目数量不能超过100")
    private Integer questionLimit;

    /**
     * 是否包含已做过的题目
     */
    private Boolean includeAnswered = false;

    /**
     * 最小置信度要求
     */
    @Min(value = 0, message = "置信度不能小于0")
    @Max(value = 1, message = "置信度不能大于1")
    private Double minConfidence = 0.5;

    /**
     * 个性化参数
     */
    private String personalizationParams;

    /**
     * 上下文信息
     */
    private String contextInfo;

}
