package com.chiron.smartlearnsystem.ai.repository;

import com.chiron.smartlearnsystem.ai.entity.LearningRecommendation;
import com.chiron.smartlearnsystem.ai.enums.RecommendationStatus;
import com.chiron.smartlearnsystem.ai.enums.RecommendationType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 学习推荐数据访问层
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Repository
public interface LearningRecommendationRepository extends JpaRepository<LearningRecommendation, Long>, 
                                                        JpaSpecificationExecutor<LearningRecommendation> {

    /**
     * 根据用户ID查找推荐
     */
    List<LearningRecommendation> findByUserId(Long userId);

    /**
     * 根据用户ID和推荐类型查找推荐
     */
    List<LearningRecommendation> findByUserIdAndRecommendationType(Long userId, RecommendationType recommendationType);

    /**
     * 根据用户ID和状态查找推荐
     */
    List<LearningRecommendation> findByUserIdAndStatus(Long userId, RecommendationStatus status);

    /**
     * 查找用户活跃的推荐
     */
    @Query("SELECT lr FROM LearningRecommendation lr WHERE lr.userId = :userId AND lr.status = 'ACTIVE' " +
           "AND (lr.expiresAt IS NULL OR lr.expiresAt > :now) ORDER BY lr.priority DESC, lr.confidenceScore DESC")
    Page<LearningRecommendation> findActiveRecommendationsByUser(@Param("userId") Long userId, 
                                                               @Param("now") LocalDateTime now, 
                                                               Pageable pageable);

    /**
     * 查找用户活跃的推荐（重载方法）
     */
    default Page<LearningRecommendation> findActiveRecommendationsByUser(Long userId, Pageable pageable) {
        return findActiveRecommendationsByUser(userId, LocalDateTime.now(), pageable);
    }

    /**
     * 根据推荐类型和科目查找推荐
     */
    List<LearningRecommendation> findByRecommendationTypeAndSubjectCode(RecommendationType recommendationType, 
                                                                       String subjectCode);

    /**
     * 查找高优先级推荐
     */
    @Query("SELECT lr FROM LearningRecommendation lr WHERE lr.userId = :userId AND lr.priority >= :minPriority " +
           "AND lr.status = 'ACTIVE' ORDER BY lr.priority DESC")
    List<LearningRecommendation> findHighPriorityRecommendations(@Param("userId") Long userId, 
                                                                @Param("minPriority") Integer minPriority);

    /**
     * 查找高置信度推荐
     */
    @Query("SELECT lr FROM LearningRecommendation lr WHERE lr.userId = :userId " +
           "AND lr.confidenceScore >= :minConfidence AND lr.status = 'ACTIVE' " +
           "ORDER BY lr.confidenceScore DESC")
    List<LearningRecommendation> findHighConfidenceRecommendations(@Param("userId") Long userId, 
                                                                  @Param("minConfidence") BigDecimal minConfidence);

    /**
     * 查找已过期的推荐
     */
    @Query("SELECT lr FROM LearningRecommendation lr WHERE lr.expiresAt < :now AND lr.status != 'EXPIRED'")
    List<LearningRecommendation> findExpiredRecommendations(@Param("now") LocalDateTime now);

    /**
     * 查找未查看的推荐
     */
    @Query("SELECT lr FROM LearningRecommendation lr WHERE lr.userId = :userId AND lr.isViewed = false " +
           "AND lr.status = 'ACTIVE' ORDER BY lr.createdAt DESC")
    List<LearningRecommendation> findUnviewedRecommendations(@Param("userId") Long userId);

    /**
     * 查找已采纳的推荐
     */
    List<LearningRecommendation> findByUserIdAndIsAcceptedTrue(Long userId);

    /**
     * 统计用户各类型推荐数量
     */
    @Query("SELECT lr.recommendationType, COUNT(lr) FROM LearningRecommendation lr " +
           "WHERE lr.userId = :userId GROUP BY lr.recommendationType")
    List<Object[]> countRecommendationsByType(@Param("userId") Long userId);

    /**
     * 统计用户各状态推荐数量
     */
    @Query("SELECT lr.status, COUNT(lr) FROM LearningRecommendation lr " +
           "WHERE lr.userId = :userId GROUP BY lr.status")
    List<Object[]> countRecommendationsByStatus(@Param("userId") Long userId);

    /**
     * 获取用户推荐统计
     */
    @Query("SELECT " +
           "COUNT(lr) as totalCount, " +
           "SUM(CASE WHEN lr.isViewed = true THEN 1 ELSE 0 END) as viewedCount, " +
           "SUM(CASE WHEN lr.isAccepted = true THEN 1 ELSE 0 END) as acceptedCount, " +
           "AVG(lr.confidenceScore) as avgConfidence " +
           "FROM LearningRecommendation lr WHERE lr.userId = :userId")
    Object[] getUserRecommendationStatistics(@Param("userId") Long userId);

    /**
     * 批量更新推荐状态
     */
    @Modifying
    @Query("UPDATE LearningRecommendation lr SET lr.status = :status WHERE lr.id IN :recommendationIds")
    int batchUpdateStatus(@Param("recommendationIds") List<Long> recommendationIds, 
                         @Param("status") RecommendationStatus status);

    /**
     * 批量标记为已过期
     */
    @Modifying
    @Query("UPDATE LearningRecommendation lr SET lr.status = 'EXPIRED' WHERE lr.expiresAt < :now " +
           "AND lr.status != 'EXPIRED'")
    int markExpiredRecommendations(@Param("now") LocalDateTime now);

    /**
     * 删除过期的推荐
     */
    @Modifying
    @Query("DELETE FROM LearningRecommendation lr WHERE lr.expiresAt < :cutoffTime AND lr.status = 'EXPIRED'")
    int deleteExpiredRecommendations(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 查找相似推荐
     */
    @Query("SELECT lr FROM LearningRecommendation lr WHERE lr.userId = :userId " +
           "AND lr.recommendationType = :type AND lr.subjectCode = :subjectCode " +
           "AND lr.status = 'ACTIVE' AND lr.id != :excludeId")
    List<LearningRecommendation> findSimilarRecommendations(@Param("userId") Long userId,
                                                           @Param("type") RecommendationType type,
                                                           @Param("subjectCode") String subjectCode,
                                                           @Param("excludeId") Long excludeId);

    /**
     * 查找用户反馈评分高的推荐
     */
    @Query("SELECT lr FROM LearningRecommendation lr WHERE lr.userId = :userId " +
           "AND lr.userFeedbackScore >= :minScore ORDER BY lr.userFeedbackScore DESC")
    List<LearningRecommendation> findHighRatedRecommendations(@Param("userId") Long userId, 
                                                             @Param("minScore") Integer minScore);

    /**
     * 查找特定算法版本的推荐
     */
    List<LearningRecommendation> findByAlgorithmVersion(String algorithmVersion);

    /**
     * 查找需要反馈的推荐
     */
    @Query("SELECT lr FROM LearningRecommendation lr WHERE lr.userId = :userId " +
           "AND (lr.status = 'COMPLETED' OR lr.status = 'REJECTED') " +
           "AND lr.userFeedbackScore IS NULL")
    List<LearningRecommendation> findRecommendationsNeedingFeedback(@Param("userId") Long userId);

    /**
     * 根据知识点查找推荐
     */
    @Query("SELECT lr FROM LearningRecommendation lr JOIN lr.targetKnowledgePoints kp " +
           "WHERE lr.userId = :userId AND kp = :knowledgePoint AND lr.status = 'ACTIVE'")
    List<LearningRecommendation> findByKnowledgePoint(@Param("userId") Long userId, 
                                                     @Param("knowledgePoint") String knowledgePoint);

    /**
     * 根据标签查找推荐
     */
    @Query("SELECT lr FROM LearningRecommendation lr JOIN lr.tags t " +
           "WHERE lr.userId = :userId AND t = :tag AND lr.status = 'ACTIVE'")
    List<LearningRecommendation> findByTag(@Param("userId") Long userId, @Param("tag") String tag);

    /**
     * 查找最近的推荐
     */
    @Query("SELECT lr FROM LearningRecommendation lr WHERE lr.userId = :userId " +
           "ORDER BY lr.createdAt DESC")
    List<LearningRecommendation> findRecentRecommendations(@Param("userId") Long userId, Pageable pageable);

}
