package com.chiron.smartlearnsystem.ai.service;

import com.chiron.smartlearnsystem.ai.dto.LearningRecommendationDTO;
import com.chiron.smartlearnsystem.ai.dto.request.RecommendationRequest;
import com.chiron.smartlearnsystem.ai.entity.LearningRecommendation;
import com.chiron.smartlearnsystem.ai.enums.RecommendationStatus;
import com.chiron.smartlearnsystem.ai.enums.RecommendationType;
import com.chiron.smartlearnsystem.ai.repository.LearningRecommendationRepository;
import com.chiron.smartlearnsystem.common.core.domain.PageResult;
import com.chiron.smartlearnsystem.common.core.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 推荐服务
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class RecommendationService {

    private final LearningRecommendationRepository recommendationRepository;
    private final RedisTemplate<String, Object> redisTemplate;
    private final AbilityAssessmentService abilityAssessmentService;
    private final MLModelService mlModelService;

    private static final String RECOMMENDATION_CACHE_KEY = "ai:recommendation:";
    private static final String USER_RECOMMENDATIONS_CACHE_KEY = "ai:user:recommendations:";
    private static final int CACHE_EXPIRE_HOURS = 1;

    /**
     * 生成学习推荐
     */
    public List<LearningRecommendationDTO> generateRecommendations(RecommendationRequest request) {
        log.info("生成学习推荐: userId={}, type={}", request.getUserId(), request.getRecommendationType());

        // 检查缓存
        String cacheKey = USER_RECOMMENDATIONS_CACHE_KEY + request.getUserId() + ":" + request.getRecommendationType();
        List<LearningRecommendationDTO> cachedRecommendations = 
                (List<LearningRecommendationDTO>) redisTemplate.opsForValue().get(cacheKey);
        if (cachedRecommendations != null && !cachedRecommendations.isEmpty()) {
            return cachedRecommendations;
        }

        // 根据推荐类型生成推荐
        List<LearningRecommendation> recommendations;
        switch (request.getRecommendationType()) {
            case QUESTION_RECOMMENDATION:
                recommendations = generateQuestionRecommendations(request);
                break;
            case STUDY_PLAN_RECOMMENDATION:
                recommendations = generateStudyPlanRecommendations(request);
                break;
            case KNOWLEDGE_POINT_RECOMMENDATION:
                recommendations = generateKnowledgePointRecommendations(request);
                break;
            case WEAKNESS_IMPROVEMENT_RECOMMENDATION:
                recommendations = generateWeaknessImprovementRecommendations(request);
                break;
            case REVIEW_REMINDER_RECOMMENDATION:
                recommendations = generateReviewReminderRecommendations(request);
                break;
            case LEARNING_PATH_RECOMMENDATION:
                recommendations = generateLearningPathRecommendations(request);
                break;
            case DIFFICULTY_ADJUSTMENT_RECOMMENDATION:
                recommendations = generateDifficultyAdjustmentRecommendations(request);
                break;
            default:
                recommendations = generateDefaultRecommendations(request);
                break;
        }

        // 保存推荐
        List<LearningRecommendation> savedRecommendations = recommendationRepository.saveAll(recommendations);

        // 转换为DTO
        List<LearningRecommendationDTO> recommendationDTOs = savedRecommendations.stream()
                .map(LearningRecommendationDTO::from)
                .collect(Collectors.toList());

        // 缓存结果
        redisTemplate.opsForValue().set(cacheKey, recommendationDTOs, CACHE_EXPIRE_HOURS, TimeUnit.HOURS);

        log.info("生成推荐完成: userId={}, count={}", request.getUserId(), recommendationDTOs.size());

        return recommendationDTOs;
    }

    /**
     * 获取用户推荐列表
     */
    @Transactional(readOnly = true)
    public PageResult<LearningRecommendationDTO> getUserRecommendations(Long userId, Integer page, Integer size) {
        log.info("获取用户推荐列表: userId={}", userId);

        Sort sort = Sort.by(Sort.Direction.DESC, "priority", "confidenceScore", "createdAt");
        Pageable pageable = PageRequest.of(page - 1, size, sort);

        Page<LearningRecommendation> recommendationPage = 
                recommendationRepository.findActiveRecommendationsByUser(userId, pageable);

        List<LearningRecommendationDTO> recommendationDTOs = recommendationPage.getContent().stream()
                .map(LearningRecommendationDTO::from)
                .collect(Collectors.toList());

        return PageResult.of(recommendationDTOs, recommendationPage.getTotalElements(), 
                           (long) page, (long) size);
    }

    /**
     * 接受推荐
     */
    public void acceptRecommendation(Long recommendationId, Long userId) {
        log.info("接受推荐: recommendationId={}, userId={}", recommendationId, userId);

        LearningRecommendation recommendation = recommendationRepository.findById(recommendationId)
                .orElseThrow(() -> new BusinessException("推荐不存在"));

        if (!recommendation.getUserId().equals(userId)) {
            throw new BusinessException("无权限操作此推荐");
        }

        recommendation.markAsAccepted();
        recommendationRepository.save(recommendation);

        // 清除缓存
        clearUserRecommendationsCache(userId);

        log.info("推荐已接受: recommendationId={}", recommendationId);
    }

    /**
     * 拒绝推荐
     */
    public void rejectRecommendation(Long recommendationId, Long userId, String feedback) {
        log.info("拒绝推荐: recommendationId={}, userId={}", recommendationId, userId);

        LearningRecommendation recommendation = recommendationRepository.findById(recommendationId)
                .orElseThrow(() -> new BusinessException("推荐不存在"));

        if (!recommendation.getUserId().equals(userId)) {
            throw new BusinessException("无权限操作此推荐");
        }

        recommendation.reject();
        if (feedback != null) {
            recommendation.setUserFeedback(feedback);
        }
        recommendationRepository.save(recommendation);

        // 清除缓存
        clearUserRecommendationsCache(userId);

        log.info("推荐已拒绝: recommendationId={}", recommendationId);
    }

    /**
     * 标记推荐为已查看
     */
    public void markRecommendationAsViewed(Long recommendationId, Long userId) {
        log.debug("标记推荐已查看: recommendationId={}, userId={}", recommendationId, userId);

        LearningRecommendation recommendation = recommendationRepository.findById(recommendationId)
                .orElseThrow(() -> new BusinessException("推荐不存在"));

        if (!recommendation.getUserId().equals(userId)) {
            throw new BusinessException("无权限操作此推荐");
        }

        if (!recommendation.getIsViewed()) {
            recommendation.markAsViewed();
            recommendationRepository.save(recommendation);
        }
    }

    /**
     * 生成题目推荐
     */
    private List<LearningRecommendation> generateQuestionRecommendations(RecommendationRequest request) {
        // TODO: 实现基于协同过滤和内容推荐的题目推荐算法
        // 1. 获取用户能力评估
        // 2. 分析用户答题历史
        // 3. 使用推荐模型生成推荐
        // 4. 根据难度和知识点过滤
        
        return List.of(createSampleRecommendation(request, RecommendationType.QUESTION_RECOMMENDATION,
                "推荐练习题", "基于您的学习进度，推荐以下题目进行练习", BigDecimal.valueOf(0.85)));
    }

    /**
     * 生成学习计划推荐
     */
    private List<LearningRecommendation> generateStudyPlanRecommendations(RecommendationRequest request) {
        // TODO: 实现学习计划推荐算法
        // 1. 分析用户学习目标和时间安排
        // 2. 评估当前学习进度
        // 3. 生成个性化学习计划
        
        return List.of(createSampleRecommendation(request, RecommendationType.STUDY_PLAN_RECOMMENDATION,
                "个性化学习计划", "为您制定了专属的学习计划", BigDecimal.valueOf(0.90)));
    }

    /**
     * 生成知识点推荐
     */
    private List<LearningRecommendation> generateKnowledgePointRecommendations(RecommendationRequest request) {
        // TODO: 实现知识点推荐算法
        // 1. 分析知识点掌握情况
        // 2. 识别学习盲点
        // 3. 推荐重点学习的知识点
        
        return List.of(createSampleRecommendation(request, RecommendationType.KNOWLEDGE_POINT_RECOMMENDATION,
                "重点知识点", "建议重点学习以下知识点", BigDecimal.valueOf(0.80)));
    }

    /**
     * 生成薄弱点强化推荐
     */
    private List<LearningRecommendation> generateWeaknessImprovementRecommendations(RecommendationRequest request) {
        // TODO: 实现薄弱点分析和强化推荐
        // 1. 识别用户薄弱环节
        // 2. 分析错误模式
        // 3. 推荐针对性练习
        
        return List.of(createSampleRecommendation(request, RecommendationType.WEAKNESS_IMPROVEMENT_RECOMMENDATION,
                "薄弱点强化", "针对您的薄弱环节，推荐专项练习", BigDecimal.valueOf(0.95)));
    }

    /**
     * 生成复习提醒推荐
     */
    private List<LearningRecommendation> generateReviewReminderRecommendations(RecommendationRequest request) {
        // TODO: 实现基于遗忘曲线的复习提醒
        // 1. 分析学习时间和遗忘规律
        // 2. 计算最佳复习时间
        // 3. 生成复习提醒
        
        return List.of(createSampleRecommendation(request, RecommendationType.REVIEW_REMINDER_RECOMMENDATION,
                "复习提醒", "根据遗忘曲线，建议复习以下内容", BigDecimal.valueOf(0.88)));
    }

    /**
     * 生成学习路径推荐
     */
    private List<LearningRecommendation> generateLearningPathRecommendations(RecommendationRequest request) {
        // TODO: 实现学习路径推荐算法
        // 1. 分析知识点依赖关系
        // 2. 评估用户当前水平
        // 3. 规划最优学习路径
        
        return List.of(createSampleRecommendation(request, RecommendationType.LEARNING_PATH_RECOMMENDATION,
                "学习路径", "为您规划了最优的学习路径", BigDecimal.valueOf(0.87)));
    }

    /**
     * 生成难度调整推荐
     */
    private List<LearningRecommendation> generateDifficultyAdjustmentRecommendations(RecommendationRequest request) {
        // TODO: 实现难度调整推荐
        // 1. 分析答题正确率和速度
        // 2. 评估当前难度适应性
        // 3. 推荐难度调整方案
        
        return List.of(createSampleRecommendation(request, RecommendationType.DIFFICULTY_ADJUSTMENT_RECOMMENDATION,
                "难度调整", "建议调整学习难度以获得更好效果", BigDecimal.valueOf(0.82)));
    }

    /**
     * 生成默认推荐
     */
    private List<LearningRecommendation> generateDefaultRecommendations(RecommendationRequest request) {
        return List.of(createSampleRecommendation(request, RecommendationType.QUESTION_RECOMMENDATION,
                "学习推荐", "为您推荐适合的学习内容", BigDecimal.valueOf(0.75)));
    }

    /**
     * 创建示例推荐
     */
    private LearningRecommendation createSampleRecommendation(RecommendationRequest request, 
                                                            RecommendationType type, 
                                                            String title, 
                                                            String description, 
                                                            BigDecimal confidence) {
        return LearningRecommendation.builder()
                .userId(request.getUserId())
                .recommendationType(type)
                .status(RecommendationStatus.ACTIVE)
                .title(title)
                .description(description)
                .subjectCode(request.getSubjectCode())
                .recommendedDifficulty(request.getTargetDifficulty())
                .confidenceScore(confidence)
                .priority(8)
                .algorithmVersion("1.0")
                .recommendationReason("基于AI算法分析生成")
                .estimatedDuration(30)
                .estimatedQuestions(10)
                .expiresAt(LocalDateTime.now().plusHours(type.getValidityHours()))
                .build();
    }

    /**
     * 清除用户推荐缓存
     */
    private void clearUserRecommendationsCache(Long userId) {
        String pattern = USER_RECOMMENDATIONS_CACHE_KEY + userId + ":*";
        redisTemplate.delete(redisTemplate.keys(pattern));
    }

}
