package com.chiron.smartlearnsystem.ai.dto;

import com.chiron.smartlearnsystem.ai.entity.UserAbilityAssessment;
import com.chiron.smartlearnsystem.ai.enums.AssessmentType;
import com.chiron.smartlearnsystem.common.core.enums.Difficulty;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户能力评估DTO
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserAbilityAssessmentDTO {

    /**
     * 评估ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 科目代码
     */
    private String subjectCode;

    /**
     * 评估类型
     */
    private AssessmentType assessmentType;

    /**
     * 评估日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime assessmentDate;

    /**
     * 总体能力评分
     */
    private BigDecimal overallAbilityScore;

    /**
     * 当前适应难度
     */
    private Difficulty currentDifficulty;

    /**
     * 推荐难度
     */
    private Difficulty recommendedDifficulty;

    /**
     * IRT能力参数θ
     */
    private BigDecimal irtTheta;

    /**
     * IRT能力参数标准误差
     */
    private BigDecimal irtThetaSe;

    /**
     * 知识点能力分布
     */
    private String knowledgeAbilities;

    /**
     * 题型能力分布
     */
    private String questionTypeAbilities;

    /**
     * 难度能力分布
     */
    private String difficultyAbilities;

    /**
     * 学习风格分析
     */
    private String learningStyle;

    /**
     * 强项知识点
     */
    private String strengthPoints;

    /**
     * 薄弱知识点
     */
    private String weaknessPoints;

    /**
     * 学习速度评估
     */
    private BigDecimal learningSpeed;

    /**
     * 专注度评分
     */
    private BigDecimal focusScore;

    /**
     * 坚持度评分
     */
    private BigDecimal persistenceScore;

    /**
     * 学习效率评分
     */
    private BigDecimal efficiencyScore;

    /**
     * 答题准确率
     */
    private BigDecimal accuracyRate;

    /**
     * 答题速度
     */
    private BigDecimal answerSpeed;

    /**
     * 错误模式分析
     */
    private String errorPatterns;

    /**
     * 学习习惯分析
     */
    private String studyHabits;

    /**
     * 进步趋势
     */
    private String progressTrend;

    /**
     * 评估置信度
     */
    private BigDecimal confidenceLevel;

    /**
     * 评估样本数量
     */
    private Integer sampleSize;

    /**
     * 评估算法版本
     */
    private String algorithmVersion;

    /**
     * 下次评估建议时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime nextAssessmentTime;

    /**
     * 评估备注
     */
    private String assessmentNotes;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 从UserAbilityAssessment实体转换为DTO
     */
    public static UserAbilityAssessmentDTO from(UserAbilityAssessment assessment) {
        if (assessment == null) {
            return null;
        }

        return UserAbilityAssessmentDTO.builder()
                .id(assessment.getId())
                .userId(assessment.getUserId())
                .subjectCode(assessment.getSubjectCode())
                .assessmentType(assessment.getAssessmentType())
                .assessmentDate(assessment.getAssessmentDate())
                .overallAbilityScore(assessment.getOverallAbilityScore())
                .currentDifficulty(assessment.getCurrentDifficulty())
                .recommendedDifficulty(assessment.getRecommendedDifficulty())
                .irtTheta(assessment.getIrtTheta())
                .irtThetaSe(assessment.getIrtThetaSe())
                .knowledgeAbilities(assessment.getKnowledgeAbilities())
                .questionTypeAbilities(assessment.getQuestionTypeAbilities())
                .difficultyAbilities(assessment.getDifficultyAbilities())
                .learningStyle(assessment.getLearningStyle())
                .strengthPoints(assessment.getStrengthPoints())
                .weaknessPoints(assessment.getWeaknessPoints())
                .learningSpeed(assessment.getLearningSpeed())
                .focusScore(assessment.getFocusScore())
                .persistenceScore(assessment.getPersistenceScore())
                .efficiencyScore(assessment.getEfficiencyScore())
                .accuracyRate(assessment.getAccuracyRate())
                .answerSpeed(assessment.getAnswerSpeed())
                .errorPatterns(assessment.getErrorPatterns())
                .studyHabits(assessment.getStudyHabits())
                .progressTrend(assessment.getProgressTrend())
                .confidenceLevel(assessment.getConfidenceLevel())
                .sampleSize(assessment.getSampleSize())
                .algorithmVersion(assessment.getAlgorithmVersion())
                .nextAssessmentTime(assessment.getNextAssessmentTime())
                .assessmentNotes(assessment.getAssessmentNotes())
                .createdAt(assessment.getCreatedAt())
                .updatedAt(assessment.getUpdatedAt())
                .build();
    }

    /**
     * 获取能力等级描述
     */
    public String getAbilityLevelDescription() {
        if (overallAbilityScore == null) {
            return "未评估";
        }
        
        if (overallAbilityScore.compareTo(BigDecimal.valueOf(90)) >= 0) {
            return "优秀";
        } else if (overallAbilityScore.compareTo(BigDecimal.valueOf(80)) >= 0) {
            return "良好";
        } else if (overallAbilityScore.compareTo(BigDecimal.valueOf(70)) >= 0) {
            return "中等";
        } else if (overallAbilityScore.compareTo(BigDecimal.valueOf(60)) >= 0) {
            return "及格";
        } else {
            return "待提高";
        }
    }

    /**
     * 获取评估类型描述
     */
    public String getAssessmentTypeDescription() {
        return assessmentType != null ? assessmentType.getDescription() : "";
    }

    /**
     * 获取置信度百分比
     */
    public String getConfidencePercentage() {
        if (confidenceLevel == null) {
            return "0%";
        }
        return confidenceLevel.multiply(BigDecimal.valueOf(100))
                .setScale(1, BigDecimal.ROUND_HALF_UP) + "%";
    }

    /**
     * 检查是否为高能力用户
     */
    public boolean isHighAbility() {
        return overallAbilityScore != null && overallAbilityScore.compareTo(BigDecimal.valueOf(80)) >= 0;
    }

    /**
     * 检查是否为低能力用户
     */
    public boolean isLowAbility() {
        return overallAbilityScore != null && overallAbilityScore.compareTo(BigDecimal.valueOf(40)) < 0;
    }

    /**
     * 检查评估是否可信
     */
    public boolean isReliable() {
        return confidenceLevel != null && confidenceLevel.compareTo(BigDecimal.valueOf(0.7)) >= 0 &&
               sampleSize != null && sampleSize >= 20;
    }

    /**
     * 检查是否需要重新评估
     */
    public boolean needsReassessment() {
        return nextAssessmentTime != null && LocalDateTime.now().isAfter(nextAssessmentTime);
    }

}
