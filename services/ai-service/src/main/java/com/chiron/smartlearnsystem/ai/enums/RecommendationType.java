package com.chiron.smartlearnsystem.ai.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 推荐类型枚举
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum RecommendationType {

    /**
     * 题目推荐
     */
    QUESTION_RECOMMENDATION("QUESTION_RECOMMENDATION", "题目推荐", "基于用户能力和学习进度推荐合适的题目"),

    /**
     * 学习计划推荐
     */
    STUDY_PLAN_RECOMMENDATION("STUDY_PLAN_RECOMMENDATION", "学习计划推荐", "为用户推荐个性化的学习计划"),

    /**
     * 知识点推荐
     */
    KNOWLEDGE_POINT_RECOMMENDATION("KNOWLEDGE_POINT_RECOMMENDATION", "知识点推荐", "推荐用户应该重点学习的知识点"),

    /**
     * 薄弱点强化推荐
     */
    WEAKNESS_IMPROVEMENT_RECOMMENDATION("WEAKNESS_IMPROVEMENT_RECOMMENDATION", "薄弱点强化推荐", "针对用户薄弱环节的专项推荐"),

    /**
     * 复习提醒推荐
     */
    REVIEW_REMINDER_RECOMMENDATION("REVIEW_REMINDER_RECOMMENDATION", "复习提醒推荐", "基于遗忘曲线的复习时间推荐"),

    /**
     * 学习路径推荐
     */
    LEARNING_PATH_RECOMMENDATION("LEARNING_PATH_RECOMMENDATION", "学习路径推荐", "推荐最优的学习顺序和路径"),

    /**
     * 难度调整推荐
     */
    DIFFICULTY_ADJUSTMENT_RECOMMENDATION("DIFFICULTY_ADJUSTMENT_RECOMMENDATION", "难度调整推荐", "根据学习效果推荐难度调整"),

    /**
     * 学习时间推荐
     */
    STUDY_TIME_RECOMMENDATION("STUDY_TIME_RECOMMENDATION", "学习时间推荐", "推荐最佳的学习时间安排"),

    /**
     * 考试策略推荐
     */
    EXAM_STRATEGY_RECOMMENDATION("EXAM_STRATEGY_RECOMMENDATION", "考试策略推荐", "针对即将到来的考试的策略建议"),

    /**
     * 学习方法推荐
     */
    STUDY_METHOD_RECOMMENDATION("STUDY_METHOD_RECOMMENDATION", "学习方法推荐", "推荐适合用户的学习方法和技巧"),

    /**
     * 资源推荐
     */
    RESOURCE_RECOMMENDATION("RESOURCE_RECOMMENDATION", "资源推荐", "推荐相关的学习资源和材料"),

    /**
     * 同伴学习推荐
     */
    PEER_LEARNING_RECOMMENDATION("PEER_LEARNING_RECOMMENDATION", "同伴学习推荐", "推荐学习伙伴或学习小组");

    private final String code;
    private final String name;
    private final String description;

    /**
     * 根据代码获取推荐类型
     */
    public static RecommendationType getByCode(String code) {
        for (RecommendationType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return QUESTION_RECOMMENDATION; // 默认返回题目推荐
    }

    /**
     * 是否为内容推荐
     */
    public boolean isContentRecommendation() {
        return this == QUESTION_RECOMMENDATION || this == KNOWLEDGE_POINT_RECOMMENDATION || 
               this == RESOURCE_RECOMMENDATION;
    }

    /**
     * 是否为策略推荐
     */
    public boolean isStrategyRecommendation() {
        return this == STUDY_PLAN_RECOMMENDATION || this == LEARNING_PATH_RECOMMENDATION || 
               this == EXAM_STRATEGY_RECOMMENDATION || this == STUDY_METHOD_RECOMMENDATION;
    }

    /**
     * 是否为时间相关推荐
     */
    public boolean isTimeRelatedRecommendation() {
        return this == REVIEW_REMINDER_RECOMMENDATION || this == STUDY_TIME_RECOMMENDATION;
    }

    /**
     * 是否为个性化推荐
     */
    public boolean isPersonalizedRecommendation() {
        return this == WEAKNESS_IMPROVEMENT_RECOMMENDATION || this == DIFFICULTY_ADJUSTMENT_RECOMMENDATION || 
               this == PEER_LEARNING_RECOMMENDATION;
    }

    /**
     * 获取推荐权重
     */
    public double getRecommendationWeight() {
        switch (this) {
            case WEAKNESS_IMPROVEMENT_RECOMMENDATION:
                return 1.0; // 薄弱点强化最重要
            case REVIEW_REMINDER_RECOMMENDATION:
                return 0.9; // 复习提醒很重要
            case QUESTION_RECOMMENDATION:
                return 0.8; // 题目推荐重要
            case STUDY_PLAN_RECOMMENDATION:
                return 0.8; // 学习计划重要
            case DIFFICULTY_ADJUSTMENT_RECOMMENDATION:
                return 0.7; // 难度调整较重要
            case LEARNING_PATH_RECOMMENDATION:
                return 0.7; // 学习路径较重要
            case EXAM_STRATEGY_RECOMMENDATION:
                return 0.6; // 考试策略中等重要
            case KNOWLEDGE_POINT_RECOMMENDATION:
                return 0.6; // 知识点推荐中等重要
            case STUDY_TIME_RECOMMENDATION:
                return 0.5; // 时间推荐一般重要
            case STUDY_METHOD_RECOMMENDATION:
                return 0.5; // 方法推荐一般重要
            case RESOURCE_RECOMMENDATION:
                return 0.4; // 资源推荐较次要
            case PEER_LEARNING_RECOMMENDATION:
                return 0.3; // 同伴学习次要
            default:
                return 0.5;
        }
    }

    /**
     * 获取推荐有效期（小时）
     */
    public int getValidityHours() {
        switch (this) {
            case REVIEW_REMINDER_RECOMMENDATION:
                return 6; // 复习提醒6小时有效
            case STUDY_TIME_RECOMMENDATION:
                return 12; // 时间推荐12小时有效
            case QUESTION_RECOMMENDATION:
                return 24; // 题目推荐24小时有效
            case WEAKNESS_IMPROVEMENT_RECOMMENDATION:
                return 48; // 薄弱点强化48小时有效
            case DIFFICULTY_ADJUSTMENT_RECOMMENDATION:
                return 72; // 难度调整72小时有效
            case STUDY_PLAN_RECOMMENDATION:
            case LEARNING_PATH_RECOMMENDATION:
                return 168; // 计划和路径推荐1周有效
            case EXAM_STRATEGY_RECOMMENDATION:
                return 336; // 考试策略2周有效
            case KNOWLEDGE_POINT_RECOMMENDATION:
            case STUDY_METHOD_RECOMMENDATION:
            case RESOURCE_RECOMMENDATION:
            case PEER_LEARNING_RECOMMENDATION:
                return 720; // 其他推荐1个月有效
            default:
                return 168; // 默认1周有效
        }
    }

}
