package com.chiron.smartlearnsystem.ai.entity;

import com.chiron.smartlearnsystem.ai.enums.AssessmentType;
import com.chiron.smartlearnsystem.common.core.enums.Difficulty;
import com.chiron.smartlearnsystem.common.database.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户能力评估实体
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Entity
@Table(name = "user_ability_assessments", indexes = {
    @Index(name = "idx_user_subject", columnList = "user_id, subject_code"),
    @Index(name = "idx_assessment_type", columnList = "assessment_type"),
    @Index(name = "idx_overall_ability", columnList = "overall_ability_score"),
    @Index(name = "idx_assessment_date", columnList = "assessment_date"),
    @Index(name = "idx_created_at", columnList = "created_at")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserAbilityAssessment extends BaseEntity {

    /**
     * 用户ID
     */
    @Column(name = "user_id", nullable = false)
    private Long userId;

    /**
     * 科目代码
     */
    @Column(name = "subject_code", nullable = false, length = 20)
    private String subjectCode;

    /**
     * 评估类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "assessment_type", nullable = false, length = 30)
    private AssessmentType assessmentType;

    /**
     * 评估日期
     */
    @Column(name = "assessment_date", nullable = false)
    private LocalDateTime assessmentDate;

    /**
     * 总体能力评分（0-100）
     */
    @Column(name = "overall_ability_score", nullable = false, precision = 5, scale = 2)
    private BigDecimal overallAbilityScore;

    /**
     * 当前适应难度
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "current_difficulty", nullable = false, length = 20)
    private Difficulty currentDifficulty;

    /**
     * 推荐难度
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "recommended_difficulty", nullable = false, length = 20)
    private Difficulty recommendedDifficulty;

    /**
     * IRT能力参数θ（theta）
     */
    @Column(name = "irt_theta", precision = 8, scale = 4)
    private BigDecimal irtTheta;

    /**
     * IRT能力参数标准误差
     */
    @Column(name = "irt_theta_se", precision = 8, scale = 4)
    private BigDecimal irtThetaSe;

    /**
     * 知识点能力分布（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "knowledge_abilities", columnDefinition = "JSON")
    private String knowledgeAbilities;

    /**
     * 题型能力分布（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "question_type_abilities", columnDefinition = "JSON")
    private String questionTypeAbilities;

    /**
     * 难度能力分布（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "difficulty_abilities", columnDefinition = "JSON")
    private String difficultyAbilities;

    /**
     * 学习风格分析（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "learning_style", columnDefinition = "JSON")
    private String learningStyle;

    /**
     * 强项知识点（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "strength_points", columnDefinition = "JSON")
    private String strengthPoints;

    /**
     * 薄弱知识点（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "weakness_points", columnDefinition = "JSON")
    private String weaknessPoints;

    /**
     * 学习速度评估（题/分钟）
     */
    @Column(name = "learning_speed", precision = 5, scale = 2)
    private BigDecimal learningSpeed;

    /**
     * 专注度评分（1-10）
     */
    @Column(name = "focus_score", precision = 3, scale = 1)
    private BigDecimal focusScore;

    /**
     * 坚持度评分（1-10）
     */
    @Column(name = "persistence_score", precision = 3, scale = 1)
    private BigDecimal persistenceScore;

    /**
     * 学习效率评分（1-10）
     */
    @Column(name = "efficiency_score", precision = 3, scale = 1)
    private BigDecimal efficiencyScore;

    /**
     * 答题准确率
     */
    @Column(name = "accuracy_rate", precision = 5, scale = 4)
    private BigDecimal accuracyRate;

    /**
     * 答题速度（秒/题）
     */
    @Column(name = "answer_speed", precision = 6, scale = 2)
    private BigDecimal answerSpeed;

    /**
     * 错误模式分析（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "error_patterns", columnDefinition = "JSON")
    private String errorPatterns;

    /**
     * 学习习惯分析（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "study_habits", columnDefinition = "JSON")
    private String studyHabits;

    /**
     * 进步趋势（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "progress_trend", columnDefinition = "JSON")
    private String progressTrend;

    /**
     * 评估置信度（0-1）
     */
    @Column(name = "confidence_level", precision = 5, scale = 4)
    private BigDecimal confidenceLevel;

    /**
     * 评估样本数量
     */
    @Column(name = "sample_size")
    private Integer sampleSize;

    /**
     * 评估算法版本
     */
    @Column(name = "algorithm_version", length = 20)
    private String algorithmVersion;

    /**
     * 评估原始数据（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "raw_data", columnDefinition = "JSON")
    private String rawData;

    /**
     * 下次评估建议时间
     */
    @Column(name = "next_assessment_time")
    private LocalDateTime nextAssessmentTime;

    /**
     * 评估备注
     */
    @Column(name = "assessment_notes", columnDefinition = "TEXT")
    private String assessmentNotes;

    /**
     * 检查是否为高能力用户
     */
    public boolean isHighAbility() {
        return overallAbilityScore.compareTo(BigDecimal.valueOf(80)) >= 0;
    }

    /**
     * 检查是否为低能力用户
     */
    public boolean isLowAbility() {
        return overallAbilityScore.compareTo(BigDecimal.valueOf(40)) < 0;
    }

    /**
     * 检查是否需要重新评估
     */
    public boolean needsReassessment() {
        return nextAssessmentTime != null && LocalDateTime.now().isAfter(nextAssessmentTime);
    }

    /**
     * 检查评估是否可信
     */
    public boolean isReliable() {
        return confidenceLevel != null && confidenceLevel.compareTo(BigDecimal.valueOf(0.7)) >= 0 &&
               sampleSize != null && sampleSize >= 20;
    }

    /**
     * 获取能力等级描述
     */
    public String getAbilityLevelDescription() {
        if (overallAbilityScore.compareTo(BigDecimal.valueOf(90)) >= 0) {
            return "优秀";
        } else if (overallAbilityScore.compareTo(BigDecimal.valueOf(80)) >= 0) {
            return "良好";
        } else if (overallAbilityScore.compareTo(BigDecimal.valueOf(70)) >= 0) {
            return "中等";
        } else if (overallAbilityScore.compareTo(BigDecimal.valueOf(60)) >= 0) {
            return "及格";
        } else {
            return "待提高";
        }
    }

    /**
     * 计算能力提升幅度
     */
    public BigDecimal calculateImprovement(BigDecimal previousScore) {
        if (previousScore == null) {
            return BigDecimal.ZERO;
        }
        return overallAbilityScore.subtract(previousScore);
    }

    /**
     * 更新下次评估时间
     */
    public void updateNextAssessmentTime() {
        // 根据能力水平和评估类型确定下次评估间隔
        int daysInterval;
        if (isHighAbility()) {
            daysInterval = 14; // 高能力用户2周评估一次
        } else if (isLowAbility()) {
            daysInterval = 3; // 低能力用户3天评估一次
        } else {
            daysInterval = 7; // 中等能力用户1周评估一次
        }
        
        this.nextAssessmentTime = LocalDateTime.now().plusDays(daysInterval);
    }

}
