package com.chiron.smartlearnsystem.ai.controller;

import com.chiron.smartlearnsystem.ai.dto.UserAbilityAssessmentDTO;
import com.chiron.smartlearnsystem.ai.dto.request.AbilityAssessmentRequest;
import com.chiron.smartlearnsystem.ai.service.AbilityAssessmentService;
import com.chiron.smartlearnsystem.common.core.domain.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * 能力评估控制器
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/ai/ability-assessment")
@RequiredArgsConstructor
@Validated
public class AbilityAssessmentController {

    private final AbilityAssessmentService abilityAssessmentService;

    /**
     * 进行能力评估
     */
    @PostMapping("/assess")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<UserAbilityAssessmentDTO>> assessUserAbility(
            @Valid @RequestBody AbilityAssessmentRequest request) {
        
        log.info("进行能力评估请求: {}", request);
        
        UserAbilityAssessmentDTO assessment = abilityAssessmentService.assessUserAbility(request);
        
        return ResponseEntity.ok(ApiResponse.success("能力评估完成", assessment));
    }

    /**
     * 获取用户最新能力评估
     */
    @GetMapping("/user/{userId}/latest")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<UserAbilityAssessmentDTO>> getUserLatestAbility(
            @PathVariable Long userId,
            @RequestParam String subjectCode) {
        
        log.info("获取用户最新能力评估: userId={}, subject={}", userId, subjectCode);
        
        UserAbilityAssessmentDTO assessment = 
                abilityAssessmentService.getUserLatestAbility(userId, subjectCode);
        
        if (assessment == null) {
            return ResponseEntity.ok(ApiResponse.success("暂无能力评估数据", null));
        }
        
        return ResponseEntity.ok(ApiResponse.success("获取能力评估成功", assessment));
    }

    /**
     * 获取用户能力历史
     */
    @GetMapping("/user/{userId}/history")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<List<UserAbilityAssessmentDTO>>> getUserAbilityHistory(
            @PathVariable Long userId,
            @RequestParam String subjectCode) {
        
        log.info("获取用户能力历史: userId={}, subject={}", userId, subjectCode);
        
        List<UserAbilityAssessmentDTO> history = 
                abilityAssessmentService.getUserAbilityHistory(userId, subjectCode);
        
        return ResponseEntity.ok(ApiResponse.success("获取能力历史成功", history));
    }

    /**
     * 实时更新用户能力
     */
    @PostMapping("/update-realtime")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse<Void>> updateAbilityRealTime(
            @RequestParam Long userId,
            @RequestParam String subjectCode,
            @RequestParam Boolean isCorrect,
            @RequestParam(required = false) String questionDifficulty,
            @RequestParam(required = false) Integer timeSpent) {
        
        log.debug("实时更新用户能力: userId={}, subject={}, correct={}", 
                userId, subjectCode, isCorrect);
        
        abilityAssessmentService.updateAbilityRealTime(userId, subjectCode, isCorrect, 
                questionDifficulty, timeSpent);
        
        return ResponseEntity.ok(ApiResponse.<Void>success("能力更新成功", null));
    }

}
