package com.chiron.smartlearnsystem.ai.repository;

import com.chiron.smartlearnsystem.ai.entity.UserAbilityAssessment;
import com.chiron.smartlearnsystem.ai.enums.AssessmentType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 用户能力评估数据访问层
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Repository
public interface UserAbilityAssessmentRepository extends JpaRepository<UserAbilityAssessment, Long>, 
                                                       JpaSpecificationExecutor<UserAbilityAssessment> {

    /**
     * 根据用户ID查找评估记录
     */
    List<UserAbilityAssessment> findByUserId(Long userId);

    /**
     * 根据用户ID和科目查找评估记录
     */
    List<UserAbilityAssessment> findByUserIdAndSubjectCode(Long userId, String subjectCode);

    /**
     * 根据用户ID和科目查找评估记录（按时间倒序）
     */
    List<UserAbilityAssessment> findByUserIdAndSubjectCodeOrderByAssessmentDateDesc(Long userId, String subjectCode);

    /**
     * 获取用户最新的能力评估
     */
    Optional<UserAbilityAssessment> findTopByUserIdAndSubjectCodeOrderByAssessmentDateDesc(Long userId, String subjectCode);

    /**
     * 根据评估类型查找评估记录
     */
    List<UserAbilityAssessment> findByAssessmentType(AssessmentType assessmentType);

    /**
     * 根据用户ID和评估类型查找评估记录
     */
    List<UserAbilityAssessment> findByUserIdAndAssessmentType(Long userId, AssessmentType assessmentType);

    /**
     * 查找需要重新评估的用户
     */
    @Query("SELECT ua FROM UserAbilityAssessment ua WHERE ua.nextAssessmentTime < :now")
    List<UserAbilityAssessment> findUsersNeedingReassessment(@Param("now") LocalDateTime now);

    /**
     * 查找高能力用户
     */
    @Query("SELECT ua FROM UserAbilityAssessment ua WHERE ua.overallAbilityScore >= :minScore " +
           "AND ua.assessmentDate = (SELECT MAX(ua2.assessmentDate) FROM UserAbilityAssessment ua2 " +
           "WHERE ua2.userId = ua.userId AND ua2.subjectCode = ua.subjectCode)")
    List<UserAbilityAssessment> findHighAbilityUsers(@Param("minScore") BigDecimal minScore);

    /**
     * 查找低能力用户
     */
    @Query("SELECT ua FROM UserAbilityAssessment ua WHERE ua.overallAbilityScore < :maxScore " +
           "AND ua.assessmentDate = (SELECT MAX(ua2.assessmentDate) FROM UserAbilityAssessment ua2 " +
           "WHERE ua2.userId = ua.userId AND ua2.subjectCode = ua.subjectCode)")
    List<UserAbilityAssessment> findLowAbilityUsers(@Param("maxScore") BigDecimal maxScore);

    /**
     * 查找可信度高的评估
     */
    @Query("SELECT ua FROM UserAbilityAssessment ua WHERE ua.confidenceLevel >= :minConfidence " +
           "AND ua.sampleSize >= :minSampleSize")
    List<UserAbilityAssessment> findReliableAssessments(@Param("minConfidence") BigDecimal minConfidence,
                                                       @Param("minSampleSize") Integer minSampleSize);

    /**
     * 根据能力分数范围查找用户
     */
    @Query("SELECT ua FROM UserAbilityAssessment ua WHERE ua.overallAbilityScore BETWEEN :minScore AND :maxScore " +
           "AND ua.assessmentDate = (SELECT MAX(ua2.assessmentDate) FROM UserAbilityAssessment ua2 " +
           "WHERE ua2.userId = ua.userId AND ua2.subjectCode = ua.subjectCode)")
    List<UserAbilityAssessment> findUsersByAbilityRange(@Param("minScore") BigDecimal minScore,
                                                       @Param("maxScore") BigDecimal maxScore);

    /**
     * 统计各科目的平均能力分数
     */
    @Query("SELECT ua.subjectCode, AVG(ua.overallAbilityScore) FROM UserAbilityAssessment ua " +
           "WHERE ua.assessmentDate = (SELECT MAX(ua2.assessmentDate) FROM UserAbilityAssessment ua2 " +
           "WHERE ua2.userId = ua.userId AND ua2.subjectCode = ua.subjectCode) " +
           "GROUP BY ua.subjectCode")
    List<Object[]> getAverageAbilityBySubject();

    /**
     * 统计用户能力分布
     */
    @Query("SELECT " +
           "SUM(CASE WHEN ua.overallAbilityScore >= 90 THEN 1 ELSE 0 END) as excellent, " +
           "SUM(CASE WHEN ua.overallAbilityScore >= 80 AND ua.overallAbilityScore < 90 THEN 1 ELSE 0 END) as good, " +
           "SUM(CASE WHEN ua.overallAbilityScore >= 70 AND ua.overallAbilityScore < 80 THEN 1 ELSE 0 END) as average, " +
           "SUM(CASE WHEN ua.overallAbilityScore >= 60 AND ua.overallAbilityScore < 70 THEN 1 ELSE 0 END) as pass, " +
           "SUM(CASE WHEN ua.overallAbilityScore < 60 THEN 1 ELSE 0 END) as needImprovement " +
           "FROM UserAbilityAssessment ua WHERE ua.subjectCode = :subjectCode " +
           "AND ua.assessmentDate = (SELECT MAX(ua2.assessmentDate) FROM UserAbilityAssessment ua2 " +
           "WHERE ua2.userId = ua.userId AND ua2.subjectCode = ua.subjectCode)")
    Object[] getAbilityDistribution(@Param("subjectCode") String subjectCode);

    /**
     * 获取用户能力进步趋势
     */
    @Query("SELECT ua FROM UserAbilityAssessment ua WHERE ua.userId = :userId AND ua.subjectCode = :subjectCode " +
           "ORDER BY ua.assessmentDate ASC")
    List<UserAbilityAssessment> getUserAbilityTrend(@Param("userId") Long userId, @Param("subjectCode") String subjectCode);

    /**
     * 查找最近评估的用户
     */
    @Query("SELECT ua FROM UserAbilityAssessment ua WHERE ua.assessmentDate >= :since " +
           "ORDER BY ua.assessmentDate DESC")
    List<UserAbilityAssessment> findRecentAssessments(@Param("since") LocalDateTime since, Pageable pageable);

    /**
     * 根据IRT能力参数范围查找用户
     */
    @Query("SELECT ua FROM UserAbilityAssessment ua WHERE ua.irtTheta BETWEEN :minTheta AND :maxTheta " +
           "AND ua.assessmentDate = (SELECT MAX(ua2.assessmentDate) FROM UserAbilityAssessment ua2 " +
           "WHERE ua2.userId = ua.userId AND ua2.subjectCode = ua.subjectCode)")
    List<UserAbilityAssessment> findUsersByIRTRange(@Param("minTheta") BigDecimal minTheta,
                                                   @Param("maxTheta") BigDecimal maxTheta);

    /**
     * 查找特定算法版本的评估
     */
    List<UserAbilityAssessment> findByAlgorithmVersion(String algorithmVersion);

    /**
     * 统计评估类型分布
     */
    @Query("SELECT ua.assessmentType, COUNT(ua) FROM UserAbilityAssessment ua GROUP BY ua.assessmentType")
    List<Object[]> countAssessmentsByType();

    /**
     * 查找学习效率高的用户
     */
    @Query("SELECT ua FROM UserAbilityAssessment ua WHERE ua.efficiencyScore >= :minEfficiency " +
           "AND ua.assessmentDate = (SELECT MAX(ua2.assessmentDate) FROM UserAbilityAssessment ua2 " +
           "WHERE ua2.userId = ua.userId AND ua2.subjectCode = ua.subjectCode)")
    List<UserAbilityAssessment> findHighEfficiencyUsers(@Param("minEfficiency") BigDecimal minEfficiency);

    /**
     * 查找专注度高的用户
     */
    @Query("SELECT ua FROM UserAbilityAssessment ua WHERE ua.focusScore >= :minFocus " +
           "AND ua.assessmentDate = (SELECT MAX(ua2.assessmentDate) FROM UserAbilityAssessment ua2 " +
           "WHERE ua2.userId = ua.userId AND ua2.subjectCode = ua.subjectCode)")
    List<UserAbilityAssessment> findHighFocusUsers(@Param("minFocus") BigDecimal minFocus);

    /**
     * 查找坚持度高的用户
     */
    @Query("SELECT ua FROM UserAbilityAssessment ua WHERE ua.persistenceScore >= :minPersistence " +
           "AND ua.assessmentDate = (SELECT MAX(ua2.assessmentDate) FROM UserAbilityAssessment ua2 " +
           "WHERE ua2.userId = ua.userId AND ua2.subjectCode = ua.subjectCode)")
    List<UserAbilityAssessment> findHighPersistenceUsers(@Param("minPersistence") BigDecimal minPersistence);

    /**
     * 获取用户各科目最新评估
     */
    @Query("SELECT ua FROM UserAbilityAssessment ua WHERE ua.userId = :userId " +
           "AND ua.assessmentDate = (SELECT MAX(ua2.assessmentDate) FROM UserAbilityAssessment ua2 " +
           "WHERE ua2.userId = ua.userId AND ua2.subjectCode = ua.subjectCode)")
    List<UserAbilityAssessment> getUserLatestAssessments(@Param("userId") Long userId);

    /**
     * 分页查询用户评估历史
     */
    Page<UserAbilityAssessment> findByUserIdOrderByAssessmentDateDesc(Long userId, Pageable pageable);

}
