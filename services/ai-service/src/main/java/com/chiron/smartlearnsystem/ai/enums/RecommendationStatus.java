package com.chiron.smartlearnsystem.ai.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 推荐状态枚举
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum RecommendationStatus {

    /**
     * 活跃状态
     */
    ACTIVE("ACTIVE", "活跃", "推荐正在生效中"),

    /**
     * 已查看
     */
    VIEWED("VIEWED", "已查看", "用户已查看推荐"),

    /**
     * 已采纳
     */
    ACCEPTED("ACCEPTED", "已采纳", "用户已采纳推荐"),

    /**
     * 已拒绝
     */
    REJECTED("REJECTED", "已拒绝", "用户拒绝了推荐"),

    /**
     * 已过期
     */
    EXPIRED("EXPIRED", "已过期", "推荐已过期"),

    /**
     * 已取消
     */
    CANCELLED("CANCELLED", "已取消", "推荐被系统取消"),

    /**
     * 执行中
     */
    IN_PROGRESS("IN_PROGRESS", "执行中", "推荐正在执行中"),

    /**
     * 已完成
     */
    COMPLETED("COMPLETED", "已完成", "推荐已完成执行");

    private final String code;
    private final String name;
    private final String description;

    /**
     * 根据代码获取推荐状态
     */
    public static RecommendationStatus getByCode(String code) {
        for (RecommendationStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return ACTIVE; // 默认返回活跃状态
    }

    /**
     * 是否为活跃状态
     */
    public boolean isActive() {
        return this == ACTIVE;
    }

    /**
     * 是否为最终状态
     */
    public boolean isFinalStatus() {
        return this == ACCEPTED || this == REJECTED || this == EXPIRED || 
               this == CANCELLED || this == COMPLETED;
    }

    /**
     * 是否为正面状态
     */
    public boolean isPositiveStatus() {
        return this == ACCEPTED || this == IN_PROGRESS || this == COMPLETED;
    }

    /**
     * 是否为负面状态
     */
    public boolean isNegativeStatus() {
        return this == REJECTED || this == EXPIRED || this == CANCELLED;
    }

    /**
     * 是否可以执行
     */
    public boolean canExecute() {
        return this == ACTIVE || this == VIEWED;
    }

    /**
     * 是否可以取消
     */
    public boolean canCancel() {
        return this == ACTIVE || this == VIEWED || this == IN_PROGRESS;
    }

    /**
     * 是否需要用户反馈
     */
    public boolean needsFeedback() {
        return this == COMPLETED || this == REJECTED;
    }

}
