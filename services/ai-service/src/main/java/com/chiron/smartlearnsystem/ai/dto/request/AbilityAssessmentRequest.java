package com.chiron.smartlearnsystem.ai.dto.request;

import com.chiron.smartlearnsystem.ai.enums.AssessmentType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

/**
 * 能力评估请求
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AbilityAssessmentRequest {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 科目代码
     */
    @NotNull(message = "科目代码不能为空")
    @Size(max = 20, message = "科目代码长度不能超过20个字符")
    private String subjectCode;

    /**
     * 评估类型
     */
    @NotNull(message = "评估类型不能为空")
    private AssessmentType assessmentType;

    /**
     * 样本数量
     */
    @Min(value = 1, message = "样本数量不能少于1")
    @Max(value = 1000, message = "样本数量不能超过1000")
    private Integer sampleSize;

    /**
     * 正确率
     */
    @Min(value = 0, message = "正确率不能小于0")
    @Max(value = 1, message = "正确率不能大于1")
    private BigDecimal accuracyRate;

    /**
     * 平均答题时间（秒）
     */
    @Min(value = 1, message = "平均答题时间不能少于1秒")
    private BigDecimal averageAnswerTime;

    /**
     * 答题记录数据
     */
    private List<AnswerRecordData> answerRecords;

    /**
     * 学习会话数据
     */
    private List<StudySessionData> studySessions;

    /**
     * 知识点掌握情况
     */
    private String knowledgePointMastery;

    /**
     * 学习时长（分钟）
     */
    @Min(value = 0, message = "学习时长不能小于0")
    private Integer studyTimeMinutes;

    /**
     * 学习天数
     */
    @Min(value = 0, message = "学习天数不能小于0")
    private Integer studyDays;

    /**
     * 连续学习天数
     */
    @Min(value = 0, message = "连续学习天数不能小于0")
    private Integer consecutiveStudyDays;

    /**
     * 错误模式数据
     */
    private String errorPatternData;

    /**
     * 学习习惯数据
     */
    private String studyHabitData;

    /**
     * 额外参数
     */
    private String additionalParams;

    /**
     * 答题记录数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AnswerRecordData {
        private Long questionId;
        private String questionType;
        private String difficulty;
        private Boolean isCorrect;
        private Integer timeSpent;
        private String knowledgePoints;
    }

    /**
     * 学习会话数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StudySessionData {
        private Integer duration;
        private Integer questionsCompleted;
        private Integer questionsCorrect;
        private BigDecimal focusScore;
        private String sessionType;
    }

}
