package com.chiron.smartlearnsystem.ai.dto;

import com.chiron.smartlearnsystem.ai.entity.LearningRecommendation;
import com.chiron.smartlearnsystem.ai.enums.RecommendationStatus;
import com.chiron.smartlearnsystem.ai.enums.RecommendationType;
import com.chiron.smartlearnsystem.common.core.enums.Difficulty;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Set;

/**
 * 学习推荐DTO
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LearningRecommendationDTO {

    /**
     * 推荐ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 推荐类型
     */
    private RecommendationType recommendationType;

    /**
     * 推荐状态
     */
    private RecommendationStatus status;

    /**
     * 推荐标题
     */
    private String title;

    /**
     * 推荐描述
     */
    private String description;

    /**
     * 推荐内容
     */
    private String recommendationContent;

    /**
     * 科目代码
     */
    private String subjectCode;

    /**
     * 推荐难度
     */
    private Difficulty recommendedDifficulty;

    /**
     * 置信度评分
     */
    private BigDecimal confidenceScore;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 推荐算法版本
     */
    private String algorithmVersion;

    /**
     * 推荐原因
     */
    private String recommendationReason;

    /**
     * 预期学习时长（分钟）
     */
    private Integer estimatedDuration;

    /**
     * 预期完成题目数
     */
    private Integer estimatedQuestions;

    /**
     * 目标知识点
     */
    private Set<String> targetKnowledgePoints;

    /**
     * 推荐标签
     */
    private Set<String> tags;

    /**
     * 是否已查看
     */
    private Boolean isViewed;

    /**
     * 查看时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime viewedAt;

    /**
     * 是否已采纳
     */
    private Boolean isAccepted;

    /**
     * 采纳时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime acceptedAt;

    /**
     * 用户反馈评分
     */
    private Integer userFeedbackScore;

    /**
     * 用户反馈内容
     */
    private String userFeedback;

    /**
     * 推荐过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expiresAt;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 从LearningRecommendation实体转换为DTO
     */
    public static LearningRecommendationDTO from(LearningRecommendation recommendation) {
        if (recommendation == null) {
            return null;
        }

        return LearningRecommendationDTO.builder()
                .id(recommendation.getId())
                .userId(recommendation.getUserId())
                .recommendationType(recommendation.getRecommendationType())
                .status(recommendation.getStatus())
                .title(recommendation.getTitle())
                .description(recommendation.getDescription())
                .recommendationContent(recommendation.getRecommendationContent())
                .subjectCode(recommendation.getSubjectCode())
                .recommendedDifficulty(recommendation.getRecommendedDifficulty())
                .confidenceScore(recommendation.getConfidenceScore())
                .priority(recommendation.getPriority())
                .algorithmVersion(recommendation.getAlgorithmVersion())
                .recommendationReason(recommendation.getRecommendationReason())
                .estimatedDuration(recommendation.getEstimatedDuration())
                .estimatedQuestions(recommendation.getEstimatedQuestions())
                .targetKnowledgePoints(recommendation.getTargetKnowledgePoints())
                .tags(recommendation.getTags())
                .isViewed(recommendation.getIsViewed())
                .viewedAt(recommendation.getViewedAt())
                .isAccepted(recommendation.getIsAccepted())
                .acceptedAt(recommendation.getAcceptedAt())
                .userFeedbackScore(recommendation.getUserFeedbackScore())
                .userFeedback(recommendation.getUserFeedback())
                .expiresAt(recommendation.getExpiresAt())
                .createdAt(recommendation.getCreatedAt())
                .updatedAt(recommendation.getUpdatedAt())
                .build();
    }

    /**
     * 获取推荐类型描述
     */
    public String getRecommendationTypeDescription() {
        return recommendationType != null ? recommendationType.getDescription() : "";
    }

    /**
     * 获取状态描述
     */
    public String getStatusDescription() {
        return status != null ? status.getDescription() : "";
    }

    /**
     * 获取置信度百分比
     */
    public String getConfidencePercentage() {
        if (confidenceScore == null) {
            return "0%";
        }
        return confidenceScore.multiply(BigDecimal.valueOf(100))
                .setScale(1, BigDecimal.ROUND_HALF_UP) + "%";
    }

    /**
     * 检查是否为高优先级
     */
    public boolean isHighPriority() {
        return priority != null && priority >= 8;
    }

    /**
     * 检查是否为高置信度
     */
    public boolean isHighConfidence() {
        return confidenceScore != null && confidenceScore.compareTo(BigDecimal.valueOf(0.8)) >= 0;
    }

    /**
     * 检查是否已过期
     */
    public boolean isExpired() {
        return expiresAt != null && LocalDateTime.now().isAfter(expiresAt);
    }

    /**
     * 检查是否活跃
     */
    public boolean isActive() {
        return status != null && status.isActive() && !isExpired();
    }

}
