package com.chiron.smartlearnsystem.ai.entity;

import com.chiron.smartlearnsystem.ai.enums.RecommendationType;
import com.chiron.smartlearnsystem.ai.enums.RecommendationStatus;
import com.chiron.smartlearnsystem.common.core.enums.Difficulty;
import com.chiron.smartlearnsystem.common.database.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

/**
 * 学习推荐实体
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Entity
@Table(name = "learning_recommendations", indexes = {
    @Index(name = "idx_user_type", columnList = "user_id, recommendation_type"),
    @Index(name = "idx_status", columnList = "status"),
    @Index(name = "idx_confidence", columnList = "confidence_score"),
    @Index(name = "idx_created_at", columnList = "created_at"),
    @Index(name = "idx_expires_at", columnList = "expires_at")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LearningRecommendation extends BaseEntity {

    /**
     * 用户ID
     */
    @Column(name = "user_id", nullable = false)
    private Long userId;

    /**
     * 推荐类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "recommendation_type", nullable = false, length = 30)
    private RecommendationType recommendationType;

    /**
     * 推荐状态
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private RecommendationStatus status = RecommendationStatus.ACTIVE;

    /**
     * 推荐标题
     */
    @Column(nullable = false, length = 200)
    private String title;

    /**
     * 推荐描述
     */
    @Column(columnDefinition = "TEXT")
    private String description;

    /**
     * 推荐内容（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "recommendation_content", columnDefinition = "JSON")
    private String recommendationContent;

    /**
     * 科目代码
     */
    @Column(name = "subject_code", length = 20)
    private String subjectCode;

    /**
     * 推荐难度
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "recommended_difficulty", length = 20)
    private Difficulty recommendedDifficulty;

    /**
     * 置信度评分（0-1）
     */
    @Column(name = "confidence_score", nullable = false, precision = 5, scale = 4)
    private BigDecimal confidenceScore;

    /**
     * 优先级（1-10，10最高）
     */
    @Column(nullable = false)
    private Integer priority = 5;

    /**
     * 推荐算法版本
     */
    @Column(name = "algorithm_version", length = 20)
    private String algorithmVersion;

    /**
     * 推荐原因
     */
    @Column(name = "recommendation_reason", columnDefinition = "TEXT")
    private String recommendationReason;

    /**
     * 预期学习时长（分钟）
     */
    @Column(name = "estimated_duration")
    private Integer estimatedDuration;

    /**
     * 预期完成题目数
     */
    @Column(name = "estimated_questions")
    private Integer estimatedQuestions;

    /**
     * 目标知识点
     */
    @ElementCollection
    @CollectionTable(
        name = "recommendation_knowledge_points",
        joinColumns = @JoinColumn(name = "recommendation_id")
    )
    @Column(name = "knowledge_point")
    private Set<String> targetKnowledgePoints = new HashSet<>();

    /**
     * 推荐标签
     */
    @ElementCollection
    @CollectionTable(
        name = "recommendation_tags",
        joinColumns = @JoinColumn(name = "recommendation_id")
    )
    @Column(name = "tag")
    private Set<String> tags = new HashSet<>();

    /**
     * 是否已查看
     */
    @Column(name = "is_viewed", nullable = false)
    private Boolean isViewed = false;

    /**
     * 查看时间
     */
    @Column(name = "viewed_at")
    private LocalDateTime viewedAt;

    /**
     * 是否已采纳
     */
    @Column(name = "is_accepted", nullable = false)
    private Boolean isAccepted = false;

    /**
     * 采纳时间
     */
    @Column(name = "accepted_at")
    private LocalDateTime acceptedAt;

    /**
     * 用户反馈评分（1-5）
     */
    @Column(name = "user_feedback_score")
    private Integer userFeedbackScore;

    /**
     * 用户反馈内容
     */
    @Column(name = "user_feedback", columnDefinition = "TEXT")
    private String userFeedback;

    /**
     * 推荐过期时间
     */
    @Column(name = "expires_at")
    private LocalDateTime expiresAt;

    /**
     * 推荐来源数据（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "source_data", columnDefinition = "JSON")
    private String sourceData;

    /**
     * 个性化参数（JSON格式）
     */
    @Type(type = "json")
    @Column(name = "personalization_params", columnDefinition = "JSON")
    private String personalizationParams;

    /**
     * 标记为已查看
     */
    public void markAsViewed() {
        this.isViewed = true;
        this.viewedAt = LocalDateTime.now();
    }

    /**
     * 标记为已采纳
     */
    public void markAsAccepted() {
        this.isAccepted = true;
        this.acceptedAt = LocalDateTime.now();
        this.status = RecommendationStatus.ACCEPTED;
    }

    /**
     * 拒绝推荐
     */
    public void reject() {
        this.status = RecommendationStatus.REJECTED;
    }

    /**
     * 设置用户反馈
     */
    public void setUserFeedback(Integer score, String feedback) {
        this.userFeedbackScore = score;
        this.userFeedback = feedback;
    }

    /**
     * 检查是否过期
     */
    public boolean isExpired() {
        return expiresAt != null && LocalDateTime.now().isAfter(expiresAt);
    }

    /**
     * 检查是否高优先级
     */
    public boolean isHighPriority() {
        return priority >= 8;
    }

    /**
     * 检查是否高置信度
     */
    public boolean isHighConfidence() {
        return confidenceScore.compareTo(BigDecimal.valueOf(0.8)) >= 0;
    }

    /**
     * 添加知识点
     */
    public void addKnowledgePoint(String knowledgePoint) {
        this.targetKnowledgePoints.add(knowledgePoint);
    }

    /**
     * 添加标签
     */
    public void addTag(String tag) {
        this.tags.add(tag);
    }

}
