package com.chiron.smartlearnsystem.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 用户状态枚举
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum UserStatus {

    /**
     * 激活
     */
    ACTIVE("ACTIVE", "激活"),

    /**
     * 禁用
     */
    DISABLED("DISABLED", "禁用"),

    /**
     * 锁定
     */
    LOCKED("LOCKED", "锁定"),

    /**
     * 待激活
     */
    PENDING("PENDING", "待激活");

    private final String code;
    private final String description;

}
