package com.chiron.smartlearnsystem.common.core.constant;

/**
 * 通用常量
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
public class CommonConstants {

    /**
     * 成功标记
     */
    public static final Integer SUCCESS = 200;

    /**
     * 失败标记
     */
    public static final Integer FAIL = 500;

    /**
     * 默认页码
     */
    public static final Integer DEFAULT_PAGE = 1;

    /**
     * 默认页大小
     */
    public static final Integer DEFAULT_SIZE = 20;

    /**
     * 最大页大小
     */
    public static final Integer MAX_SIZE = 1000;

    /**
     * UTF-8 字符集
     */
    public static final String UTF8 = "UTF-8";

    /**
     * GBK 字符集
     */
    public static final String GBK = "GBK";

    /**
     * 默认语言
     */
    public static final String DEFAULT_LANGUAGE = "zh-CN";

    /**
     * 默认时区
     */
    public static final String DEFAULT_TIMEZONE = "Asia/Shanghai";

    /**
     * 请求头 - 用户ID
     */
    public static final String HEADER_USER_ID = "X-User-Id";

    /**
     * 请求头 - 用户名
     */
    public static final String HEADER_USERNAME = "X-Username";

    /**
     * 请求头 - 授权
     */
    public static final String HEADER_AUTHORIZATION = "Authorization";

    /**
     * 请求头 - 请求ID
     */
    public static final String HEADER_REQUEST_ID = "X-Request-Id";

    /**
     * JWT Token前缀
     */
    public static final String TOKEN_PREFIX = "Bearer ";

    /**
     * 缓存前缀
     */
    public static final String CACHE_PREFIX = "smartlearn:";

    /**
     * 用户缓存前缀
     */
    public static final String USER_CACHE_PREFIX = CACHE_PREFIX + "user:";

    /**
     * 题目缓存前缀
     */
    public static final String QUESTION_CACHE_PREFIX = CACHE_PREFIX + "question:";

    /**
     * 学习记录缓存前缀
     */
    public static final String STUDY_CACHE_PREFIX = CACHE_PREFIX + "study:";

    /**
     * 默认密码
     */
    public static final String DEFAULT_PASSWORD = "123456";

    /**
     * 系统用户ID
     */
    public static final Long SYSTEM_USER_ID = 0L;

    /**
     * 系统用户名
     */
    public static final String SYSTEM_USERNAME = "system";

    /**
     * 删除标记 - 已删除
     */
    public static final Integer DELETED = 1;

    /**
     * 删除标记 - 未删除
     */
    public static final Integer NOT_DELETED = 0;

    /**
     * 启用状态
     */
    public static final Integer ENABLED = 1;

    /**
     * 禁用状态
     */
    public static final Integer DISABLED = 0;

    /**
     * 是
     */
    public static final String YES = "Y";

    /**
     * 否
     */
    public static final String NO = "N";

    /**
     * 日期格式
     */
    public static final String DATE_FORMAT = "yyyy-MM-dd";

    /**
     * 时间格式
     */
    public static final String DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    /**
     * 时间戳格式
     */
    public static final String TIMESTAMP_FORMAT = "yyyy-MM-dd HH:mm:ss.SSS";

}
