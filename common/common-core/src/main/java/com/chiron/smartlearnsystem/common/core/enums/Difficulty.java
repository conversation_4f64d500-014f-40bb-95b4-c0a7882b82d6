package com.chiron.smartlearnsystem.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 难度枚举
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum Difficulty {

    /**
     * 简单
     */
    EASY("EASY", "简单", 1),

    /**
     * 中等
     */
    MEDIUM("MEDIUM", "中等", 2),

    /**
     * 困难
     */
    HARD("HARD", "困难", 3),

    /**
     * 极难
     */
    EXPERT("EXPERT", "极难", 4);

    private final String code;
    private final String description;
    private final Integer level;

    /**
     * 根据级别获取难度
     */
    public static Difficulty getByLevel(Integer level) {
        for (Difficulty difficulty : values()) {
            if (difficulty.getLevel().equals(level)) {
                return difficulty;
            }
        }
        return MEDIUM;
    }

}
